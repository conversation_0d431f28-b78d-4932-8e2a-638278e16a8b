#!/bin/bash

# Setup script for Perplexity AI MCP Server with Cline VS Code Extension

echo "🚀 Setting up Perplexity AI MCP Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are installed"

# Install the Perplexity AI MCP server package globally
echo "📦 Installing Perplexity AI MCP server..."
npm install -g @perplexity-ai/mcp-server

if [ $? -eq 0 ]; then
    echo "✅ Perplexity AI MCP server installed successfully"
else
    echo "❌ Failed to install Perplexity AI MCP server"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create one with your Perplexity API key."
    exit 1
fi

echo "✅ .env file found"

# Validate MCP configuration
if [ -f ".roo/mcp.json" ]; then
    echo "✅ MCP configuration found at .roo/mcp.json"
    
    # Check if perplexity-ai server is configured
    if grep -q "perplexity-ai" ".roo/mcp.json"; then
        echo "✅ Perplexity AI server is configured in MCP"
    else
        echo "❌ Perplexity AI server not found in MCP configuration"
        exit 1
    fi
else
    echo "❌ MCP configuration file not found at .roo/mcp.json"
    exit 1
fi

echo ""
echo "🎉 Setup complete! Next steps:"
echo ""
echo "1. 🔑 IMPORTANT: Regenerate your Perplexity API key (the one you shared is compromised)"
echo "   - Go to https://www.perplexity.ai/settings/api"
echo "   - Generate a new API key"
echo ""
echo "2. 📝 Update your .env file with the new API key:"
echo "   PERPLEXITY_API_KEY=your_new_api_key_here"
echo ""
echo "3. 🔄 Restart VS Code to load the new MCP server"
echo ""
echo "4. 🧪 Test the connection in Cline by using Perplexity AI features"
echo ""
echo "📋 Your MCP servers are now configured:"
echo "   - task-master-ai (multi-provider including Perplexity)"
echo "   - context7 (context management)"
echo "   - perplexity-ai (dedicated Perplexity server)"
echo ""
echo "🔧 Configuration files:"
echo "   - MCP config: .roo/mcp.json"
echo "   - Environment: .env"
echo ""
