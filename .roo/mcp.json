{"mcpServers": {"task-master-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "ANTHROPIC_API_KEY_HERE", "PERPLEXITY_API_KEY": "YOUR_NEW_PERPLEXITY_API_KEY_HERE", "OPENAI_API_KEY": "OPENAI_API_KEY_HERE", "GOOGLE_API_KEY": "AIzaSyBeySlH3SMxKFrDaDiZMq1qRjXFGJRS5TY", "XAI_API_KEY": "XAI_API_KEY_HERE", "OPENROUTER_API_KEY": "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", "MISTRAL_API_KEY": "MISTRAL_API_KEY_HERE", "AZURE_OPENAI_API_KEY": "AZURE_OPENAI_API_KEY_HERE", "OLLAMA_API_KEY": "OLLAMA_API_KEY_HERE"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "perplexity-ai": {"command": "npx", "args": ["-y", "@perplexity-ai/mcp-server"], "env": {"PERPLEXITY_API_KEY": "YOUR_NEW_PERPLEXITY_API_KEY_HERE"}}}}