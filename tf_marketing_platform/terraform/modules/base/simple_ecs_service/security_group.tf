resource "aws_security_group" "ecs_tasks" {
  name        = "ecs-${var.env}-${var.name}-containers"
  description = "ecs-${var.env}-${var.name}-containers"
  vpc_id      = var.vpc_id

  dynamic "ingress" {
    for_each = var.cidrs
    content {
      from_port   = var.container_port
      to_port     = var.container_port
      protocol    = "tcp"
      cidr_blocks = [ingress.value]
    }
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name  = "ecs-${var.env}-${var.name}-tasks",
    "App" = "${var.name}"
  }
}
