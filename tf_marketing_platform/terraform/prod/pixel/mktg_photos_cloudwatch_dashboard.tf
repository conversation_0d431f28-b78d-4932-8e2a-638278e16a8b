resource "aws_cloudwatch_dashboard" "mktg-photos" {
  dashboard_name = "mktg-photos"
  dashboard_body = jsonencode(
    {
      widgets = [
        {
          height = 1
          width = 24
          y = 0
          x = 0
          type = "text"
          properties = {
            markdown = "# Photos Stream"
          }
        },
        {
          height = 5
          width = 9
          y = 1
          x = 0
          type = "metric"
          properties = {
            end = "P0D"
            metrics = [
              [
                {
                  expression = "m10/1024/1024/PERIOD(m10)"
                  id = "e2"
                  label = "PutRecords - sum (MB/s)"
                  region = "us-east-1"
                  visible = false
                }
              ],
              [
                "AWS/Kinesis",
                "PutRecords.Bytes",
                "StreamName",
                "tlprod_scoreAllMomentsV2_update_stream",
                {
                  id = "m10"
                  period = 3600
                  stat = "Sum"
                }
              ]
            ]
            period = 300
            region = "us-east-1"
            stacked = false
            start = "-PT336H"
            stat = "Average"
            timezone = "UTC"
            title = "MB"
            view = "timeSeries"
            yAxis = {
              left = {
                min = 0
                showUnits = false
              }
            }
          }
        },
        {
          height = 5
          width = 7
          y = 1
          x = 9
          type = "metric"
          properties = {
            metrics = [
              [
                {
                  expression = "m10/1024/1024/PERIOD(m10)"
                  id = "e2"
                  label = "PutRecords - sum (MB/s)"
                  region = "us-east-1"
                  visible = false
                }
              ],
              [
                "AWS/Kinesis",
                "PutRecords.Bytes",
                "StreamName",
                "tlprod_scoreAllMomentsV2_update_stream",
                {
                  id = "m10"
                  period = 3600
                  visible = false
                }
              ],
              [
                ".",
                "PutRecords.TotalRecords",
                ".",
                ".",
                {
                  id = "m1"
                  period = 60
                }
              ]
            ]
            period = 300
            region = "us-east-1"
            stacked = false
            stat = "Sum"
            timezone = "UTC"
            title = "Put Records"
            view = "timeSeries"
            yAxis = {
              left = {
                min = 0
                showUnits = false
              }
            }
          }
        },
        {
          height = 5
          width = 8
          y = 1
          x = 16
          type = "metric"
          properties = {
            metrics = [
              [
                "AWS/Kinesis",
                "GetRecords.Records",
                "StreamName",
                "tlprod_scoreAllMomentsV2_update_stream",
                {
                  id = "m3"
                  visible = true
                }
              ]
            ]
            period = 3600
            region = "us-east-1"
            stacked = false
            stat = "Sum"
            timezone = "UTC"
            title = "GetRecords - sum (Count)"
            view = "timeSeries"
            yAxis = {
              left = {
                min = 0
              }
            }
          }
        },
        {
          height = 1
          width = 24
          y = 6
          x = 0
          type = "text"
          properties = {
            markdown = "# **Photos 2.0**"
          }
        },
        {
          height = 6
          width = 13
          y = 7
          x = 0
          type = "metric"
          properties = {
            end = "P0D"
            metrics = [
              [
                "mktg-photos",
                "number_of_events_processed_daily_silver",
                {
                  region = "us-east-1"
                }
              ]
            ]
            period = 86400
            region = "us-east-1"
            stacked = false
            start = "-PT1008H"
            stat = "Average"
            title = "number_of_events_processed_daily_silver"
            view = "timeSeries"
          }
        },
        {
          height = 6
          width = 11
          y = 7
          x = 13
          type = "metric"
          properties = {
            end = "P0D"
            metrics = [
              [
                "mktg-photos",
                "number_of_events_processed_daily_bronze",
                {
                  region = "us-east-1"
                }
              ]
            ]
            period = 86400
            region = "us-east-1"
            stacked = false
            start = "-PT672H"
            stat = "Average"
            title = "number_of_events_processed_daily_bronze"
            view = "timeSeries"
          }
        },
        {
          height = 6
          width = 24
          y = 13
          x = 0
          type = "metric"
          properties = {
            end = "P0D"
            metrics = [
              [
                "trip_returners",
                "number_of_events_processed_bronze",
                {
                  region = "us-east-1"
                  visible = false
                }
              ],
              [
                ".",
                "number_of_events_processed_silver",
                {
                  region = "us-east-1"
                  visible = false
                }
              ],
              [
                "mktg-photos",
                "number_of_deleted_photos_daily",
                {
                  color = "#1f77b4"
                  region = "us-east-1"
                }
              ]
            ]
            period = 86400
            region = "us-east-1"
            stacked = false
            start = "-PT672H"
            stat = "Average"
            title = "Deleted Photos"
            view = "timeSeries"
          }
        },
        {
          height = 1
          width = 24
          y = 19
          x = 0
          type = "text"
          properties = {
            markdown = "# **Aurora Photos 3.0**"
          }
        },
        {
          height = 1
          width = 12
          y = 20
          x = 0
          type = "text"
          properties = {
            markdown = "### Media"
          }
        },
        {
          height = 1
          width = 12
          y = 20
          x = 12
          type = "text"
          properties = {
            markdown = "### APC"
          }
        },
        {
          height = 6
          width = 6
          y = 21
          x = 0
          type = "metric"
          properties = {
            end = "P0D"
            metrics = [
              [
                "aurora_memories",
                "number_of_events_processed_bronze_media",
                {
                  region = "us-east-1"
                }
              ]
            ]
            period = 86400
            region = "us-east-1"
            stacked = false
            start = "-PT168H"
            stat = "Maximum"
            title = "Bronze Media"
            view = "timeSeries"
          }
        },
        {
          height = 6
          width = 6
          y = 21
          x = 6
          type = "metric"
          properties = {
            end = "P0D"
            metrics = [
              [
                "aurora_memories",
                "number_of_events_processed_silver_media",
                {
                  color = "#9467bd"
                  region = "us-east-1"
                }
              ]
            ]
            period = 86400
            region = "us-east-1"
            stacked = false
            start = "-PT168H"
            stat = "Maximum"
            title = "Silver Media"
            view = "timeSeries"
          }
        },
        {
          height = 6
          width = 6
          y = 21
          x = 12
          type = "metric"
          properties = {
            end = "P0D"
            metrics = [
              [
                "aurora_memories",
                "number_of_events_processed_bronze_apc",
                {
                  region = "us-east-1"
                }
              ]
            ]
            period = 86400
            region = "us-east-1"
            stacked = false
            start = "-PT168H"
            stat = "Maximum"
            title = "Bronze APC"
            view = "timeSeries"
          }
        },
        {
          height = 6
          width = 6
          y = 21
          x = 18
          type = "metric"
          properties = {
            end = "P0D"
            metrics = [
              [
                "aurora_memories",
                "number_of_events_processed_silver_apc",
                {
                  color = "#9467bd"
                  region = "us-east-1"
                }
              ]
            ]
            period = 86400
            region = "us-east-1"
            stacked = false
            start = "-PT168H"
            stat = "Maximum"
            title = "Silver APC"
            view = "timeSeries"
          }
        },
        {
          height = 6
          width = 12
          y = 27
          x = 0
          type = "metric"
          properties = {
            end = "P0D"
            metrics = [
              [
                "aurora_memories",
                "number_of_events_processed_gold",
                {
                  color = "#ff7f0e"
                }
              ]
            ]
            period = 86400
            region = "us-east-1"
            stacked = false
            start = "-PT168H"
            stat = "Average"
            title = "**Gold**"
            view = "timeSeries"
          }
        },
        {
          height = 6
          width = 12
          y = 27
          x = 12
          type = "metric"
          properties = {
            end = "P0D"
            metrics = [
              [
                "aurora_memories",
                "number_of_events_processed_deleted",
                {
                  color = "#d62728"
                }
              ]
            ]
            period = 86400
            region = "us-east-1"
            stacked = false
            start = "-PT168H"
            stat = "Average"
            title = "Deleted Photos"
            view = "timeSeries"
          }
        },
        {
          height = 1
          width = 24
          y = 33
          x = 0
          type = "text"
          properties = {
            markdown = "## **Aurora Photos - DQ Checks**"
          }
        },
        {
          height = 6
          width = 12
          y = 37
          x = 0
          type = "metric"
          properties = {
            metrics = [
              [
                "aurora_memories_dq_check",
                "missing_apc_data",
                {
                  region = "us-east-1"
                }
              ]
            ]
            view = "timeSeries"
            stacked = false
            region = "us-east-1"
            period = 86400
            title = "Missing APC Data"
            start = "-PT168H"
            end = "P0D"
            stat = "Average"
          }
        },
        {
          height = 6
          width = 12
          y = 37
          x = 12
          type = "metric"
          properties = {
            metrics = [
              [
                "aurora_memories_dq_check",
                "missing_media_date",
                {
                  color = "#ff7f0e"
                }
              ]
            ]
            view = "timeSeries"
            stacked = false
            region = "us-east-1"
            period = 86400
            title = "Missing Media Data"
            start = "-PT168H"
            end = "P0D"
            stat = "Average"
          }
        },
        {
          height = 6
          width = 12
          y = 43
          x = 0
          type = "metric"
          properties = {
            metrics = [
              ["aurora_memories_dq_check", "future_date_taken"],
              [".", "future_date_uploaded"]
            ]
            view = "timeSeries"
            stacked = false
            region = "us-east-1"
            start = "-PT168H"
            end = "P0D"
            period = 86400
            stat = "Average"
            title = "Future dates"
          }
        },
        {
          height = 6
          width = 12
          y = 43
          x = 12
          type = "metric"
          properties = {
            metrics = [
              ["aurora_memories_dq_check", "null_or_zero_categories"],
              [".", "null_or_zero_faces"],
              [".", "null_or_zero_rank"]
            ]
            view = "timeSeries"
            stacked = false
            region = "us-east-1"
            start = "-PT168H"
            end = "P0D"
            period = 86400
            stat = "Average"
            title = "Missing Ranking Data"
          }
        },
        {
          height = 3
          width = 4
          y = 34
          x = 0
          type = "metric"
          properties = {
            metrics = [
              ["aurora_memories_dq_check", "missing_apc_data"]
            ]
            sparkline = false
            view = "singleValue"
            region = "us-east-1"
            period = 86400
            stat = "Average"
            title = "Missing APC Data"
            start = "-PT24H"
            end = "P0D"
          }
        },
        {
          height = 3
          width = 4
          y = 34
          x = 4
          type = "metric"
          properties = {
            metrics = [
              ["aurora_memories_dq_check", "missing_media_date"]
            ]
            sparkline = false
            view = "singleValue"
            region = "us-east-1"
            period = 86400
            stat = "Average"
            title = "Missing Media Data"
            start = "-PT24H"
            end = "P0D"
          }
        },
        {
          height = 3
          width = 4
          y = 34
          x = 8
          type = "metric"
          properties = {
            metrics = [
              ["aurora_memories_dq_check", "null_or_zero_categories"]
            ]
            sparkline = false
            view = "singleValue"
            region = "us-east-1"
            period = 86400
            stat = "Average"
            title = "Empty Categories"
            start = "-PT24H"
            end = "P0D"
          }
        },
        {
          height = 3
          width = 4
          y = 34
          x = 12
          type = "metric"
          properties = {
            metrics = [
              ["aurora_memories_dq_check", "null_or_zero_faces"]
            ]
            sparkline = false
            view = "singleValue"
            region = "us-east-1"
            period = 86400
            stat = "Average"
            title = "Empty Faces"
            start = "-PT24H"
            end = "P0D"
          }
        },
        {
          height = 3
          width = 4
          y = 34
          x = 16
          type = "metric"
          properties = {
            metrics = [
              ["aurora_memories_dq_check", "null_or_zero_rank"]
            ]
            sparkline = false
            view = "singleValue"
            region = "us-east-1"
            period = 86400
            stat = "Average"
            title = "Empty Rank"
            start = "-PT24H"
            end = "P0D"
          }
        },
        {
          height = 3
          width = 4
          y = 34
          x = 20
          type = "metric"
          properties = {
            metrics = [
              ["aurora_memories_dq_check", "future_date_taken"]
            ]
            sparkline = false
            view = "singleValue"
            region = "us-east-1"
            period = 86400
            stat = "Average"
            title = "Future Date"
            start = "-PT24H"
            end = "P0D"
          }
        }
      ]
    }
  )
}