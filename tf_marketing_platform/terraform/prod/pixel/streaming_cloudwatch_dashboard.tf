resource "aws_cloudwatch_dashboard" "streaming" {
  dashboard_name = "streaming"
  dashboard_body = jsonencode(
    {
    "widgets": [
        {
            "height": 1,
            "width": 24,
            "y": 0,
            "x": 0,
            "type": "text",
            "properties": {
                "markdown": "# Pixel - Incoming events"
            }
        },
        {
            "height": 6,
            "width": 12,
            "y": 1,
            "x": 0,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Lambda", "Invocations", "FunctionName", "beacon-pixle-prod", { "label": "BeaconPixel - Overall Lambda Invocations", "region": "us-east-1" } ],
                    [ "Pixel", "BeaconPixel-MobileCount", { "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-CartCount", { "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-WebCount", { "color": "#8c564b", "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-ProspectsCount", { "region": "us-east-1", "visible": false } ]
                ],
                "period": 60,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "BeaconPixel- Overall Lambda Invocations",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 12,
            "y": 1,
            "x": 12,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Lambda", "Invocations", "FunctionName", "beacon-pixle-prod", { "label": "BeaconPixel - Overall Lambda Invocations", "region": "us-east-1", "visible": false } ],
                    [ "Pixel", "BeaconPixel-MobileCount", { "region": "us-east-1" } ],
                    [ ".", "BeaconPixel-CartCount", { "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-WebCount", { "color": "#8c564b", "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-ProspectsCount", { "region": "us-east-1", "visible": false } ]
                ],
                "period": 60,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "BeaconPixel- MobileCount",
                "view": "timeSeries"
            }
        },
        {
            "height": 1,
            "width": 24,
            "y": 13,
            "x": 0,
            "type": "text",
            "properties": {
                "markdown": "# Pixel - Incoming Mobile Events"
            }
        },
        {
            "height": 6,
            "width": 8,
            "y": 14,
            "x": 0,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "Pixel", "BeaconPixel-MobileCount", { "id": "all" } ],
                    [ { "expression": "SUM([all, -android])", "id": "ios", "label": "BeaconPixel-IOSCount", "period": 60, "region": "us-east-1" } ],
                    [ "Pixel", "BeaconPixel-AndroidCount", { "id": "android" } ]
                ],
                "period": 60,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "BeaconPixel- Incoming Mobile Events",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 8,
            "y": 14,
            "x": 8,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "Pixel", "BeaconPixel-SuccessfulMobileCalls", { "region": "us-east-1" } ],
                    [ ".", "BeaconPixel-SuccessfulCartCalls", { "region": "us-east-1" } ],
                    [ ".", "BeaconPixel-SuccessfulWebCalls", { "region": "us-east-1" } ],
                    [ ".", "BeaconPixel-SuccessfulProspectCalls", { "region": "us-east-1" } ]
                ],
                "period": 60,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "BeaconPixel - Successful Invocations",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 8,
            "y": 14,
            "x": 16,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "Pixel", "BeaconPixel-FailedMobileCalls" ],
                    [ ".", "BeaconPixel-FailedCartCalls" ],
                    [ ".", "BeaconPixel-FailedWebCalls" ],
                    [ ".", "BeaconPixel-FailedProspectCalls" ]
                ],
                "period": 60,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "BeaconPixel - Failed Invocations",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 8,
            "y": 20,
            "x": 0,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ { "expression": "m7/1024/1024/PERIOD(m7)", "id": "e1", "label": "PutRecord - sum (MB/s)", "region": "us-east-1", "visible": false } ],
                    [ "AWS/Kinesis", "PutRecord.Bytes", "StreamName", "mktg-i1-incoming-events", { "id": "m7", "period": 3600, "stat": "Sum" } ]
                ],
                "period": 300,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Average",
                "title": "MB - mktg-i1-incoming-events (Kinesis)",
                "view": "timeSeries",
                "yAxis": {
                    "left": {
                        "min": 0,
                        "showUnits": false
                    }
                }
            }
        },
        {
            "height": 6,
            "width": 8,
            "y": 20,
            "x": 8,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Kinesis", "IncomingRecords", "StreamName", "mktg-i1-incoming-events", { "color": "#2ca02c" } ]
                ],
                "period": 3600,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "Put Records - mktg-i1-incoming-events (Kinesis)",
                "view": "timeSeries",
                "yAxis": {
                    "left": {
                        "min": 0,
                        "showUnits": false
                    }
                }
            }
        },
        {
            "height": 6,
            "width": 8,
            "y": 20,
            "x": 16,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Kinesis", "GetRecords.Records", "StreamName", "mktg-i1-incoming-events" ]
                ],
                "period": 3600,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "GetRecords - sum (Count) - mktg-i1-incoming-events (Kinesis)",
                "view": "timeSeries",
                "yAxis": {
                    "left": {
                        "min": 0,
                        "showUnits": false
                    }
                }
            }
        },
        {
            "height": 6,
            "width": 12,
            "y": 26,
            "x": 0,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Kinesis", "PutRecord.Success", "StreamName", "mktg-i1-incoming-events" ]
                ],
                "region": "us-east-1",
                "stacked": false,
                "title": "PutRecord success - average (Ratio)",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 12,
            "y": 26,
            "x": 12,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Kinesis", "GetRecords.Success", "StreamName", "mktg-i1-incoming-events", { "label": "GetRecords.Success", "region": "us-east-1" } ]
                ],
                "period": 300,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Average",
                "title": "GetRecords success - average (Ratio)",
                "view": "timeSeries"
            }
        },
        {
            "height": 1,
            "width": 24,
            "y": 32,
            "x": 0,
            "type": "text",
            "properties": {
                "markdown": "# Events sent to AIQ - action-iq-events-stream-prod\n"
            }
        },
        {
            "height": 6,
            "width": 9,
            "y": 33,
            "x": 0,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "Pixel", "EchoConsumer-OrderCompletionCount", { "region": "us-east-1" } ],
                    [ ".", "EchoConsumer-OrderCompletionTotals", { "label": "EchoConsumer-OrderCompletionTotals ($)", "region": "us-east-1" } ]
                ],
                "period": 60,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "OrderCompletions (WEB)",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 7,
            "y": 33,
            "x": 9,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "Pixel", "delta_time", "ConsumerName", "PlatformConsumer", { "stat": "Maximum" } ],
                    [ "Pixel", "delta_time", "ConsumerName", "PlatformConsumer", { "stat": "p74" } ],
                    [ "Pixel", "delta_time", "ConsumerName", "PlatformConsumer", { "stat": "p85" } ],
                    [ "Pixel", "delta_time", "ConsumerName", "PlatformConsumer" ]
                ],
                "period": 300,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Average",
                "title": "PlatformConsumer iterator - delta time",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 8,
            "y": 33,
            "x": 16,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "Pixel", "AbandonBrowse-WindowSession" ],
                    [ ".", "SavedProject-WindowSession" ],
                    [ ".", "AbandonCart-WindowSession" ],
                    [ ".", "PostPurchase-WindowSession" ]
                ],
                "period": 300,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "Window Sessions Size",
                "view": "timeSeries"
            }
        },
        {
            "height": 1,
            "width": 24,
            "y": 39,
            "x": 0,
            "type": "text",
            "properties": {
                "markdown": "# Events sent to AIQ - action-iq-events-stream-prod\n"
            }
        },
        {
            "height": 6,
            "width": 6,
            "y": 40,
            "x": 0,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Lambda", "Duration", "FunctionName", "action-iq-events-stream-prod", { "label": "Duration - Min", "stat": "Minimum" } ],
                    [ "...", { "label": "Duration - AVG" } ],
                    [ "...", { "label": "Duration - Max", "stat": "Maximum" } ]
                ],
                "period": 60,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Average",
                "title": "Duration",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 6,
            "y": 40,
            "x": 6,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Lambda", "Errors", "FunctionName", "action-iq-events-stream-prod", { "color": "#d13212", "id": "errors", "stat": "Sum" } ],
                    [ ".", "Invocations", ".", ".", { "id": "invocations", "stat": "Sum", "visible": false } ],
                    [ { "expression": "100 - 100 * errors / MAX([errors, invocations])", "id": "availability", "label": "Success rate (%)", "region": "us-east-1", "yAxis": "right" } ]
                ],
                "period": 60,
                "region": "us-east-1",
                "stacked": false,
                "title": "Error count and success rate (%)",
                "view": "timeSeries",
                "yAxis": {
                    "right": {
                        "max": 100
                    }
                }
            }
        },
        {
            "height": 6,
            "width": 6,
            "y": 40,
            "x": 12,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Lambda", "Invocations", "FunctionName", "action-iq-events-stream-prod", "Resource", "action-iq-events-stream-prod", { "id": "m1" } ]
                ],
                "period": 60,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 6,
            "y": 40,
            "x": 18,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ { "expression": "FLOOR(100*(abandon_browse_success/abandon_browse_all))", "id": "abandon_browse_success_rate", "label": "ActionIQ-AbandonBrowse-SuccessRate" } ],
                    [ { "expression": "FLOOR (100*(abandon_cart_success/abandon_cart_all))", "id": "abandon_cart_success_rate", "label": "ActionIQ-AbandonCart-SuccessRate" } ],
                    [ { "expression": "FLOOR (100*(post_purchase_success/post_purchase_all))", "id": "post_purchase_success_rate", "label": "ActionIQ-PostPurchase-SuccessRate" } ],
                    [ "Pixel", "ActionIQ-AbandonBrowse-Invocations", { "id": "abandon_browse_all", "visible": false } ],
                    [ ".", "ActionIQ-AbandonBrowse-Success", { "id": "abandon_browse_success", "visible": false } ],
                    [ ".", "ActionIQ-AbandonCart-Invocations", { "id": "abandon_cart_all", "visible": false } ],
                    [ ".", "ActionIQ-AbandonCart-Success", { "id": "abandon_cart_success", "visible": false } ],
                    [ ".", "ActionIQ-PostPurchase-Invocations", { "id": "post_purchase_all", "visible": false } ],
                    [ ".", "ActionIQ-PostPurchase-Success", { "id": "post_purchase_success", "visible": false } ]
                ],
                "period": 300,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "ActionIQ-SuccessRates",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 8,
            "y": 7,
            "x": 0,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Lambda", "Invocations", "FunctionName", "beacon-pixle-prod", { "label": "BeaconPixel - Overall Lambda Invocations", "region": "us-east-1", "visible": false } ],
                    [ "Pixel", "BeaconPixel-MobileCount", { "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-CartCount", { "region": "us-east-1" } ],
                    [ ".", "BeaconPixel-WebCount", { "color": "#8c564b", "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-ProspectsCount", { "region": "us-east-1", "visible": false } ]
                ],
                "period": 60,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "BeaconPixel- CartCount",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 8,
            "y": 7,
            "x": 8,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Lambda", "Invocations", "FunctionName", "beacon-pixle-prod", { "label": "BeaconPixel - Overall Lambda Invocations", "region": "us-east-1", "visible": false } ],
                    [ "Pixel", "BeaconPixel-MobileCount", { "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-CartCount", { "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-WebCount", { "color": "#8c564b", "region": "us-east-1" } ],
                    [ ".", "BeaconPixel-ProspectsCount", { "region": "us-east-1", "visible": false } ]
                ],
                "period": 60,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "BeaconPixel- WebCount",
                "view": "timeSeries"
            }
        },
        {
            "height": 6,
            "width": 8,
            "y": 7,
            "x": 16,
            "type": "metric",
            "properties": {
                "metrics": [
                    [ "AWS/Lambda", "Invocations", "FunctionName", "beacon-pixle-prod", { "label": "BeaconPixel - Overall Lambda Invocations", "region": "us-east-1", "visible": false } ],
                    [ "Pixel", "BeaconPixel-MobileCount", { "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-CartCount", { "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-WebCount", { "color": "#8c564b", "region": "us-east-1", "visible": false } ],
                    [ ".", "BeaconPixel-ProspectsCount", { "region": "us-east-1" } ]
                ],
                "period": 3600,
                "region": "us-east-1",
                "stacked": false,
                "stat": "Sum",
                "title": "BeaconPixel- ProspectsCount",
                "view": "timeSeries"
            }
        }
    ]
}
  )
}