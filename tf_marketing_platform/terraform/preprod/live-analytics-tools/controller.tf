variable "name" {
  type        = string
  default     = "controller"
}

# Custom IAM Policy for DynamoDB - Least Privilege Access
resource "aws_iam_policy" "controller_dynamodb_policy" {
  name        = "${var.env}-${var.name}-live-analytics-tools-dynamodb-policy"
  description = "Least privilege DynamoDB access for controller live analytics tools"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:PutItem",
          "dynamodb:DeleteItem",
          "dynamodb:Scan"
        ]
        Resource = "arn:aws:dynamodb:${var.region}:463166080413:table/${var.env}-${var.name}-live-analytics-tools"
      }
    ]
  })

  tags = {
    Name        = "${var.env}-${var.name}-live-analytics-tools-dynamodb-policy"
    Environment = var.env
    Service     = "live-analytics-tools"
    Component   = "controller"
  }
}

# Custom IAM Policy for ECS - Least Privilege Access
resource "aws_iam_policy" "controller_ecs_policy" {
  name        = "${var.env}-${var.name}-live-analytics-tools-ecs-policy"
  description = "Least privilege ECS access for controller live analytics tools"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecs:UpdateService",
          "ecs:ListTasks",
          "ecs:StopTask",
          "ecs:DescribeTasks"
        ]
        Resource = [
          "arn:aws:ecs:${var.region}:463166080413:cluster/ecs-${var.env}-cluster-v1",
          "arn:aws:ecs:${var.region}:463166080413:service/ecs-${var.env}-cluster-v1/ecs-${var.env}-RecentlyViewed-live-analytics-tools",
          "arn:aws:ecs:${var.region}:463166080413:service/ecs-${var.env}-cluster-v1/ecs-${var.env}-ProspectsCollect-live-analytics-tools",
          "arn:aws:ecs:${var.region}:463166080413:service/ecs-${var.env}-cluster-v1/ecs-${var.env}-PlatformConsumer-live-analytics-tools",
          "arn:aws:ecs:${var.region}:463166080413:task/ecs-${var.env}-cluster-v1/*"
        ]
      }
    ]
  })

  tags = {
    Name        = "${var.env}-${var.name}-live-analytics-tools-ecs-policy"
    Environment = var.env
    Service     = "live-analytics-tools"
    Component   = "controller"
  }
}

# Custom IAM Policy for CloudWatch Logs - Least Privilege Access
resource "aws_iam_policy" "controller_cloudwatch_policy" {
  name        = "${var.env}-${var.name}-live-analytics-tools-cloudwatch-policy"
  description = "Least privilege CloudWatch Logs access for controller live analytics tools"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogGroups",
          "logs:DescribeLogStreams"
        ]
        Resource = [
          "arn:aws:logs:${var.region}:463166080413:log-group:ecs-${var.env}-${var.name}-live-analytics-tools",
          "arn:aws:logs:${var.region}:463166080413:log-group:ecs-${var.env}-${var.name}-live-analytics-tools:*"
        ]
      }
    ]
  })

  tags = {
    Name        = "${var.env}-${var.name}-live-analytics-tools-cloudwatch-policy"
    Environment = var.env
    Service     = "live-analytics-tools"
    Component   = "controller"
  }
}

module "controller-live-analytics-tools" {
    source  = "../../modules/base/simple_ecs_service"
    name = "${var.name}-live-analytics-tools"
    cluster_name = var.cluster_name
    env = "${var.env}"
    vpc_id = "vpc-0d28db63f078ecbd4"
    cidrs = ["1.1.1.1/32"]
    cpu = 512
    memory = 1024
    desired_count = 1
    policies = [
                  "arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess",
                  "arn:aws:iam::aws:policy/CloudWatchFullAccess",
                  "arn:aws:iam::aws:policy/AmazonECS_FullAccess",
                  "arn:aws:iam::aws:policy/AmazonKinesisFullAccess",
                  "arn:aws:iam::463166080413:policy/MSK-Read-Policy"
               ]
    ecs_task_execution_role = "arn:aws:iam::463166080413:role/ecsTaskExecutionRole"
    service_subnets = ["subnet-0c4e52dd218d11b06", "subnet-097e93dc6ffb310bd"]
    task_definition = jsonencode(
    [
        {
            name      = "${var.name}-live-analytics-tools"
            image     = "463166080413.dkr.ecr.us-east-1.amazonaws.com/live-analytics-tools:latest"
            essential = true
            environment = [
                            {"name": "APP", "value": "live-analytics-tools-controller"},
                            {"name": "STAGE", "value": "${var.env}"}
                          ]
            "command": [
                          "python",
                          "live_analytics_tool/controller.py"
                       ],
            "logConfiguration" : {
              "logDriver" : "awslogs",
              "options" : {
                "awslogs-group" : "ecs-${var.env}-${var.name}-live-analytics-tools",
                "awslogs-stream-prefix" : "ecs-${var.env}-${var.name}-live-analytics-tools"
                "awslogs-region" : "${var.region}"
              }
            }
        }
    ])
    container_port = 8080
    container_name = "${var.name}-live-analytics-tools"
}
