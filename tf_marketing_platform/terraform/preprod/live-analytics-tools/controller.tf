variable "name" {
  type        = string
  default     = "controller"
}

module "controller-live-analytics-tools" {
    source  = "../../modules/base/simple_ecs_service"
    name = "${var.name}-live-analytics-tools"
    cluster_name = var.cluster_name
    env = "${var.env}"
    vpc_id = "vpc-0d28db63f078ecbd4"
    cidrs = ["1.1.1.1/32"]
    cpu = 512
    memory = 1024
    desired_count = 1
    policies = [
                  "arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess",
                  "arn:aws:iam::aws:policy/CloudWatchFullAccess",
                  "arn:aws:iam::aws:policy/AmazonECS_FullAccess",
                  "arn:aws:iam::aws:policy/AmazonKinesisFullAccess",
                  "arn:aws:iam::463166080413:policy/MSK-Read-Policy"
               ]
    ecs_task_execution_role = "arn:aws:iam::463166080413:role/ecsTaskExecutionRole"
    service_subnets = ["subnet-0c4e52dd218d11b06", "subnet-097e93dc6ffb310bd"]
    task_definition = jsonencode(
    [
        {
            name      = "${var.name}-live-analytics-tools"
            image     = "463166080413.dkr.ecr.us-east-1.amazonaws.com/live-analytics-tools:latest"
            essential = true
            environment = [
                            {"name": "APP", "value": "live-analytics-tools-controller"},
                            {"name": "STAGE", "value": "${var.env}"}
                          ]
            "command": [
                          "python",
                          "live_analytics_tool/controller.py"
                       ],
            "logConfiguration" : {
              "logDriver" : "awslogs",
              "options" : {
                "awslogs-group" : "ecs-${var.env}-${var.name}-live-analytics-tools",
                "awslogs-stream-prefix" : "ecs-${var.env}-${var.name}-live-analytics-tools"
                "awslogs-region" : "${var.region}"
              }
            }
        }
    ])
    container_port = 8080
    container_name = "${var.name}-live-analytics-tools"
}