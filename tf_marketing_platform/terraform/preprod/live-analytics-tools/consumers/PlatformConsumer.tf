variable "consumer9_name" {
  type        = string
  default     = "PlatformConsumer"
}

# Custom IAM Policy for DynamoDB - Least Privilege Access
resource "aws_iam_policy" "platform_consumer_dynamodb_policy" {
  name        = "${var.env}-${var.consumer9_name}-live-analytics-tools-dynamodb-policy"
  description = "Least privilege DynamoDB access for PlatformConsumer live analytics tools"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:GetItem",
          "dynamodb:BatchWriteItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem"
        ]
        Resource = "arn:aws:dynamodb:${var.region}:463166080413:table/mktg-realtimedata-attributes"
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:DeleteItem",
          "dynamodb:PutItem",
          "dynamodb:GetItem"
        ]
        Resource = "arn:aws:dynamodb:${var.region}:463166080413:table/mktg-tmp-cart-data"
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:UpdateItem"
        ]
        Resource = "arn:aws:dynamodb:${var.region}:463166080413:table/mktg-pixel-checkpoints-dev"
      }
    ]
  })

  tags = {
    Name        = "${var.env}-${var.consumer9_name}-live-analytics-tools-dynamodb-policy"
    Environment = var.env
    Service     = "live-analytics-tools"
    Component   = "PlatformConsumer"
  }
}

# Custom IAM Policy for SSM Parameter Store - Least Privilege Access
resource "aws_iam_policy" "platform_consumer_ssm_policy" {
  name        = "${var.env}-${var.consumer9_name}-live-analytics-tools-ssm-policy"
  description = "Least privilege SSM Parameter Store access for PlatformConsumer live analytics tools"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ssm:GetParameter"
        ]
        Resource = [
          "arn:aws:ssm:${var.region}:463166080413:parameter/beacon-pixel/s2s_api_key",
          "arn:aws:ssm:${var.region}:463166080413:parameter/beacon-pixel/s2s_auth_token",
          "arn:aws:ssm:${var.region}:463166080413:parameter/beacon-pixel/next_gen_sfly_apikey_header"
        ]
      }
    ]
  })

  tags = {
    Name        = "${var.env}-${var.consumer9_name}-live-analytics-tools-ssm-policy"
    Environment = var.env
    Service     = "live-analytics-tools"
    Component   = "PlatformConsumer"
  }
}

resource "aws_iam_policy" "KafkaReadPolicy" {
  name   = "platformConsumer-kafka-read-policy"
  path   = "/"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "kafka-cluster:Connect",
          "kafka-cluster:DescribeCluster"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:kafka:us-east-1:749291820019:cluster/msk-infra-dev/5b07cba1-2e5b-4d14-9c70-5b60848b4884-4"
      },
      {
        Action = [
          "kafka-cluster:ReadData",
          "kafka-cluster:DescribeTopic"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:kafka:us-east-1:749291820019:topic/msk-infra-dev/5b07cba1-2e5b-4d14-9c70-5b60848b4884-4/sfly.beacon.pixel-events"
      },
      {
        Action = [
          "kafka-cluster:AlterGroup",
          "kafka-cluster:DescribeGroup"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:kafka:us-east-1:749291820019:group/msk-infra-dev/5b07cba1-2e5b-4d14-9c70-5b60848b4884-4/mktg_pixel_events"
      },
      {
        Action = [
          "kafka:GetBootstrapBrokers"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "platformConsumer_policy_attachment" {
  role       = "ecs-preprod-PlatformConsumer-live-analytics-tools"
  policy_arn = aws_iam_policy.KafkaReadPolicy.arn
}

module "platformConsumer-live-analytics-tools" {
    source  = "../../../modules/base/simple_ecs_service"
    name = "${var.consumer9_name}-live-analytics-tools"
    cluster_name = var.cluster_name
    env = "${var.env}"
    vpc_id = "vpc-0d28db63f078ecbd4"
    cidrs = ["1.1.1.1/32"]
    cpu = 512
    memory = 1024
    desired_count = 1
    policies = [
                  "arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess",
                  "arn:aws:iam::aws:policy/CloudWatchFullAccess",
                  "arn:aws:iam::aws:policy/AmazonKinesisFullAccess",
                  "arn:aws:iam::aws:policy/AmazonS3FullAccess",
                  "arn:aws:iam::aws:policy/AWSLambdaInvocation-DynamoDB",
                  "arn:aws:iam::aws:policy/AmazonSSMFullAccess",
                  "arn:aws:iam::463166080413:policy/MSK-Read-Policy"
               ]
    ecs_task_execution_role = "arn:aws:iam::463166080413:role/ecsTaskExecutionRole"
    service_subnets = ["subnet-0ab389bd3b7d33716", "subnet-0bf9258531706106b", "subnet-0927b59f9b95e0e7d"]
    task_definition = jsonencode(
    [
        {
            name      = "${var.consumer9_name}-live-analytics-tools"
            image     = "463166080413.dkr.ecr.us-east-1.amazonaws.com/live-analytics-tools:latest"
            essential = true
            environment = [
                            {"name": "SERVICE_SUFFIX", "value": "live-analytics-tools"},
                            {"name": "STAGE", "value": "${var.stage}"},
                            {"name": "ENV", "value": "${var.env}"},
                            {"name": "CONTROLLER_NAME", "value": "controller"},
                            {"name": "ECS_CONTAINER_STOP_TIMEOUT", "value": "900s"}
                          ]
            "logConfiguration" : {
              "logDriver" : "awslogs",
              "options" : {
                "awslogs-group" : "ecs-${var.env}-${var.consumer9_name}-live-analytics-tools",
                "awslogs-stream-prefix" : "ecs-${var.env}-${var.consumer9_name}-live-analytics-tools"
                "awslogs-region" : "${var.region}"
              }
            }
        }
    ])
    container_port = 8080
    container_name = "${var.consumer9_name}-live-analytics-tools"
}
