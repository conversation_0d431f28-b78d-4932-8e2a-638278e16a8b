variable "consumer6_name" {
  type        = string
  default     = "ProspectsCollect"
}

# Custom IAM Policy for DynamoDB - Least Privilege Access
resource "aws_iam_policy" "prospects_collect_dynamodb_policy" {
  name        = "${var.env}-${var.consumer6_name}-live-analytics-tools-dynamodb-policy"
  description = "Least privilege DynamoDB access for ProspectsCollect live analytics tools"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:PutItem",
          "dynamodb:DeleteItem",
          "dynamodb:Scan"
        ]
        Resource = "arn:aws:dynamodb:${var.region}:463166080413:table/${var.env}-controller-live-analytics-tools"
      }
    ]
  })

  tags = {
    Name        = "${var.env}-${var.consumer6_name}-live-analytics-tools-dynamodb-policy"
    Environment = var.env
    Service     = "live-analytics-tools"
    Component   = "ProspectsCollect"
  }
}

# Custom IAM Policy for ECS - Least Privilege Access
resource "aws_iam_policy" "prospects_collect_ecs_policy" {
  name        = "${var.env}-${var.consumer6_name}-live-analytics-tools-ecs-policy"
  description = "Least privilege ECS access for ProspectsCollect live analytics tools"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecs:UpdateService",
          "ecs:ListTasks",
          "ecs:StopTask",
          "ecs:DescribeTasks"
        ]
        Resource = [
          "arn:aws:ecs:${var.region}:463166080413:cluster/ecs-${var.env}-cluster-v1",
          "arn:aws:ecs:${var.region}:463166080413:service/ecs-${var.env}-cluster-v1/ecs-${var.env}-RecentlyViewed-live-analytics-tools",
          "arn:aws:ecs:${var.region}:463166080413:service/ecs-${var.env}-cluster-v1/ecs-${var.env}-ProspectsCollect-live-analytics-tools",
          "arn:aws:ecs:${var.region}:463166080413:service/ecs-${var.env}-cluster-v1/ecs-${var.env}-PlatformConsumer-live-analytics-tools",
          "arn:aws:ecs:${var.region}:463166080413:task/ecs-${var.env}-cluster-v1/*"
        ]
      }
    ]
  })

  tags = {
    Name        = "${var.env}-${var.consumer6_name}-live-analytics-tools-ecs-policy"
    Environment = var.env
    Service     = "live-analytics-tools"
    Component   = "ProspectsCollect"
  }
}

# Custom IAM Policy for CloudWatch Logs - Least Privilege Access
resource "aws_iam_policy" "prospects_collect_cloudwatch_policy" {
  name        = "${var.env}-${var.consumer6_name}-live-analytics-tools-cloudwatch-policy"
  description = "Least privilege CloudWatch Logs access for ProspectsCollect live analytics tools"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogGroups",
          "logs:DescribeLogStreams"
        ]
        Resource = [
          "arn:aws:logs:${var.region}:463166080413:log-group:ecs-${var.env}-${var.consumer6_name}-live-analytics-tools",
          "arn:aws:logs:${var.region}:463166080413:log-group:ecs-${var.env}-${var.consumer6_name}-live-analytics-tools:*"
        ]
      }
    ]
  })

  tags = {
    Name        = "${var.env}-${var.consumer6_name}-live-analytics-tools-cloudwatch-policy"
    Environment = var.env
    Service     = "live-analytics-tools"
    Component   = "ProspectsCollect"
  }
}

resource "aws_iam_policy" "ProspectKafkaReadPolicy" {
  name   = "prospectConsumer-kafka-read-policy"
  path   = "/"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "kafka-cluster:Connect",
          "kafka-cluster:DescribeCluster"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:kafka:us-east-1:749291820019:cluster/msk-infra-dev/5b07cba1-2e5b-4d14-9c70-5b60848b4884-4"
      },
      {
        Action = [
          "kafka-cluster:ReadData",
          "kafka-cluster:DescribeTopic"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:kafka:us-east-1:749291820019:topic/msk-infra-dev/5b07cba1-2e5b-4d14-9c70-5b60848b4884-4/sfly.beacon.pixel-events"
      },
      {
        Action = [
          "kafka-cluster:AlterGroup",
          "kafka-cluster:DescribeGroup"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:kafka:us-east-1:749291820019:group/msk-infra-dev/5b07cba1-2e5b-4d14-9c70-5b60848b4884-4/mktg_pixel_events"
      },
      {
        Action = [
          "kafka:GetBootstrapBrokers"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "prospectConsumer_policy_attachment" {
  role       = "ecs-preprod-ProspectsCollect-live-analytics-tools"
  policy_arn = aws_iam_policy.ProspectKafkaReadPolicy.arn
}

module "ProspectsCollect-live-analytics-tools" {
    source  = "../../../modules/base/simple_ecs_service"
    name = "${var.consumer6_name}-live-analytics-tools"
    cluster_name = var.cluster_name
    env = "${var.env}"
    vpc_id = "vpc-0d28db63f078ecbd4"
    cidrs = ["1.1.1.1/32"]
    cpu = 512
    memory = 1024
    desired_count = 1
    policies = [
                  aws_iam_policy.prospects_collect_dynamodb_policy.arn,
                  aws_iam_policy.prospects_collect_ecs_policy.arn,
                  aws_iam_policy.prospects_collect_cloudwatch_policy.arn
               ]
    ecs_task_execution_role = "arn:aws:iam::463166080413:role/ecsTaskExecutionRole"
    service_subnets = ["subnet-0c4e52dd218d11b06", "subnet-097e93dc6ffb310bd"]
    task_definition = jsonencode(
    [
        {
            name      = "${var.consumer6_name}-live-analytics-tools"
            image     = "463166080413.dkr.ecr.us-east-1.amazonaws.com/live-analytics-tools:latest"
            essential = true
            environment = [
                            {"name": "SERVICE_SUFFIX", "value": "live-analytics-tools"},
                            {"name": "STAGE", "value": "${var.stage}"},
                            {"name": "ENV", "value": "${var.env}"},
                            {"name": "CONTROLLER_NAME", "value": "controller"},
                            {"name": "ECS_CONTAINER_STOP_TIMEOUT", "value": "900s"}
                          ]
            "logConfiguration" : {
              "logDriver" : "awslogs",
              "options" : {
                "awslogs-group" : "ecs-${var.env}-${var.consumer6_name}-live-analytics-tools",
                "awslogs-stream-prefix" : "ecs-${var.env}-${var.consumer6_name}-live-analytics-tools"
                "awslogs-region" : "${var.region}"
              }
            }
        }
    ])
    container_port = 8080
    container_name = "${var.consumer6_name}-live-analytics-tools"
}
