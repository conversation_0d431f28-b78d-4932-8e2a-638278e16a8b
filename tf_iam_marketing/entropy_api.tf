
module "service_policy_entropy_v2_apigateway" {
  source      = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"
  stage_name  = var.stage_name
  policy_name = "entropy_apigateway"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/entropy-permissions_apigateway.json")

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

}

module "service_role_entropy_v2_apigateway" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias    = data.aws_iam_account_alias.current.account_alias
  account_id       = data.aws_caller_identity.current.account_id
  role_name_prefix = local.role_name_prefix_services
  role_name        = "entropy-apigateway"
  assume_role      = "apigateway_assume"

  policy_names = [
    module.service_policy_entropy_v2_apigateway.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
  ]
}
