locals {
  fq_policy_name_environment_dsmp_users_ec2 = "${var.stage_name}-dsmp-users-ec2-account-specific"
}

module "service_policy_svc_marketing_dsmp_users_ec2" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-dsmp-users-ec2"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/${var.stage_name}-svc-mktg-dsmp-users-ec2.json")
}

module "service_policy_svc_marketing_dsmp_users_ec2_athenaglue" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-dsmp-users-ec2-athenaglue"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/${var.stage_name}-svc-mktg-dsmp-users-ec2-athenaglue.json")
}

module "service_role_marketing_dsmp_users_ec2" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix        = local.role_name_prefix_services
  role_name               = "marketing-dsmp-users-ec2"
  assume_role             = "ec2_assume"
  enable_instance_profile = true

  policy_names = [
    module.service_policy_svc_marketing_dsmp_users_ec2.name,
    module.service_policy_svc_marketing_dsmp_users_ec2_athenaglue.name,
    aws_iam_policy.environment_dsmp_users_ec2.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
    module.marketing_kms_key.name,
  ]
}

resource "aws_iam_policy" "environment_dsmp_users_ec2" {
  name   = local.fq_policy_name_environment_dsmp_users_ec2
  path   = "/"
  policy = data.aws_iam_policy_document.environment_dsmp_users_ec2_policy.json
}

data "aws_iam_policy_document" "environment_dsmp_users_ec2_policy" {
  statement {
    sid    = "DWHKMSKeyUsageS3"
    effect = "Allow"

    actions = [
      "kms:Decrypt",
      "kms:Encrypt",
      "kms:GenerateDataKey*",
    ]

    resources = [
      "arn:aws:kms:us-east-1:${var.dwh_account[var.stage_name]}:key/${var.dwh_s3_key[var.stage_name]}",
    ]
  }
}

