module "policy_analytics_users" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-analytics-users"

  template = file("${path.module}/templates/marketing-analytics.json")
}

module "policy_analytics_personalize" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-analytics-personalize"

  template = file("${path.module}/templates/svc-marketing-personalize.json")
}

module "cis_role_marketing_analytics" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix = local.role_name_prefix_people
  role_name        = "analytics"
  assume_role      = "saml_provider_assume"

  max_session_duration = var.cis_role_session_duration

  policy_names = [
    module.policy_analytics_users.name,
    module.policy_analytics_personalize.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
    module.marketing_kms_key.name,
    module.service_policy_svc_dwh_s3_kms.name,
    "${var.stage_name}-shared-readonly",
  ]
}

