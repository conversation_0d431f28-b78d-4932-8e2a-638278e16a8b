module "service_policy_svc_marketing_glue" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-glue"

  template = file("${path.module}/templates/svc-marketing-glue.json")
}

module "service_role_marketing_glue" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix = local.role_name_prefix_services
  role_name        = "marketing-glue"
  assume_role      = "glue_assume"

  policy_names = [
    module.service_policy_svc_marketing_glue.name,
    module.marketing_kms_key.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
  ]
}

