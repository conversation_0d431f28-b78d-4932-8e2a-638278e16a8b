// Instantiate a minimal version of the module for testing
provider "aws" {
  region = "us-east-1"

  default_tags {
    tags = {
      ManagedBy    = "marketing-platform"
      Environment  = "dev"
      App          = "IAM"
      BusinessUnit = "Consumer"
      Provisioner  = "Terraform"

      # custom tag
      Repository = "https://github.com/sflyinc-shutterfly/tf_iam_marketing"
    }
  }
}

variable "region" {
  type = string
}

resource "random_id" "testing_suffix" {
  byte_length = 4
}

module "preprod" {
  source     = "../"
  stage_name = "preprod"

  # this value doesn't really matter; SignalFX won't be collecting metrics on the test role
  signalfx_external_id = "1234"

  enable_service_linked_roles = false

  enable_password_policy = false

  # identify roles/policy not to create for testing - roles might already exist, managed by other system (awsIAM)
  enable_jenkins   = false
  enable_splunk_hf = false
}

output "terraform_state" {
  description = "The path to the Terraform state file; used in the state_file control"
  value       = "${path.cwd}/terraform.tfstate.d/${terraform.workspace}/terraform.tfstate"
}

output "preprod_role_names" {
  value = module.preprod.role_names
}

output "preprod_user_names" {
  value = module.preprod.user_names
}

