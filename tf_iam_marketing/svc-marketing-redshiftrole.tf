module "service_policy_svc_marketing_redshift" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-redshift"

  template = file("${path.module}/templates/svc-marketing-redshift.json")
}

module "service_policy_svc_marketing_dwh_glue" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-dwh-glue"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/svc-marketing-dwh-glue.json")
}

module "service_policy_svc_marketing_mfg_glue" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-mfg-glue"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/${var.stage_name}-mfg-glue-assume.json")
}

module "service_role_marketing_redshift" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix = local.role_name_prefix_services
  role_name        = "marketing-redshift"
  assume_role      = "redshift_assume"

  policy_names = [
    module.service_policy_svc_marketing_redshift.name,
    module.service_policy_svc_marketing_dwh_glue.name,
    module.service_policy_svc_marketing_dwh_s3.name,
    module.service_policy_svc_mfg_s3.name,
    module.service_policy_svc_marketing_mfg_glue.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
    module.marketing_kms_key.name,
    module.service_policy_svc_dwh_s3_kms.name,
    module.actioniq_kms_key.name
  ]
}

data "template_file" "actioniq_kms_policy_template" {
  template = file("${path.module}/templates/svc-marketing-actioniq-kms.json")

  vars = {
    actioniq_key    = "${var.mktg_account[var.stage_name]}:key/${var.actioniq_s3_key[var.stage_name]}"
    mktg_account_id = var.mktg_account[var.stage_name]
  }
}

module "actioniq_kms_key" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name = var.stage_name

  policy_name = "actioniq_kms"
  template    = data.template_file.actioniq_kms_policy_template.rendered
}

module "read_only_user" {
  source        = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//user"
  stage_name    = var.stage_name
  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  enabled = false

  policy_names = [
    module.service_policy_svc_marketing_redshift.name,
  ]

  user_name = "redshift"
}

