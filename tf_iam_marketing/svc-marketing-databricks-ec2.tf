module "service_policy_svc_marketing_databricks_ec2" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-databricks-ec2"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/${var.stage_name}-svc-mktg-databricks-ec2.json")
}

module "service_policy_svc_marketing_databricks_ec2_extra" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-databricks-ec2-extra"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/${var.stage_name}-svc-mktg-databricks-ec2-extra.json")
}

module "service_policy_svc_can_assume_memories" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "pc-memories-can-assume"

  template = file("${path.module}/templates/svc-pc-memories-can-assume-policy.json")
}

module "service_role_marketing_databricks_ec2" {
  source        = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"
  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix        = local.role_name_prefix_services
  role_name               = "marketing-databricks-ec2"
  assume_role             = "ec2_assume"
  enable_instance_profile = true

  policy_names = [
    module.service_policy_svc_marketing_databricks_ec2.name,
    module.service_policy_svc_marketing_databricks_ec2_extra.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
    module.actioniq_kms_key.name,
    module.service_policy_svc_can_assume_memories.name
  ]

}
