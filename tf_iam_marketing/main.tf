locals {
  role_name_prefix_services               = "${data.aws_iam_account_alias.current.account_alias}-svc-"
  role_name_prefix_people                 = "${data.aws_iam_account_alias.current.account_alias}-cis-"
  policy_name_deny_security_critical_apis = "${var.stage_name}-deny-security-critical-aws-apis"
  policy_name_deny_iam_critical_apis      = "${var.stage_name}-deny-iam-critical-apis"
}

module "standard_policies" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//standard_policies"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name              = var.stage_name
  role_name_prefix_people = local.role_name_prefix_people

  enable_admin                     = true
  enable_operations                = true
  enable_platform                  = true
  enable_cloudwatch_to_s3          = true
  enable_appeng                    = true
  enable_iam                       = true
  enable_infosec                   = true
  enable_ecs_autoscale             = true
  enable_ecs_instance_role         = true
  enable_ecs_service               = true
  enable_public_key_access         = true
  enable_signalfx                  = true
  enable_drainer                   = true
  enable_host_monitor              = true
  enable_ecs_asg_hook              = true
  enable_platform_ecs_task_monitor = true
  enable_splunk_forwarder          = true
  enable_crash_monitor             = true
  enable_sre                       = true
  enable_sre_lead                  = true

  enable_jenkins   = var.enable_jenkins
  enable_splunk_hf = var.enable_splunk_hf

  enable_password_policy = var.enable_password_policy
}

module "standard_roles" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//standard_roles"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix_services = local.role_name_prefix_services
  role_name_prefix_people   = local.role_name_prefix_people
  stage_name                = var.stage_name

  policy_name_deny_security_critical_apis = module.standard_policies.policy_name_deny_security_critical_apis

  enable_admin                     = true
  enable_operations                = true
  enable_platform                  = true
  enable_cloudwatch_to_s3          = true
  enable_appeng                    = true
  enable_iam                       = true
  enable_infosec                   = true
  enable_jumphost                  = true
  enable_ecs_autoscale             = true
  enable_ecs_instance_role         = true
  enable_ecs_service               = true
  enable_drainer                   = true
  enable_host_monitor              = true
  enable_ecs_asg_hook              = true
  enable_platform_ecs_task_monitor = true
  enable_signalfx                  = true
  enable_splunk_forwarder          = true
  enable_crash_monitor             = true
  enable_sre                       = true
  enable_sre_lead                  = true

  enable_jenkins         = var.enable_jenkins
  enable_getawsresources = false
  enable_splunk_hf       = var.enable_splunk_hf

  service_linked_roles = [
    "ecs.application-autoscaling.amazonaws.com",
    "lakeformation.amazonaws.com",
  ]

  signalfx_external_id = var.signalfx_external_id

  enable_service_linked_roles = var.enable_service_linked_roles

  depends_on = [module.standard_policies]
}

locals {
  roles_managed_by_module = compact(
    flatten(
      [
        module.standard_roles.role_names,
        module.service_role_airflow.role_names,
        module.service_role_redshift-ec2.role_names,
        module.service_role_marketing_emr.role_names,
        module.service_role_marketing_emr_ec2.role_names,
        module.service_role_marketing_emr_autoscaling.role_names,
        module.service_role_marketing_redshift.role_names,
        module.service_role_marketing_dwh_s3.role_names,
        module.service_role_marketing_unica.role_names,
        module.service_role_marketing_databricksxacct.role_names,
        module.service_role_marketing_databricks_ec2.role_names,
        module.service_role_marketing_dsmp_users_ec2.role_names,
        module.cis_role_marketing_dsmp_users.role_names,
        module.service_role_marketing_dwh_redshift_spectrum.role_names,
        module.service_role_marketing_databricks_reports.role_names,
        module.service_role_ecs.role_names,
        module.service_role_lambda.role_names,
        module.service_role_managed_airflow.role_names,
        module.service_role_kinesis.role_names,
        module.cis_role_marketing_analytics.role_names,
        module.cis_role_marketing_ops.role_names,
      ],
    ),
  )

  #the nulls are instead of removed users so we don't break other stuff
  users_managed_by_module = [
    null,
    null,
    null,
    module.read_only_user.iam_users
  ]
}

module "deny_iam_critical_apis" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "deny-iam-critical-apis"

  template = file("${path.module}/templates/deny-iam-critical-apis.json")
}


resource "aws_iam_role_policy_attachment" "all_kms_keys_policy" {
  role       = module.standard_roles.role_name_platform
  policy_arn = module.all_kms_key.arn
}

module "marketing_dbe_policy" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-dbe"

  template = file("${path.module}/templates/marketing-dbe.json")
}

resource "aws_iam_role_policy_attachment" "marketing_dbe_policy" {
  role       = module.standard_roles.role_name_dbe
  policy_arn = module.marketing_dbe_policy.arn
}

