locals {
  fq_policy_name_environment_marketing_ops = "${var.stage_name}-marketing-ops-account-specific"
}
module "policy_marketing_ops" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-ops"

  template = file("${path.module}/templates/marketing-ops.json")
}

module "cis_role_marketing_ops" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix = local.role_name_prefix_people
  role_name        = "marketing-ops"
  assume_role      = "saml_provider_assume"

  max_session_duration = var.cis_role_session_duration

  policy_names = [
    module.policy_marketing_ops.name,
    aws_iam_policy.environment_marketing_ops.name,
    module.service_policy_svc_dwh_s3_kms.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
    "${var.stage_name}-shared-readonly",
  ]
}

resource "aws_iam_policy" "environment_marketing_ops" {
  name   = local.fq_policy_name_environment_marketing_ops
  path   = "/"
  policy = data.aws_iam_policy_document.environment_marketing_ops_policy.json
}

data "aws_iam_policy_document" "environment_marketing_ops_policy" {
  statement {
    sid    = "MKTGKMSKeyUsageS3"
    effect = "Allow"

    actions = [
      "kms:DescribeKey",
      "kms:ListGrants",
      "kms:CreateGrant",
      "kms:RevokeGrant",
      "kms:Decrypt",
      "kms:Encrypt",
      "kms:GenerateDataKey*",
      "kms:ReEncrypt*",
    ]

    resources = [
      "arn:aws:kms:us-east-1:${data.aws_caller_identity.current.account_id}:key/${var.mktg_s3_key[var.stage_name]}",
    ]
  }
  statement {
    sid    = "RAMAccessFromCentralAccount"
    effect = "Allow"

    actions = [
      "ram:AcceptResourceShareInvitation",
      "ram:RejectResourceShareInvitation",
    ]

    resources = [
      "arn:aws:ram:us-east-1:${data.aws_caller_identity.current.account_id}:resource-share-invitation/*",
    ]

    condition {
      test     = "StringLike"
      variable = "ram:ShareOwnerAccountId"

      values = [
        "${var.lakeformation_central_account[var.stage_name]}",
      ]
    }
  }
}

