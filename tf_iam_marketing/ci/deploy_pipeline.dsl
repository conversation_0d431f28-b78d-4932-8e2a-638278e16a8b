import Defs
import javaposse.jobdsl.dsl.DslFactory
import policies.iam.IAMDefaults
import policies.sfly.SflyDefaults
import policies.DockerImages

Defs.AWS_ACCOUNT_DEPLOY.values().each { Defs.AWS_ACCOUNT_DEPLOY awsAccountDeploy ->
  GString awsAccountAlias = "${awsAccountDeploy.alias}"
  GString jobNamePlan = "${Defs.jobNamePlanBase}-${awsAccountAlias}"
  GString jobNameApply = "${Defs.jobNameApplyBase}-${awsAccountAlias}"
  GString projectFolder = "${Defs.projectName}"
  Boolean autoDeploy = true

  freeStyleJob(jobNamePlan) {

    description("Runs Terraform plan against ${Defs.projectName} module to identify changes for this deployment. \nTerraform plans can be applied after approval with the ${jobNameApply} job")

    SflyDefaults.addRunPermissions(delegate, Defs.devTeam)

    IAMDefaults.createTerraformPlan(delegate, Defs.repoUrl, awsAccountAlias, autoDeploy)
    SflyDefaults.dockerImage(delegate, DockerImages.CLOUD_PLATFORM_TOOLS_TF_1_10)

    steps {
      shell("""
echo "Link to apply plan: \${JENKINS_URL}job/${projectFolder}/job/${jobNameApply}/parambuild?PLAN_JOB_BUILD_ID=\${BUILD_ID}"
      """)
    }
  }

  freeStyleJob(jobNameApply) {

    description("Deploy ${Defs.projectName} to AWS account ${awsAccountAlias}. \nJob is automatically generated.")

    SflyDefaults.addRunPermissions(delegate, Defs.devTeam)

    IAMDefaults.applyTerraformPlan(delegate, Defs.repoUrl, awsAccountAlias)
    SflyDefaults.dockerImage(delegate, DockerImages.CLOUD_PLATFORM_TOOLS_TF_1_10)

    blockOnDownstreamProjects()
  }

  ((DslFactory) this).buildPipelineView("${Defs.projectName}-deploy-pipeline-${awsAccountAlias}") {
    SflyDefaults.buildPipelineViewDefaults(delegate)
    filterBuildQueue()
    filterExecutors()
    title("Deploy pipeline for ${Defs.projectName} in ${awsAccountAlias}")
    description("Deploy pipeline for ${Defs.projectName} in ${awsAccountAlias}. \nAutomatically generated, edit in ci/...")
    displayedBuilds(10)
    consoleOutputLinkStyle(OutputStyle.NewWindow)
    alwaysAllowManualTrigger()
    showPipelineParameters()
    refreshFrequency(60)
  }

}
