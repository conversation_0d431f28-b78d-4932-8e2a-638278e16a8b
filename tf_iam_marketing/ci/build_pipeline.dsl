import Defs
import javaposse.jobdsl.dsl.DslFactory
import policies.iam.IAMDefaults
import policies.sfly.SflyDefaults
import policies.DockerImages

freeStyleJob(Defs.jobNamePullRequest) {
  SflyDefaults.addRunPermissions(delegate, Defs.devTeam)
  IAMDefaults.checkPullRequest(delegate, Defs.repoUrl, Defs.AWS_ACCOUNT_ALIAS_CI, Defs.SECRET_NAME_CI_USER_API_KEY)
  SflyDefaults.dockerImage(delegate, DockerImages.CLOUD_PLATFORM_TOOLS_TF_1_10)
}

freeStyleJob(Defs.jobNameIntegrationTest) {
  SflyDefaults.addRunPermissions(delegate, Defs.devTeam)
  IAMDefaults.runIntegrationTests(delegate, Defs.repoUrl, Defs.AWS_ACCOUNT_ALIAS_CI, Defs.SECRET_NAME_CI_USER_API_KEY, Defs.jobNamePublish)
  SflyDefaults.dockerImage(delegate, DockerImages.CLOUD_PLATFORM_TOOLS_TF_1_10)
}

List<String> publishDownstreamJobNames = Defs.AWS_ACCOUNT_DEPLOY.values().collect { it ->
  "${Defs.jobNamePlanBase}-${it.alias}"
}

freeStyleJob(Defs.jobNamePublish) {
  SflyDefaults.addRunPermissions(delegate, Defs.devTeam)

  IAMDefaults.publishRelease(delegate, Defs.repoUrl, publishDownstreamJobNames)
}

((DslFactory) this).buildPipelineView("${Defs.projectName}-build-pipeline") {
  SflyDefaults.buildPipelineViewDefaults(delegate)
  filterBuildQueue()
  filterExecutors()
  title("Build pipeline for ${Defs.projectName}")
  description("Build pipeline for ${Defs.projectName}. \nAutomatically generated, edit in ci/...")
  displayedBuilds(10)
  consoleOutputLinkStyle(OutputStyle.NewWindow)
  selectedJob(Defs.jobNameIntegrationTest)
  alwaysAllowManualTrigger()
  showPipelineParameters()
  refreshFrequency(60)
}
