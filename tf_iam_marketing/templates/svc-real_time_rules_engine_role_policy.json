{"Version": "2012-10-17", "Statement": [{"Sid": "InvokeAndListRulesEngineLambdaPermissions", "Effect": "Allow", "Action": ["lambda:GetFunction", "lambda:InvokeFunction", "lambda:ListFunctions"], "Resource": "arn:aws:lambda:us-east-1:${current_account_id}:function:real-time-rules-engine*"}, {"Sid": "dynamoForRulesEngine", "Action": ["dynamodb:GetItem", "dynamodb:<PERSON><PERSON>", "dynamodb:PutItem", "dynamodb:DeleteItem"], "Effect": "Allow", "Resource": ["arn:aws:dynamodb:us-east-1:${current_account_id}:table/mktg-pixel-configurations"]}, {"Sid": "s3UsedInForRulesEngine", "Action": ["s3:PutObject", "s3:GetObject", "s3:DeleteObject"], "Effect": "Allow", "Resource": ["arn:aws:s3:::sfly-aws-mktg-${stage_name}-streams-data/*"]}, {"Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Effect": "Allow", "Resource": "*", "Sid": "LambdaBasic"}]}