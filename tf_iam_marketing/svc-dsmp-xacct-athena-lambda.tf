locals {
  fq_policy_name_environment_dsmp_xacct_lambda = "${var.stage_name}-dsmp-xacct-lambda-account-specific"
}

module "policy_dsmp_xacct_lambda" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "dsmp-xacct-lambda"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/${var.stage_name}-dsmp-xacct-lambda.json")
}

module "role_dsmp_xacct_lambda" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix = local.role_name_prefix_services
  role_name        = "dsmp-xacct-lambda"
  assume_role      = "lambda_assume"

  policy_names = [
    module.policy_dsmp_xacct_lambda.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
  ]
}

