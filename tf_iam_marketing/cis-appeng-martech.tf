# Primary Policy Module
module "service_policy_cis_appeng_martech" {
  source      = "**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"
  stage_name  = var.stage_name
  policy_name = "appeng-martech"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/cis-appeng-martech.json")

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id
}

# Additional Policy Module for Production Only
module "service_policy_cis_appeng_martech_extra" {
  source      = "**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"
  stage_name  = var.stage_name
  policy_name = "appeng-martech-extra"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/cis-appeng-martech-extra.json")

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  # Conditional creation
  count = var.stage_name == "prod" ? 1 : 0
}

# Additional Policy Module 2 for Production Only
module "service_policy_cis_appeng_martech_extra_2" {
  source      = "**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"
  stage_name  = var.stage_name
  policy_name = "appeng-martech-extra-2"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/cis-appeng-martech-extra-2.json")

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  # Conditional creation
  count = var.stage_name == "prod" ? 1 : 0
}

# Additional Policy Module 3 for Production Only
module "service_policy_cis_appeng_martech_extra_3" {
  source      = "**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"
  stage_name  = var.stage_name
  policy_name = "appeng-martech-extra-3"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/cis-appeng-martech-extra-3.json")

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  # Conditional creation
  count = var.stage_name == "prod" ? 1 : 0
}

# Additional Policy Module 4 for Production Only
module "service_policy_cis_appeng_martech_extra_4" {
  source      = "**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"
  stage_name  = var.stage_name
  policy_name = "appeng-martech-extra-4"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/cis-appeng-martech-extra-4.json")

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  # Conditional creation
  count = var.stage_name == "prod" ? 1 : 0
}

# Service Role Module
module "service_role_cis_appeng_martech" {
  source           = "**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"
  role_name_prefix = local.role_name_prefix_people
  role_name        = "appeng-martech"
  assume_role      = "saml_provider_assume"

  max_session_duration = var.cis_role_session_duration

  # Conditional policy names based on stage_name
  policy_names = var.stage_name == "prod" ? [
    module.service_policy_cis_appeng_martech.name,
    module.service_policy_cis_appeng_martech_extra[0].name,
    module.service_policy_cis_appeng_martech_extra_2[0].name,
    module.service_policy_cis_appeng_martech_extra_3[0].name,
    module.service_policy_cis_appeng_martech_extra_4[0].name,
    module.standard_policies.policy_name_deny_security_critical_apis,
    "${var.stage_name}-shared-readonly",
    "${var.stage_name}-shared-readonly-extra",
  ] : [
    module.service_policy_cis_appeng_martech.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
    "${var.stage_name}-shared-readonly",
    "${var.stage_name}-shared-readonly-extra",
  ]

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id
}
