locals {
  fq_policy_name_environment_dsmp_users = "${var.stage_name}-dsmp-users-account-specific"
}

module "policy_dsmp_users" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-dsmp-users"

  template = file(
    "${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/${var.stage_name}-cis-mktg-dsmp-users.json",
  )
}

module "policy_dsmp_users_athenaglue" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-dsmp-users-athenaglue"

  template = file(
    "${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/${var.stage_name}-cis-mktg-dsmp-users-athenaglue.json",
  )
}

module "cis_role_marketing_dsmp_users" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix = local.role_name_prefix_people
  role_name        = "marketing-dsmp-users"
  assume_role      = "saml_provider_assume"

  max_session_duration = var.cis_role_session_duration

  policy_names = [
    module.policy_dsmp_users.name,
    module.policy_dsmp_users_athenaglue.name,
    aws_iam_policy.environment_dsmp_users.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
    module.marketing_kms_key.name,
  ]
}

resource "aws_iam_policy" "environment_dsmp_users" {
  name   = local.fq_policy_name_environment_dsmp_users
  path   = "/"
  policy = data.aws_iam_policy_document.environment_dsmp_users_policy.json
}

data "aws_iam_policy_document" "environment_dsmp_users_policy" {
  statement {
    sid    = "DWHKMSKeyUsageS3"
    effect = "Allow"

    actions = [
      "kms:Decrypt",
      "kms:Encrypt",
      "kms:GenerateDataKey*",
    ]

    resources = [
      "arn:aws:kms:us-east-1:${var.dwh_account[var.stage_name]}:key/${var.dwh_s3_key[var.stage_name]}",
    ]
  }
}

