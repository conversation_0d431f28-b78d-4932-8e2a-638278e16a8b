module "service_pythonproject2" {
  source      = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"
  enabled     = var.stage_name == "preprod" ? true : false
  stage_name  = var.stage_name
  policy_name = "pythonproject2_lambda"

  template = file("${path.module}/live/sfly-aws-marketing-preprod/templates/pythonproject2.json")

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

}

module "service_role_pythonproject2" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"
  enabled = var.stage_name == "preprod" ? true : false
  account_alias    = data.aws_iam_account_alias.current.account_alias
  account_id       = data.aws_caller_identity.current.account_id
  role_name_prefix = local.role_name_prefix_services
  role_name        = "pythonproject2_lambda"
  assume_role      = "lambda_assume"

  policy_names = [
    module.service_pythonproject2.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
  ]
}