{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ses:SendEmail", "ses:SendCustomVerificationEmail", "ses:SendRawEmail", "ses:SendTemplatedEmail", "ses:<PERSON>erifyEmail<PERSON><PERSON>"], "Resource": "*"}, {"Effect": "Allow", "Action": ["athena:CancelQueryExecution", "athena:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "athena:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "athena:<PERSON><PERSON><PERSON><PERSON>", "athena:StartQueryExecution", "athena:StopQueryExecution"], "Resource": ["arn:aws:athena:us-east-1:${current_account_id}:*"]}, {"Effect": "Allow", "Action": ["cloudformation:ValidateTemplate"], "Resource": "*"}, {"Sid": "CreateDeleteLambdaLayerPermissions", "Effect": "Allow", "Action": ["lambda:DeleteLayerVersion", "lambda:PublishLayerVersion"], "Resource": "arn:aws:lambda:us-east-1:${current_account_id}:layer:*"}, {"Sid": "apigatewayPermissions", "Effect": "Allow", "Action": ["apigateway:PATCH", "apigateway:POST"], "Resource": "arn:aws:apigateway:us-east-1::/*/*"}, {"Sid": "SQSPermissions", "Effect": "Allow", "Action": ["sqs:SendMessage", "sqs:DeleteMessage"], "Resource": ["arn:aws:sqs:us-east-1:${current_account_id}:*"]}, {"Sid": "ParameterStorePermissions", "Effect": "Allow", "Action": ["ssm:PutParameter", "ssm:GetParameter", "ssm:GetParameters", "ssm:GetParameterHistory", "ssm:GetParametersByPath", "ssm:DeleteParameter", "ssm:DeleteParameters", "ssm:LabelParameterVersion", "ssm:AddTagsToResource", "ssm:RemoveTagsFromResource"], "Resource": ["arn:aws:ssm:us-east-1:${current_account_id}:parameter/airflow/confluent", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/prod/redshift/mktg_dbadmin/password", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/interfacelayer/sfmcLtPass", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/interfacelayer/sfmcPass", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/interfacelayer/SFMC/clientSecret", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/interfacelayer/SFMCLT/clientSecret", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/platform_sfmc_sfly_inc", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/redshift_secret", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/msk_connection", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/prospects_collect_consumer/*", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/beacon-pixel/s2s_api_key", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/beacon-pixel/s2s_auth_token", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/airflow/databricks_policy_id", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/airflow/new_databricks_policy_id", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/airflow/databricks_token", "arn:aws:ssm:us-east-1:${current_account_id}:parameter/airflow/signalfx_token"]}, {"Sid": "AllowSpecificKinesisActions", "Effect": "Allow", "Action": ["kinesis:<PERSON><PERSON><PERSON><PERSON>", "kinesis:PutRecords"], "Resource": ["arn:aws:kinesis:us-east-1:${current_account_id}:stream/kafka_poc", "arn:aws:kinesis:us-east-1:${current_account_id}:stream/mktg-action-iq-events", "arn:aws:kinesis:us-east-1:${current_account_id}:stream/mktg-i1-incoming-events", "arn:aws:kinesis:us-east-1:${current_account_id}:stream/mktg-public-events-stream-prod", "arn:aws:kinesis:us-east-1:${current_account_id}:stream/prod-project-service-stream", "arn:aws:kinesis:us-east-1:${current_account_id}:stream/tlprod_scoreAllMomentsV2_update_stream"]}, {"Sid": "Allowkinesisanalytics", "Effect": "Allow", "Action": ["kinesisanalytics:*"], "Resource": "*"}, {"Sid": "AllowEcsOperations", "Effect": "Allow", "Action": ["ecs:RunTask", "ecs:StopTask"], "Resource": ["arn:aws:ecs:us-east-1:${current_account_id}:cluster/ecs-prod-cluster-v1", "arn:aws:ecs:us-east-1:${current_account_id}:cluster/standard-app-prod-common", "arn:aws:ecs:us-east-1:${current_account_id}:cluster/default", "arn:aws:ecs:us-east-1:${current_account_id}:cluster/rde-links-tester"]}, {"Sid": "AllowFirehose", "Effect": "Allow", "Action": ["firehose:PutRecord", "firehose:PutRecordBatch", "firehose:DeleteDeliveryStream"], "Resource": "arn:aws:firehose:us-east-1:${current_account_id}:deliverystream/mktg-incoming-events-firehose-prod"}]}