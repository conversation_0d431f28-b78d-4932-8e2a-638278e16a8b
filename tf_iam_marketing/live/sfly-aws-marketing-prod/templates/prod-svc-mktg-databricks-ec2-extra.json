{"Version": "2012-10-17", "Statement": [{"Sid": "KafkaClusterConnectAndDescribe", "Effect": "Allow", "Action": ["kafka-cluster:Connect", "kafka-cluster:DescribeCluster"], "Resource": ["arn:aws:kafka:us-east-1:997534182922:cluster/msk-infra-prod/5f07b703-6362-40e4-921c-8dfcbbb676d8-11"]}, {"Sid": "KafkaClusterReadDataAndDescribeTopic", "Effect": "Allow", "Action": ["kafka-cluster:ReadData", "kafka-cluster:DescribeTopic"], "Resource": ["arn:aws:kafka:us-east-1:997534182922:topic/msk-infra-prod/5f07b703-6362-40e4-921c-8dfcbbb676d8-11/sfly.media.photo-analysis-events.fct", "arn:aws:kafka:us-east-1:997534182922:topic/msk-infra-prod/5f07b703-6362-40e4-921c-8dfcbbb676d8-11/sfly.apc.photo-analysis-events.fct"]}, {"Sid": "KafkaClusterDescribeGroup", "Effect": "Allow", "Action": ["kafka-cluster:AlterGroup", "kafka-cluster:DescribeGroup"], "Resource": ["arn:aws:kafka:us-east-1:997534182922:group/msk-infra-prod/5f07b703-6362-40e4-921c-8dfcbbb676d8-11/mktg_photos_databricks"]}, {"Sid": "GlueReadOnlyTableAndPartitions", "Effect": "Allow", "Action": ["glue:GetTables", "glue:GetTable", "glue:GetPartitions", "glue:GetPartition", "glue:GetDatabase*"], "Resource": ["arn:aws:glue:us-east-1:************:catalog", "arn:aws:glue:us-east-1:************:database/dwh", "arn:aws:glue:us-east-1:************:table/dwh/*", "arn:aws:glue:us-east-1:************:database/dwh_bo", "arn:aws:glue:us-east-1:************:table/dwh_bo/*"]}, {"Sid": "Glue", "Effect": "Allow", "Action": ["glue:GetDatabase", "glue:GetDatabase*", "glue:UpdateDatabase", "glue:CreateTable", "glue:GetTables", "glue:GetTable", "glue:DeleteTable", "glue:UpdateTable", "glue:GetPartitions", "glue:GetPartition", "glue:UpdatePartition", "glue:UpdatePartitions", "glue:BatchCreatePartition", "glue:DeletePartition", "glue:GetUserDefinedFunctions"], "Resource": ["arn:aws:glue:us-east-1:${current_account_id}:catalog", "arn:aws:glue:us-east-1:${current_account_id}:database", "arn:aws:glue:us-east-1:${current_account_id}:database/mktg_*", "arn:aws:glue:us-east-1:${current_account_id}:database/default", "arn:aws:glue:us-east-1:${current_account_id}:database/delta_rde", "arn:aws:glue:us-east-1:${current_account_id}:table/*"]}, {"Sid": "AllowAccess2PhotoccinoImportBucketProd", "Effect": "Allow", "Action": ["s3:GetObject", "s3:ListBucket"], "Resource": ["arn:aws:s3:::prod-memories-export", "arn:aws:s3:::prod-memories-export/*"]}, {"Sid": "DynamoMemoriesAccess", "Action": ["dynamodb:GetItem", "dynamodb:BatchGetItem", "dynamodb:PutItem", "dynamodb:UpdateItem", "dynamodb:BatchWriteItem", "dynamodb:DeleteItem"], "Effect": "Allow", "Resource": ["arn:aws:dynamodb:us-east-1:${current_account_id}:table/mktg-memories"]}]}