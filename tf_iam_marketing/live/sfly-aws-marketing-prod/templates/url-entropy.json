{"Statement": [{"Action": ["secretsmanager:GetSecretValue", "secretsmanager:Describe<PERSON><PERSON><PERSON>"], "Effect": "Allow", "Resource": ["arn:aws:secretsmanager:us-east-1:141066338864:secret:/media/prod/timeline*", "arn:aws:secretsmanager:us-east-1:361614443039:secret:/media/stage/timeline*", "arn:aws:secretsmanager:us-east-1:326947223243:secret:/media/beta/timeline*", "arn:aws:secretsmanager:us-east-1:326947223243:secret:/media/dev/timeline*", "arn:aws:secretsmanager:us-east-1:326947223243:secret:/media/kappa/timeline*"]}, {"Action": "kms:Decrypt", "Effect": "Allow", "Resource": ["arn:aws:kms:us-east-1:141066338864:key/d6a69b8f-00fb-4f06-b49e-fa0122fdf837", "arn:aws:kms:us-east-1:326947223243:key/40ae9058-fe83-4d50-bb9e-901943d90d6e", "arn:aws:kms:us-east-1:361614443039:key/f732e66c-09d6-4add-86e7-ccaf672d6412"]}], "Version": "2012-10-17"}