{"Version": "2012-10-17", "Statement": [{"Sid": "DynamoDBAccess", "Effect": "Allow", "Action": ["dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:Query"], "Resource": ["arn:aws:dynamodb:us-east-1:${current_account_id}:table/${stage_name}-controller-live-analytics-tools"]}, {"Sid": "ECSAccess", "Effect": "Allow", "Action": ["ecs:UpdateService", "ecs:ListTasks", "ecs:StopTask", "ecs:DescribeTasks"], "Resource": ["arn:aws:ecs:us-east-1:${current_account_id}:cluster/ecs-${stage_name}-cluster-v1", "arn:aws:ecs:us-east-1:${current_account_id}:service/ecs-${stage_name}-cluster-v1/ecs-${stage_name}-RecentlyViewed-live-analytics-tools", "arn:aws:ecs:us-east-1:${current_account_id}:service/ecs-${stage_name}-cluster-v1/ecs-${stage_name}-ProspectsCollect-live-analytics-tools", "arn:aws:ecs:us-east-1:${current_account_id}:service/ecs-${stage_name}-cluster-v1/ecs-${stage_name}-PlatformConsumer-live-analytics-tools", "arn:aws:ecs:us-east-1:${current_account_id}:task/ecs-${stage_name}-cluster-v1/*"]}]}