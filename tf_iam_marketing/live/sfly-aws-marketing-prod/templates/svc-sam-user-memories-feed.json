{"Version": "2012-10-17", "Statement": [{"Sid": "DynamoSegments", "Action": ["dynamodb:GetItem", "dynamodb:BatchGetItem"], "Effect": "Allow", "Resource": ["arn:aws:dynamodb:us-east-1:${current_account_id}:table/mktg-tmp*"]}, {"Effect": "Allow", "Action": ["lambda:InvokeFunction"], "Resource": ["arn:aws:lambda:us-east-1:${current_account_id}:function:user-memories-feed-prod"]}, {"Sid": "LambdaBasic", "Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": ["arn:aws:logs:us-east-1:${current_account_id}:log-group:/aws/lambda/user-memories-feed-prod*", "arn:aws:logs:us-east-1:${current_account_id}:log-group:/aws/lambda/user-memories-feed-v2-prod"]}, {"Sid": "DynamoMemoriesAccess", "Action": ["dynamodb:GetItem", "dynamodb:BatchGetItem"], "Effect": "Allow", "Resource": ["arn:aws:dynamodb:us-east-1:${current_account_id}:table/mktg-memories"]}]}