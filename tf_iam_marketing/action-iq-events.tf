module "policy_action_iq" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "action-iq-events-stream"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/action-iq-events-stream.json")
}

module "role_action_iq" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias    = data.aws_iam_account_alias.current.account_alias
  account_id       = data.aws_caller_identity.current.account_id
  role_name_prefix = local.role_name_prefix_services
  role_name        = "action-iq-events-stream"

  policy_names = [
    module.policy_action_iq.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
  ]

  assume_role = "apigateway_assume"

}
