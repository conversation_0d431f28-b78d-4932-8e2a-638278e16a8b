variable "region" {
  default = "us-east-1"
}

variable "databricks_account_id" {
  default = "************"
}

variable "databricks_account_map" {
  description = "A map from environment the databricks external ID"
  type        = map(any)
  default = {
    dev     = ["c3402be8-722c-451f-b8b1-116a4a22b4f1", "d3746b40-fd8f-4218-81ea-47b40d249ea8"]
    preprod = ["c3402be8-722c-451f-b8b1-116a4a22b4f1", "d3746b40-fd8f-4218-81ea-47b40d249ea8"]
    prod    = ["0981a34d-2548-47f9-98fa-3b0db6ec111e", "d3746b40-fd8f-4218-81ea-47b40d249ea8"]
  }
}

variable "dwh_s3_key" {
  description = "A map from environment the databricks external ID"
  type        = map(string)
  default = {
    dev     = "90fa8694-ec36-408d-804b-ff0491e3a30c"
    preprod = "90fa8694-ec36-408d-804b-ff0491e3a30c"
    prod    = "f6dff027-6286-4de4-8503-e92c31d04b88"
  }
}

variable "dwh_account" {
  description = "A map from environment the databricks external ID"
  type        = map(string)
  default = {
    dev     = "************"
    preprod = "************"
    prod    = "************"
  }
}

variable "mktg_s3_key" {
  description = "A map from environment the databricks external ID"
  type        = map(string)
  default = {
    dev     = "f8e0334f-7f3e-4bc8-809f-25e84d1b1a9c"
    preprod = "f8e0334f-7f3e-4bc8-809f-25e84d1b1a9c"
    prod    = "d1a11a2c-941b-4e5b-a6d6-1c2033ccec9d"
  }
}

variable "actioniq_s3_key" {
  description = "A map of actioniq kms key to envs"
  type        = map(string)
  default = {
    dev     = "ae49aa44-19c3-497b-87e5-c955db5748f1"
    preprod = "ae49aa44-19c3-497b-87e5-c955db5748f1"
    prod    = "af4c8ac2-2a06-4ec5-aa1b-8e113967f5f4"
  }
}

variable "mktg_account" {
  description = "A map from environment to the marketing account id"
  type        = map(string)
  default = {
    dev     = "************"
    preprod = "************"
    prod    = "************"
  }
}

variable "project-service-account" {
  description = "A map from environment to the project account id"
  type        = map(string)
  default = {
    dev     = "************"
    preprod = "************"
    prod    = "************"
  }
}


variable "photoccino_account" {
  description = "A map from environment the photoccino external ID"
  type        = map(string)
  default = {
    dev     = "************"
    preprod = "************"
    prod    = "************"
  }
}

variable "databricks_reports_externalid_map" {
  description = "A map from environment to external id for Databricks billable usage reports"
  type        = map(string)
  default = {
    dev     = "5f6d1bf0-15b2-434b-bce4-142e38afefa1"
    preprod = "5f6d1bf0-15b2-434b-bce4-142e38afefa1"
    prod    = "d0df15b9-c344-4a78-9652-82404c2ba686"
  }
}

output "databricks_externalID_type" {
  value = var.databricks_account_map[var.stage_name]
}

output "databricks_reports_externalid" {
  value = var.databricks_reports_externalid_map[var.stage_name]
}

variable "cis_role_session_duration" {
  description = "The maximum session duration (in seconds) that you want to set for the specified CIS role (people). Default and max time is 12 hours (43200)"
  default     = "43200"
}

variable "role_name_prefix_people" {
  type        = string
  description = "Optional prefix for the logical role name when it is a federated people role.  Many users will find it useful to prefix the role name with the account alias or environment name; Default: '' (empty string)"
  default     = ""
}
variable "lakeformation_central_account" {
  description = "A map from environment to LakeFormation Central Account ID"
  type        = map(string)
  default = {
    dev     = "************"
    preprod = "************"
    prod    = "************"
  }
}

variable "enable_password_policy" {
  description = "enable iam account password policy"
  type    = bool
  default = true
}