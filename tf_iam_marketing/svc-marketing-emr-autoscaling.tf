module "service_policy_svc_marketing_emr_autoscaling" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "marketing-emr-autoscaling"

  template = file("${path.module}/templates/svc-marketing-emr-autoscaling.json")
}

module "service_role_marketing_emr_autoscaling" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix   = local.role_name_prefix_services
  role_name          = "marketing-emr-autoscaling"
  assume_role_policy = data.template_file.assume_policy_autoscaling_emr.rendered

  policy_names = [
    module.service_policy_svc_marketing_emr_autoscaling.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
  ]
}

data "template_file" "assume_policy_autoscaling_emr" {
  template = file("${path.module}/templates/assume_role_policy/application_autoscaling_elasticmapreduce.json")
}

