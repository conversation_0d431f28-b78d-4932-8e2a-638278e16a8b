locals {
  business_unit = "Consumer"
  managed_by    = "marketing-platform"
  env           = "stage"
  region        = "us-east-1"
}

provider "aws" {
  region = local.region
}

terraform {
  backend "s3" {
    bucket         = "sfly-aws-marketing-preprod-cm-data"
    key            = "infra/terraform/sfly-aws-marketing-preprod/us-east-1/app-data/preprod/marketing-platform/ProspectsCollect/ProspectsCollect.state"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "tf-state-lock"
  }
}

module "lambda_cloudwatch_to_s3" {
  source = "**************:sflyinc-shutterfly/tf_cloudwatch_to_s3.git?ref=v2.1.0"

  log_groups           = ["ecs-preprod-ProspectsCollect-live-analytics-tools"]
  app                  = "ProspectsCollect-live-analytics-tools"
  env                  = local.env
  lambda_function_name = "ecs-preprod-ProspectsCollect-live-analytics-tools-log-forwarder"
  description          = "Forward logs from ecs-preprod-prospects-collect-live-analytics-tool to S3"
  retention_in_days    = 7
  region               = local.region
  managed_by           = local.managed_by
  business_unit        = local.business_unit

  ## S3 bucket prefix where the cloudwatch logs will be shipped to
  s3_key_prefix = "cloudwatch/ecs/sfly/marketingplatform/ecs-preprod-ProspectsCollect-live-analytics-tools/application"
}

module "prospects_to_dwh" {
  source                     = "**************:sflyinc-shutterfly/tf_sqs_queue.git?ref=v1.0.4"
  queue_name                 = "sfly-aws-marketing-preprod-prospects-to-dwh"
  app                        = "prospects-to-dwh"
  business_unit              = local.business_unit
  env                        = local.env
  logical_queue_name         = ""
  owner                      = local.managed_by
  visibility_timeout_seconds = 30
  message_retention_seconds  = 345600
  receive_wait_time_seconds  = 0
  max_message_size           = 262144
}
