# DynamoDB Table: mktg-tmp-user-activity-pixel

## Overview
This DynamoDB table is designed to track user activity pixels with automatic data expiration after 7 days.

## Table Specifications

### Table Configuration
- **Table Name**: `mktg-tmp-user-activity-pixel`
- **Billing Mode**: Pay-per-request (on-demand)
- **Hash Key**: `user_id` (String)
- **TTL**: 7 days (automatic deletion)

### Schema
| Attribute | Type | Description | Required |
|-----------|------|-------------|----------|
| `user_id` | String | Primary key - unique user identifier | Yes |
| `landing_time` | Number | Unix timestamp when user landed | No |
| `landing_page` | String | URL of the landing page | No |
| `ttl` | Number | Unix timestamp for TTL expiration | Yes* |

*Required for TTL functionality

## TTL (Time To Live) Configuration
- **TTL Attribute**: `ttl`
- **Expiration**: 7 days from creation
- **Automatic Cleanup**: Items are automatically deleted by DynamoDB when TTL expires

### Setting TTL Values
When inserting items, you MUST include a separate `ttl` attribute (different from `landing_time`):
```
ttl = current_timestamp + (7 * 24 * 60 * 60)  // 7 days in seconds
```

Example:
```javascript
const currentTimestamp = Math.floor(Date.now() / 1000);
const ttlValue = currentTimestamp + (7 * 24 * 60 * 60); // 7 days from now
```

**Important**: The `ttl` field is separate from `landing_time`. Both should be included in your records.

## Usage Examples

### Insert Item (AWS SDK v3 - JavaScript)
```javascript
import { DynamoDBClient, PutItemCommand } from "@aws-sdk/client-dynamodb";

const client = new DynamoDBClient({ region: "us-east-1" });

const params = {
  TableName: "mktg-tmp-user-activity-pixel",
  Item: {
    user_id: { S: "user123" },
    landing_time: { N: Math.floor(Date.now() / 1000).toString() },
    landing_page: { S: "https://example.com/landing" },
    ttl: { N: (Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60)).toString() }
  }
};

await client.send(new PutItemCommand(params));
```

### Query Item
```javascript
import { DynamoDBClient, GetItemCommand } from "@aws-sdk/client-dynamodb";

const params = {
  TableName: "mktg-tmp-user-activity-pixel",
  Key: {
    user_id: { S: "user123" }
  }
};

const result = await client.send(new GetItemCommand(params));
```

## Deployment

### Deploy the Table
```bash
# Deploy only the DynamoDB table
terraform plan -target=module.user_activity_pixel
terraform apply -target=module.user_activity_pixel
```

### Verify Deployment
```bash
# Check if table exists
aws dynamodb describe-table --table-name mktg-tmp-user-activity-pixel --region us-east-1
```

## Important Notes

1. **TTL Behavior**: Items may not be deleted immediately when TTL expires. DynamoDB typically deletes expired items within 48 hours.

2. **Non-Key Attributes**: `landing_time` and `landing_page` are not defined in the Terraform schema because DynamoDB is schema-less for non-key attributes.

3. **Billing**: Using pay-per-request billing mode, so you only pay for actual read/write operations.

4. **Monitoring**: Consider setting up CloudWatch alarms for table metrics if needed.

## Troubleshooting

### Common Issues
- **TTL not working**: Ensure the `ttl` attribute is set as a Unix timestamp (number)
- **Access denied**: Verify IAM permissions for DynamoDB operations
- **Item not found**: Check if item has expired due to TTL

### Useful AWS CLI Commands
```bash
# List tables
aws dynamodb list-tables --region us-east-1

# Describe table
aws dynamodb describe-table --table-name mktg-tmp-user-activity-pixel --region us-east-1

# Scan table (use carefully in production)
aws dynamodb scan --table-name mktg-tmp-user-activity-pixel --region us-east-1 --max-items 10
```
