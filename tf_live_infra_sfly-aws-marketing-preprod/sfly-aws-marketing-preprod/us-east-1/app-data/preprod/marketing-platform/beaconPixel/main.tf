locals {
  business_unit = "Consumer"
  managed_by    = "marketing-platform"
  env           = "stage"
  region        = "us-east-1"
}

provider "aws" {
  region = local.region
}

terraform {
  backend "s3" {
    bucket         = "sfly-aws-marketing-preprod-cm-data"
    key            = "infra/terraform/sfly-aws-marketing-preprod/us-east-1/app-data/preprod/marketing-platform/beaconPixel/beaconPixel.state"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "tf-state-lock"
  }
}

module "lambda_cloudwatch_to_s3" {

  source = "**************:sflyinc-shutterfly/tf_cloudwatch_to_s3.git?ref=v2.1.0"

  log_groups           = ["/aws/lambda/beacon-pixel-v2-dev"]
  app                  = "beaconPixel"
  env                  = local.env
  lambda_function_name = "beacon-pixel-v2-dev-log-forwarder"
  description          = "Forward logs from beacon-pixel-v2-dev to S3"
  retention_in_days    = 7
  region               = local.region
  managed_by           = local.managed_by
  business_unit        = local.business_unit

  ## S3 bucket prefix where the cloudwatch logs will be shipped to
  s3_key_prefix = "cloudwatch/lambda/sfly/marketingplatform/beacon-pixel-v2-dev/application"
}
module "user_activity_pixel" {
  source = "**************:sflyinc-shutterfly/tf_dynamodb.git?ref=v2.1.1"

  table_name     = "mktg-tmp-user-activity-pixel"
  hash_key       = "user_id"
  stream_enabled = false
  billing_mode   = "PAY_PER_REQUEST"

  enable_deletion_protection    = false
  enable_point_in_time_recovery = false
  enable_encryption             = false

  # TTL configuration - items will be automatically deleted after 7 days
  ttl = [
    {
      attribute_name = "ttl"
      enabled        = true
    }
  ]

  managed_by    = local.managed_by
  environment   = local.env
  role          = "user_activity_pixel"
  app           = "beacon-pixel"
  business_unit = local.business_unit

  # Only define key attributes for DynamoDB
  # Non-key attributes (landing_time, landing_page) are added dynamically
  attributes = [
    {
      name = "user_id"
      type = "S"
    }
  ]
}