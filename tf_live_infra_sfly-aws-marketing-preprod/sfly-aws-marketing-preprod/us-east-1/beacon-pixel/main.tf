locals {
  business_unit     = "consumer"
  managed_by        = "marketing-platform"
  account_name      = "sfly-aws-marketing-preprod"
  state_bucket      = "${local.account_name}-cm-data"
  env               = "dev"
  log_bucket        = "sfly-aws-mktg-${local.env}-log"
  region            = "us-east-1"
  steam_bucket_name = "${local.account_name}-streams-data"
  application       = "beacon_pixel"
}

provider "aws" {
  region = local.region
}

terraform {
  backend "s3" {
    bucket         = "sfly-aws-marketing-preprod-cm-data"
    key            = "infra/terraform/sfly-aws-marketing-preprod/us-east-1/app-data/preprod/marketing-platform/beacon-pixel/beacon-pixel.state"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "tf-state-lock"
  }
}

resource "aws_s3_bucket" "sfly-aws-mktg-stream-data" {
  bucket = local.steam_bucket_name
  acl    = "private"

  versioning {
    enabled = true
  }


  tags = {
    BusinessUnit = local.business_unit
    App          = local.application
    ManagedBy    = local.managed_by
    Environment  = local.env
    Provisioner  = "Terraform"
    Application  = local.application
    Version      = "v1.0"
  }
}

resource "aws_s3_bucket" "sfly-aws-mktg-stream-data-preprod" {
  bucket = "sfly-aws-mktg-dev-streams-data"
  acl    = "private"

  versioning {
    enabled = true
  }


  tags = {
    BusinessUnit = local.business_unit
    App          = local.application
    ManagedBy    = local.managed_by
    Environment  = local.env
    Provisioner  = "Terraform"
    Application  = local.application
    Version      = "v1.0"
  }
}

