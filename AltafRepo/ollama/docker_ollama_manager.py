#!/usr/bin/env python3
"""
Docker Ollama Manager
Manage Ollama models running in Docker without local installation
"""

import subprocess
import sys
import argparse

def run_command(cmd, timeout=60):
    """Run a shell command and return result"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def check_docker():
    """Check if Docker is available"""
    success, _, _ = run_command("docker --version")
    return success

def get_ollama_containers():
    """Get running Ollama containers"""
    success, stdout, _ = run_command("docker ps --filter name=ollama --format '{{.Names}}'")
    if success and stdout.strip():
        return stdout.strip().split('\n')
    return []

def start_ollama_container(gpu=False):
    """Start Ollama Docker container"""
    container_name = "ollama"
    
    # Check if container already exists
    success, stdout, _ = run_command(f"docker ps -a --filter name={container_name} --format '{{.Names}}'")
    if success and container_name in stdout:
        print(f"Container '{container_name}' already exists. Starting it...")
        success, _, stderr = run_command(f"docker start {container_name}")
        if success:
            print("✅ Container started!")
            return True
        else:
            print(f"❌ Failed to start container: {stderr}")
            return False
    
    # Create new container
    if gpu:
        cmd = f"docker run -d --gpus all --name {container_name} -p 11434:11434 ollama/ollama"
        print("🚀 Starting Ollama with GPU support...")
    else:
        cmd = f"docker run -d --name {container_name} -p 11434:11434 ollama/ollama"
        print("🚀 Starting Ollama (CPU only)...")
    
    success, stdout, stderr = run_command(cmd)
    if success:
        print(f"✅ Ollama container started! Container ID: {stdout.strip()}")
        return True
    else:
        print(f"❌ Failed to start container: {stderr}")
        return False

def list_models(container_name="ollama"):
    """List models in Docker container"""
    success, stdout, stderr = run_command(f"docker exec {container_name} ollama list")
    if success:
        print("📋 Available models:")
        print(stdout)
        return True
    else:
        print(f"❌ Failed to list models: {stderr}")
        return False

def pull_model(model_name, container_name="ollama"):
    """Pull a model in Docker container"""
    print(f"📥 Pulling model '{model_name}'...")
    success, stdout, stderr = run_command(f"docker exec {container_name} ollama pull {model_name}", timeout=600)
    if success:
        print(f"✅ Model '{model_name}' installed successfully!")
        return True
    else:
        print(f"❌ Failed to pull model: {stderr}")
        return False

def remove_model(model_name, container_name="ollama"):
    """Remove a model from Docker container"""
    print(f"🗑️  Removing model '{model_name}'...")
    success, stdout, stderr = run_command(f"docker exec {container_name} ollama rm {model_name}")
    if success:
        print(f"✅ Model '{model_name}' removed successfully!")
        return True
    else:
        print(f"❌ Failed to remove model: {stderr}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Manage Ollama in Docker")
    parser.add_argument("action", choices=["start", "list", "pull", "remove", "status"], 
                       help="Action to perform")
    parser.add_argument("--model", help="Model name for pull/remove actions")
    parser.add_argument("--gpu", action="store_true", help="Use GPU support when starting")
    parser.add_argument("--container", default="ollama", help="Container name (default: ollama)")
    
    args = parser.parse_args()
    
    if not check_docker():
        print("❌ Docker is not available. Please install Docker first.")
        sys.exit(1)
    
    if args.action == "start":
        start_ollama_container(gpu=args.gpu)
    
    elif args.action == "status":
        containers = get_ollama_containers()
        if containers:
            print(f"✅ Running Ollama containers: {', '.join(containers)}")
        else:
            print("❌ No Ollama containers running")
    
    elif args.action == "list":
        containers = get_ollama_containers()
        if not containers:
            print("❌ No Ollama containers running. Start one first with: python docker_ollama_manager.py start")
            sys.exit(1)
        list_models(containers[0])
    
    elif args.action == "pull":
        if not args.model:
            print("❌ Please specify a model name with --model")
            sys.exit(1)
        containers = get_ollama_containers()
        if not containers:
            print("❌ No Ollama containers running. Start one first with: python docker_ollama_manager.py start")
            sys.exit(1)
        pull_model(args.model, containers[0])
    
    elif args.action == "remove":
        if not args.model:
            print("❌ Please specify a model name with --model")
            sys.exit(1)
        containers = get_ollama_containers()
        if not containers:
            print("❌ No Ollama containers running")
            sys.exit(1)
        remove_model(args.model, containers[0])

if __name__ == "__main__":
    main()
