import requests
import json

def test_ollama_connection():
    """Test if Ollama server is running"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            return True, response.json()
        else:
            return False, f"Server responded with status {response.status_code}"
    except requests.exceptions.RequestException as e:
        return False, f"Connection error: {e}"

def generate_text(model_name, prompt):
    """Generate text using Ollama API"""
    ollama_url = "http://localhost:11434/api/generate"

    payload = {
        "model": model_name,
        "prompt": prompt,
        "stream": False  # Get complete response at once
    }

    try:
        response = requests.post(ollama_url, json=payload, timeout=30)
        response.raise_for_status()
        return True, response.json()
    except requests.exceptions.RequestException as e:
        return False, f"Request error: {e}"

def main():
    print("Testing Ollama connection...")

    # Test connection and get available models
    is_connected, result = test_ollama_connection()

    if not is_connected:
        print(f"❌ Ollama server is not accessible: {result}")
        print("\nTo fix this:")
        print("1. Make sure Ollama is installed: https://ollama.ai/")
        print("2. Start Ollama server: ollama serve")
        print("3. Pull a model: ollama pull llama3.2")
        return

    print("✅ Ollama server is running!")

    # Check available models
    models = result.get('models', [])
    if not models:
        print("\n❌ No models found!")
        print("Install a model first:")
        print("  ollama pull llama3.2")
        print("  ollama pull codellama")
        print("  ollama pull mistral")
        return

    print(f"\n📋 Available models ({len(models)}):")
    for model in models:
        print(f"  - {model['name']}")

    # Use the first available model
    model_name = models[0]['name']
    prompt = "Tell me a joke!"

    print(f"\n🤖 Testing with model: {model_name}")
    print(f"📝 Prompt: {prompt}")

    success, result = generate_text(model_name, prompt)

    if success:
        print("\n✅ Response from Ollama:")
        print("-" * 50)
        print(result.get('response', 'No response text found'))
        print("-" * 50)
    else:
        print(f"\n❌ Error generating text: {result}")

if __name__ == "__main__":
    main()
