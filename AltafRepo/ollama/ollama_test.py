import requests
import subprocess

def check_docker_ollama():
    """Check if Ollama Docker container is running"""
    try:
        result = subprocess.run(['docker', 'ps', '--filter', 'name=ollama', '--format', '{{.Names}}'],
                              capture_output=True, text=True, timeout=10)
        containers = result.stdout.strip().split('\n')
        running_containers = [c for c in containers if c and 'ollama' in c]
        return len(running_containers) > 0, running_containers
    except Exception as e:
        return False, f"Error checking Docker: {e}"

def test_ollama_connection():
    """Test if Ollama server is running (Docker or local)"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            return True, response.json()
        else:
            return False, f"Server responded with status {response.status_code}"
    except requests.exceptions.RequestException as e:
        return False, f"Connection error: {e}"

def generate_text(model_name, prompt):
    """Generate text using Ollama API"""
    ollama_url = "http://localhost:11434/api/generate"

    payload = {
        "model": model_name,
        "prompt": prompt,
        "stream": False  # Get complete response at once
    }

    try:
        response = requests.post(ollama_url, json=payload, timeout=30)
        response.raise_for_status()
        return True, response.json()
    except requests.exceptions.RequestException as e:
        return False, f"Request error: {e}"

def install_model_in_docker(container_name, model_name):
    """Install a model in Docker container"""
    try:
        print(f"📥 Installing {model_name} in Docker container...")
        result = subprocess.run(['docker', 'exec', container_name, 'ollama', 'pull', model_name],
                              capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            return True, "Model installed successfully"
        else:
            return False, result.stderr
    except Exception as e:
        return False, f"Error installing model: {e}"

def main():
    print("🐳 Docker + Ollama Setup")
    print("=" * 40)

    # Check if Docker Ollama is running
    docker_running, containers = check_docker_ollama()

    if not docker_running:
        print("❌ No Ollama Docker container found!")
        print("\n🚀 To start Ollama in Docker:")
        print("docker run -d --name ollama -p 11434:11434 ollama/ollama")
        print("\nOr if you want GPU support:")
        print("docker run -d --gpus all --name ollama -p 11434:11434 ollama/ollama")
        return

    print(f"✅ Docker containers running: {', '.join(containers)}")
    container_name = containers[0]  # Use first container

    # Test connection
    is_connected, result = test_ollama_connection()

    if not is_connected:
        print(f"❌ Cannot connect to Ollama API: {result}")
        print(f"\n🔧 Make sure container '{container_name}' has port 11434 exposed")
        print("Check with: docker ps")
        return

    print("✅ Ollama API is accessible!")

    # Check available models
    models = result.get('models', [])
    if not models:
        print("\n❌ No models found in Docker container!")
        print("\n📥 Available models to install:")
        suggested_models = ["llama3.2", "llama3.2:1b", "codellama", "mistral", "phi3"]
        for i, model in enumerate(suggested_models, 1):
            print(f"  {i}. {model}")

        print(f"\n🛠️  To install a model in Docker:")
        print(f"docker exec {container_name} ollama pull llama3.2")

        # Offer to install a model
        try:
            choice = input("\nWould you like to install llama3.2:1b (small, fast model)? [y/N]: ").strip().lower()
            if choice in ['y', 'yes']:
                success, msg = install_model_in_docker(container_name, "llama3.2:1b")
                if success:
                    print("✅ Model installed! Re-running test...")
                    # Refresh models list
                    _, result = test_ollama_connection()
                    models = result.get('models', [])
                else:
                    print(f"❌ Failed to install model: {msg}")
                    return
            else:
                return
        except KeyboardInterrupt:
            print("\n👋 Cancelled by user")
            return

    if not models:
        print("❌ Still no models available")
        return

    print(f"\n📋 Available models ({len(models)}):")
    for model in models:
        print(f"  - {model['name']}")

    # Use the first available model
    model_name = models[0]['name']
    prompt = "Tell me a joke!"

    print(f"\n🤖 Testing with model: {model_name}")
    print(f"📝 Prompt: {prompt}")

    success, result = generate_text(model_name, prompt)

    if success:
        print("\n✅ Response from Ollama (Docker):")
        print("-" * 50)
        print(result.get('response', 'No response text found'))
        print("-" * 50)
        print(f"\n🎉 Success! You can now use Ollama models from Docker without local installation!")
    else:
        print(f"\n❌ Error generating text: {result}")

if __name__ == "__main__":
    main()
