import boto3
import pandas as pd
from tabulate import tabulate

elbv2 = boto3.client('elbv2')
ec2 = boto3.client('ec2')

albs = elbv2.describe_load_balancers()['LoadBalancers']
data = []

for alb in albs:
    alb_name = alb['LoadBalancerName']
    alb_arn = alb['LoadBalancerArn']
    alb_dns = alb['DNSName']
    alb_scheme = alb['Scheme']
    alb_sgs = alb.get('SecurityGroups', [])

    target_groups = elbv2.describe_target_groups(LoadBalancerArn=alb_arn)['TargetGroups']

    for tg in target_groups:
        tg_name = tg['TargetGroupName']
        tg_arn = tg['TargetGroupArn']
        target_health = elbv2.describe_target_health(TargetGroupArn=tg_arn)

        for target in target_health['TargetHealthDescriptions']:
            target_id = target['Target']['Id']

            if target_id.startswith('i-'):
                try:
                    instance_desc = ec2.describe_instances(InstanceIds=[target_id])
                    instance = instance_desc['Reservations'][0]['Instances'][0]
                    tags = instance.get('Tags', [])
                    name_tag = next((tag['Value'] for tag in tags if tag['Key'] == 'Name'), 'N/A')
                    instance_sgs = instance['SecurityGroups']
                    instance_sg_names = ', '.join(sg['GroupName'] for sg in instance_sgs)
                except Exception as e:
                    name_tag = f"Error: {str(e)}"
                    instance_sg_names = 'Unknown'
            else:
                name_tag = 'N/A (IP Target)'
                instance_sg_names = 'N/A'

            data.append([
                alb_name,
                alb_dns,
                alb_scheme,
                tg_name,
                target_id,
                'Instance' if target_id.startswith('i-') else 'IP',
                name_tag,
                instance_sg_names,
                ', '.join(alb_sgs)
            ])

# Column headers
headers = [
    'ALB Name', 'ALB DNS', 'ALB Scheme', 'Target Group', 'Target ID',
    'Target Type', 'Instance Name', 'Instance SGs', 'ALB SGs'
]

# Print box-style table
print("\n✅ ALB Resource Mapping:\n")
print(tabulate(data, headers=headers, tablefmt="fancy_grid"))

# Collect all unique SG IDs from ALBs and instances
sg_ids = set()

for row in data:
    # Extract SGs (can be names, so we will have to resolve SGs by name too if needed)
    # But boto3 works with SG IDs, so we should collect actual SG IDs
    # Here we assume ALB SGs are passed as IDs, which they are
    alb_sg_ids = row[-1].split(', ')
    sg_ids.update(alb_sg_ids)

    instance_sgs = row[-2]
    if instance_sgs != 'N/A':
        # In a real case, we might need mapping from SG name to ID.
        # But for now, assuming names and IDs are the same — or ignore non-ID
        pass  # Or enhance if needed

# Get SG details from AWS
sg_details = []
for sg_id in sg_ids:
    try:
        response = ec2.describe_security_groups(GroupIds=[sg_id])
        sg = response['SecurityGroups'][0]
        sg_name = sg.get('GroupName', sg_id)

        # Inbound rules
        for rule in sg['IpPermissions']:
            from_port = rule.get('FromPort', 'All')
            to_port = rule.get('ToPort', 'All')
            protocol = rule.get('IpProtocol', 'All')
            sources = []

            for ip_range in rule.get('IpRanges', []):
                sources.append(ip_range.get('CidrIp'))

            for sg_source in rule.get('UserIdGroupPairs', []):
                sources.append(sg_source.get('GroupId'))

            for src in sources or ['All']:
                sg_details.append([
                    sg_name, sg['GroupId'], 'Inbound', protocol, from_port, to_port, src
                ])

        # Outbound rules
        for rule in sg['IpPermissionsEgress']:
            from_port = rule.get('FromPort', 'All')
            to_port = rule.get('ToPort', 'All')
            protocol = rule.get('IpProtocol', 'All')
            destinations = []

            for ip_range in rule.get('IpRanges', []):
                destinations.append(ip_range.get('CidrIp'))

            for sg_dest in rule.get('UserIdGroupPairs', []):
                destinations.append(sg_dest.get('GroupId'))

            for dest in destinations or ['All']:
                sg_details.append([
                    sg_name, sg['GroupId'], 'Outbound', protocol, from_port, to_port, dest
                ])

    except Exception as e:
        sg_details.append([f"Error retrieving SG: {sg_id}", "", "", "", "", "", str(e)])

# Print the SG rule table
headers = ['SG Name', 'SG ID', 'Direction', 'Protocol', 'From Port', 'To Port', 'Source/Destination']
print("\n🔐 Security Group Rules:\n")
print(tabulate(sg_details, headers=headers, tablefmt="fancy_grid"))

