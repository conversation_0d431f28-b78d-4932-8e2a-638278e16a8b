FROM node:18-alpine

# Install curl for health checks
RUN apk add --no-cache curl

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application files
COPY app-v2.js ./
COPY analysis-engine.js ./
COPY file-analyzer.js ./
COPY default-checklists.js ./
COPY views-v2/ ./views-v2/
COPY public/ ./public/

# Create data directory
RUN mkdir -p data

# Create default environment file
RUN echo "PORT=3000" > .env.default

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/ || exit 1

EXPOSE 3000

CMD ["node", "app-v2.js"]
