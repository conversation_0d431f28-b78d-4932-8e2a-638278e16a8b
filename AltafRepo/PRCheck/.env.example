# GitHub Configuration
GITHUB_TOKEN=your_github_personal_access_token_here
GITHUB_WEBHOOK_SECRET=your_webhook_secret_here

# AI Configuration (Ollama)
OLLAMA_URL=http://host.docker.internal:11434
AI_MODEL=GandalfBaum/llama3.2-claude3.7:latest

# Database Configuration
POSTGRES_PASSWORD=pr_password_123
DATABASE_URL=**************************************************/pr_review

# Redis Configuration
REDIS_URL=redis://redis:6379

# Application Configuration
NODE_ENV=production
PORT=3000
SESSION_SECRET=your_super_secret_session_key_here
LOG_LEVEL=info

# Security Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://your-domain.com

# Optional: Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Optional: File Upload Limits
MAX_FILE_SIZE=10485760
MAX_FILES=10

# Optional: AI Service Timeouts
AI_TIMEOUT_MS=120000
AI_MAX_TOKENS=2000

# Optional: GitHub API Configuration
GITHUB_API_TIMEOUT_MS=30000
GITHUB_MAX_RETRIES=3

# Optional: Webhook Configuration
WEBHOOK_PATH=/webhooks/github
WEBHOOK_TIMEOUT_MS=10000
