#!/bin/bash

# Setup script for Ollama with smollm2:360m model

echo "🤖 Setting up Ollama with smollm2:360m model..."

# Wait for Ollama service to be ready
echo "⏳ Waiting for Ollama service to start..."
for i in {1..30}; do
    if curl -s http://ollama:11434/api/tags > /dev/null 2>&1; then
        echo "✅ Ollama service is ready!"
        break
    fi
    echo "   Attempt $i/30: Waiting for Ollama..."
    sleep 2
done

# Check if Ollama is accessible
if ! curl -s http://ollama:11434/api/tags > /dev/null 2>&1; then
    echo "❌ Failed to connect to Ollama service"
    exit 1
fi

# Check if smollm2:360m model is already installed
echo "🔍 Checking if smollm2:360m model is installed..."
if curl -s http://ollama:11434/api/tags | grep -q "smollm2:360m"; then
    echo "✅ smollm2:360m model is already installed!"
else
    echo "📥 Installing smollm2:360m model (this may take a few minutes)..."
    
    # Pull the model
    curl -X POST http://ollama:11434/api/pull \
        -H "Content-Type: application/json" \
        -d '{"name": "smollm2:360m"}' \
        --no-buffer
    
    # Verify installation
    if curl -s http://ollama:11434/api/tags | grep -q "smollm2:360m"; then
        echo "✅ smollm2:360m model installed successfully!"
    else
        echo "❌ Failed to install smollm2:360m model"
        exit 1
    fi
fi

# Test the model
echo "🧪 Testing smollm2:360m model..."
response=$(curl -s -X POST http://ollama:11434/api/generate \
    -H "Content-Type: application/json" \
    -d '{
        "model": "smollm2:360m",
        "prompt": "Hello! Can you help with code review?",
        "stream": false,
        "options": {
            "temperature": 0.7,
            "max_tokens": 50
        }
    }')

if echo "$response" | grep -q "response"; then
    echo "✅ smollm2:360m model is working correctly!"
    echo "🎉 Ollama setup complete!"
else
    echo "⚠️  Model installed but test failed. Response: $response"
fi

echo ""
echo "📋 Model Information:"
curl -s http://ollama:11434/api/tags | grep -A 5 -B 5 "smollm2:360m" || echo "Model details not available"

echo ""
echo "🚀 Ollama is ready for use with smollm2:360m model!"
