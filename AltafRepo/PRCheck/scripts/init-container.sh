#!/bin/bash

# Container initialization script

echo "🚀 Initializing PR Review Assistant..."

# Wait for Ollama (optional)
echo "⏳ Waiting for Ollama..."
for i in {1..30}; do
    if curl -s http://ollama:11434/api/tags > /dev/null 2>&1; then
        echo "   ✅ Ollama is ready"
        break
    fi
    echo "   Attempt $i/30: Waiting for Ollama..."
    sleep 2
done

# Setup Ollama model in background
echo "🤖 Setting up AI model in background..."
(
    sleep 10  # Give the main app time to start
    /app/scripts/setup-ollama.sh
) &

# Start the main application
echo "🌟 Starting PR Review Assistant..."
exec node server.js
