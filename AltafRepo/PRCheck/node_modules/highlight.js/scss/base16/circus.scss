pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Circus
  Author: <PERSON> (https://github.com/stepchowfun) and <PERSON> (https://github.com/ewang12)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme circus
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #191919  Default Background
base01  #202020  Lighter Background (Used for status bars, line number and folding marks)
base02  #303030  Selection Background
base03  #5f5a60  Comments, Invisibles, Line Highlighting
base04  #505050  Dark Foreground (Used for status bars)
base05  #a7a7a7  Default Foreground, Caret, Delimiters, Operators
base06  #808080  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #dc657d  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #4bb1a7  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #c3ba63  Classes, Markup Bold, Search Text Background
base0B  #84b97c  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #4bb1a7  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #639ee4  Functions, Methods, Attribute IDs, Headings
base0E  #b888e2  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b888e2  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #a7a7a7;
  background: #191919
}
.hljs::selection,
.hljs ::selection {
  background-color: #303030;
  color: #a7a7a7
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #5f5a60 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #5f5a60
}
/* base04 - #505050 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #505050
}
/* base05 - #a7a7a7 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #a7a7a7
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #dc657d
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #4bb1a7
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #c3ba63
}
.hljs-strong {
  font-weight: bold;
  color: #c3ba63
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #84b97c
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #4bb1a7
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #639ee4
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #b888e2
}
.hljs-emphasis {
  color: #b888e2;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b888e2
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}