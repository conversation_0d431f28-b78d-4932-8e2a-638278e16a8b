{"name": "universal-user-agent", "description": "Get a user agent string in both browser and node", "version": "6.0.1", "license": "ISC", "files": ["dist-*/", "bin/"], "pika": true, "sideEffects": false, "keywords": [], "repository": "https://github.com/gr2m/universal-user-agent.git", "dependencies": {}, "devDependencies": {"@gr2m/pika-plugin-build-web": "^0.6.0-issue-84.1", "@pika/pack": "^0.5.0", "@pika/plugin-build-node": "^0.9.1", "@pika/plugin-ts-standard-pkg": "^0.9.1", "@types/jest": "^25.1.0", "jest": "^24.9.0", "prettier": "^2.0.0", "semantic-release": "^17.0.5", "ts-jest": "^26.0.0", "typescript": "^3.6.2"}, "source": "dist-src/index.js", "types": "dist-types/index.d.ts", "main": "dist-node/index.js", "module": "dist-web/index.js"}