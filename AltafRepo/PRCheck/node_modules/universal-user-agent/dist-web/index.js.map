{"version": 3, "file": "index.js", "sources": ["../dist-src/index.js"], "sourcesContent": ["export function getUserAgent() {\n    if (typeof navigator === \"object\" && \"userAgent\" in navigator) {\n        return navigator.userAgent;\n    }\n    if (typeof process === \"object\" && process.version !== undefined) {\n        return `Node.js/${process.version.substr(1)} (${process.platform}; ${process.arch})`;\n    }\n    return \"<environment undetectable>\";\n}\n"], "names": [], "mappings": "AAAO,SAAS,YAAY,GAAG;AAC/B,IAAI,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,WAAW,IAAI,SAAS,EAAE;AACnE,QAAQ,OAAO,SAAS,CAAC,SAAS,CAAC;AACnC,KAAK;AACL,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;AACtE,QAAQ,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7F,KAAK;AACL,IAAI,OAAO,4BAA4B,CAAC;AACxC;;;;"}