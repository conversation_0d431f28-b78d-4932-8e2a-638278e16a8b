{"version": 3, "sources": ["../dist-src/index.js", "../dist-src/version.js"], "sourcesContent": ["import { getUserAgent } from \"universal-user-agent\";\nimport { Collection } from \"before-after-hook\";\nimport { request } from \"@octokit/request\";\nimport { graphql, withCustomRequest } from \"@octokit/graphql\";\nimport { createTokenAuth } from \"@octokit/auth-token\";\nimport { VERSION } from \"./version\";\nclass Octokit {\n  static defaults(defaults) {\n    const OctokitWithDefaults = class extends this {\n      constructor(...args) {\n        const options = args[0] || {};\n        if (typeof defaults === \"function\") {\n          super(defaults(options));\n          return;\n        }\n        super(\n          Object.assign(\n            {},\n            defaults,\n            options,\n            options.userAgent && defaults.userAgent ? {\n              userAgent: `${options.userAgent} ${defaults.userAgent}`\n            } : null\n          )\n        );\n      }\n    };\n    return OctokitWithDefaults;\n  }\n  /**\n   * Attach a plugin (or many) to your Octokit instance.\n   *\n   * @example\n   * const API = Octokit.plugin(plugin1, plugin2, plugin3, ...)\n   */\n  static plugin(...newPlugins) {\n    var _a;\n    const currentPlugins = this.plugins;\n    const NewOctokit = (_a = class extends this {\n    }, _a.plugins = currentPlugins.concat(\n      newPlugins.filter((plugin) => !currentPlugins.includes(plugin))\n    ), _a);\n    return NewOctokit;\n  }\n  constructor(options = {}) {\n    const hook = new Collection();\n    const requestDefaults = {\n      baseUrl: request.endpoint.DEFAULTS.baseUrl,\n      headers: {},\n      request: Object.assign({}, options.request, {\n        // @ts-ignore internal usage only, no need to type\n        hook: hook.bind(null, \"request\")\n      }),\n      mediaType: {\n        previews: [],\n        format: \"\"\n      }\n    };\n    requestDefaults.headers[\"user-agent\"] = [\n      options.userAgent,\n      `octokit-core.js/${VERSION} ${getUserAgent()}`\n    ].filter(Boolean).join(\" \");\n    if (options.baseUrl) {\n      requestDefaults.baseUrl = options.baseUrl;\n    }\n    if (options.previews) {\n      requestDefaults.mediaType.previews = options.previews;\n    }\n    if (options.timeZone) {\n      requestDefaults.headers[\"time-zone\"] = options.timeZone;\n    }\n    this.request = request.defaults(requestDefaults);\n    this.graphql = withCustomRequest(this.request).defaults(requestDefaults);\n    this.log = Object.assign(\n      {\n        debug: () => {\n        },\n        info: () => {\n        },\n        warn: console.warn.bind(console),\n        error: console.error.bind(console)\n      },\n      options.log\n    );\n    this.hook = hook;\n    if (!options.authStrategy) {\n      if (!options.auth) {\n        this.auth = async () => ({\n          type: \"unauthenticated\"\n        });\n      } else {\n        const auth = createTokenAuth(options.auth);\n        hook.wrap(\"request\", auth.hook);\n        this.auth = auth;\n      }\n    } else {\n      const { authStrategy, ...otherOptions } = options;\n      const auth = authStrategy(\n        Object.assign(\n          {\n            request: this.request,\n            log: this.log,\n            // we pass the current octokit instance as well as its constructor options\n            // to allow for authentication strategies that return a new octokit instance\n            // that shares the same internal state as the current one. The original\n            // requirement for this was the \"event-octokit\" authentication strategy\n            // of https://github.com/probot/octokit-auth-probot.\n            octokit: this,\n            octokitOptions: otherOptions\n          },\n          options.auth\n        )\n      );\n      hook.wrap(\"request\", auth.hook);\n      this.auth = auth;\n    }\n    const classConstructor = this.constructor;\n    classConstructor.plugins.forEach((plugin) => {\n      Object.assign(this, plugin(this, options));\n    });\n  }\n}\nOctokit.VERSION = VERSION;\nOctokit.plugins = [];\nexport {\n  Octokit\n};\n", "const VERSION = \"4.2.4\";\nexport {\n  VERSION\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCAA6B;AAC7B,+BAA2B;AAC3B,qBAAwB;AACxB,qBAA2C;AAC3C,wBAAgC;;;ACJhC,IAAM,UAAU;;;ADMhB,IAAM,UAAN,MAAc;AAAA,EACZ,OAAO,SAAS,UAAU;AACxB,UAAM,sBAAsB,cAAc,KAAK;AAAA,MAC7C,eAAe,MAAM;AACnB,cAAM,UAAU,KAAK,CAAC,KAAK,CAAC;AAC5B,YAAI,OAAO,aAAa,YAAY;AAClC,gBAAM,SAAS,OAAO,CAAC;AACvB;AAAA,QACF;AACA;AAAA,UACE,OAAO;AAAA,YACL,CAAC;AAAA,YACD;AAAA,YACA;AAAA,YACA,QAAQ,aAAa,SAAS,YAAY;AAAA,cACxC,WAAW,GAAG,QAAQ,aAAa,SAAS;AAAA,YAC9C,IAAI;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UAAU,YAAY;AAC3B,QAAI;AACJ,UAAM,iBAAiB,KAAK;AAC5B,UAAM,cAAc,KAAK,cAAc,KAAK;AAAA,IAC5C,GAAG,GAAG,UAAU,eAAe;AAAA,MAC7B,WAAW,OAAO,CAAC,WAAW,CAAC,eAAe,SAAS,MAAM,CAAC;AAAA,IAChE,GAAG;AACH,WAAO;AAAA,EACT;AAAA,EACA,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,OAAO,IAAI,oCAAW;AAC5B,UAAM,kBAAkB;AAAA,MACtB,SAAS,uBAAQ,SAAS,SAAS;AAAA,MACnC,SAAS,CAAC;AAAA,MACV,SAAS,OAAO,OAAO,CAAC,GAAG,QAAQ,SAAS;AAAA;AAAA,QAE1C,MAAM,KAAK,KAAK,MAAM,SAAS;AAAA,MACjC,CAAC;AAAA,MACD,WAAW;AAAA,QACT,UAAU,CAAC;AAAA,QACX,QAAQ;AAAA,MACV;AAAA,IACF;AACA,oBAAgB,QAAQ,YAAY,IAAI;AAAA,MACtC,QAAQ;AAAA,MACR,mBAAmB,eAAW,0CAAa;AAAA,IAC7C,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC1B,QAAI,QAAQ,SAAS;AACnB,sBAAgB,UAAU,QAAQ;AAAA,IACpC;AACA,QAAI,QAAQ,UAAU;AACpB,sBAAgB,UAAU,WAAW,QAAQ;AAAA,IAC/C;AACA,QAAI,QAAQ,UAAU;AACpB,sBAAgB,QAAQ,WAAW,IAAI,QAAQ;AAAA,IACjD;AACA,SAAK,UAAU,uBAAQ,SAAS,eAAe;AAC/C,SAAK,cAAU,kCAAkB,KAAK,OAAO,EAAE,SAAS,eAAe;AACvE,SAAK,MAAM,OAAO;AAAA,MAChB;AAAA,QACE,OAAO,MAAM;AAAA,QACb;AAAA,QACA,MAAM,MAAM;AAAA,QACZ;AAAA,QACA,MAAM,QAAQ,KAAK,KAAK,OAAO;AAAA,QAC/B,OAAO,QAAQ,MAAM,KAAK,OAAO;AAAA,MACnC;AAAA,MACA,QAAQ;AAAA,IACV;AACA,SAAK,OAAO;AACZ,QAAI,CAAC,QAAQ,cAAc;AACzB,UAAI,CAAC,QAAQ,MAAM;AACjB,aAAK,OAAO,aAAa;AAAA,UACvB,MAAM;AAAA,QACR;AAAA,MACF,OAAO;AACL,cAAM,WAAO,mCAAgB,QAAQ,IAAI;AACzC,aAAK,KAAK,WAAW,KAAK,IAAI;AAC9B,aAAK,OAAO;AAAA,MACd;AAAA,IACF,OAAO;AACL,YAAM,EAAE,cAAc,GAAG,aAAa,IAAI;AAC1C,YAAM,OAAO;AAAA,QACX,OAAO;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,KAAK,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMV,SAAS;AAAA,YACT,gBAAgB;AAAA,UAClB;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,MACF;AACA,WAAK,KAAK,WAAW,KAAK,IAAI;AAC9B,WAAK,OAAO;AAAA,IACd;AACA,UAAM,mBAAmB,KAAK;AAC9B,qBAAiB,QAAQ,QAAQ,CAAC,WAAW;AAC3C,aAAO,OAAO,MAAM,OAAO,MAAM,OAAO,CAAC;AAAA,IAC3C,CAAC;AAAA,EACH;AACF;AACA,QAAQ,UAAU;AAClB,QAAQ,UAAU,CAAC;", "names": []}