{"name": "@octokit/core", "version": "4.2.4", "publishConfig": {"access": "public"}, "description": "Extendable client for GitHub's REST & GraphQL APIs", "repository": "github:octokit/core.js", "keywords": ["octokit", "github", "api", "sdk", "toolkit"], "author": "<PERSON> (https://github.com/gr2m)", "license": "MIT", "dependencies": {"@octokit/auth-token": "^3.0.0", "@octokit/graphql": "^5.0.0", "@octokit/request": "^6.0.0", "@octokit/request-error": "^3.0.0", "@octokit/types": "^9.0.0", "before-after-hook": "^2.2.0", "universal-user-agent": "^6.0.0"}, "devDependencies": {"@octokit/auth": "^3.0.1", "@octokit/tsconfig": "^2.0.0", "@types/fetch-mock": "^7.3.1", "@types/jest": "^29.0.0", "@types/lolex": "^5.1.0", "@types/node": "^18.0.0", "esbuild": "^0.18.0", "fetch-mock": "^9.0.0", "glob": "^10.2.5", "http-proxy-agent": "^7.0.0", "jest": "^29.0.0", "lolex": "^6.0.0", "prettier": "2.8.8", "proxy": "^2.0.0", "semantic-release": "^21.0.0", "semantic-release-plugin-update-version-in-files": "^1.0.0", "ts-jest": "^29.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">= 14"}, "files": ["dist-*/**", "bin/**"], "main": "dist-node/index.js", "module": "dist-web/index.js", "types": "dist-types/index.d.ts", "source": "dist-src/index.js", "sideEffects": false}