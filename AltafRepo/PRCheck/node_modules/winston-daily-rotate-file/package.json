{"name": "winston-daily-rotate-file", "version": "4.7.1", "description": "A transport for winston which logs to a rotating file each day.", "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --ignore **/*.worker.js && eslint ."}, "repository": {"type": "git", "url": "**************:winstonjs/winston-daily-rotate-file.git"}, "keywords": ["winston", "daily-rotate-file", "log-rotate", "logrotate"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/winstonjs/winston-daily-rotate-file/issues"}, "files": ["daily-rotate-file.js", "index.js", "index.d.ts"], "homepage": "https://github.com/winstonjs/winston-daily-rotate-file#readme", "peerDependencies": {"winston": "^3"}, "devDependencies": {"chai": "4.2.0", "eslint": "^6.8.0", "eslint-plugin-node": "^11.1.0", "mocha": "^7.2.0", "rimraf": "^3.0.2", "threads": "^1.7.0"}, "dependencies": {"file-stream-rotator": "^0.6.1", "object-hash": "^2.0.1", "triple-beam": "^1.3.0", "winston-transport": "^4.4.0"}}