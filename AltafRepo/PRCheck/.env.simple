# Simple PR Review Tool Configuration

# Application Settings
PORT=3000
NODE_ENV=production
SESSION_SECRET=simple-pr-review-secret-key-change-this

# GitHub OAuth Configuration
# Create a GitHub OAuth App at: https://github.com/settings/developers
# Set Authorization callback URL to: http://localhost:3000/auth/github/callback
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here
GITHUB_CALLBACK_URL=http://localhost:3000/auth/github/callback

# Instructions:
# 1. Go to GitHub Settings → Developer settings → OAuth Apps
# 2. Click "New OAuth App"
# 3. Fill in:
#    - Application name: Simple PR Review Tool
#    - Homepage URL: http://localhost:3000
#    - Authorization callback URL: http://localhost:3000/auth/github/callback
# 4. Copy the Client ID and Client Secret to the variables above
# 5. Save this file as .env (remove .simple extension)
