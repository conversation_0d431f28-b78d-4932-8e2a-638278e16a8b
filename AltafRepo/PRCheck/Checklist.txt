https://cbea.ms/git-commit/

Peer Reviews
Our team relies extensively on Peer Reviews, often in the form of GitHub Pull Requests.

(info) Our team does both internal PRs of our own changes for the Sprint, but also reviews requests from other teams. Most of this content is focused on internal peer review etiquette.

Branches
PR changes are done against a feature branch, e.g., STRATUS-123_add_foo
PRs are to merge into master  branch
master  should always be production-ready. If it's been merged to master , it can be deployed to production.
We favor smaller incremental changes, frequently deployed, when possible rather than large changes deployed infrequently.
Submitting PRs
Put yourself in the shoes of the reviewer. Each review takes time; if the PR is clearly defined it saves time on the reviewer but you also benefit in getting feedback/approval faster with fewer feedback iterations
Include the context of the change
The focus is Why. Ideally the What is clear from the code content, but complicated changes often deserve a description of What
Reference other associated changes as appropriate
e.g., if deploying a Terraform change in a specific account, include a link to the module change (e.g., security group update) so the reviewer can follow along
Review your PR first before asking another to do it for you
You may find that you accidentally checked in extra files or had some changes that were not intentional
Summarize the change in the title
Rather than: "STRATUS-123", the title could be "standard-app-stage-common: Update cluster max_size 5 → 10"
This helps greatly when looking at history of changes.
As needed, include testing information
for new functionality, we ideally add automated tests but in some cases it requires some manual testing if automation is not feasible or many systems are involved
Include information on how you've tested it. For example, if updating ECS terraform modules, you could describe how you launched a temporary cluster in dev and performed X step, then observed Y logs/behavior.
Try to limit the diff as much as reasonable
e.g., for a critical changes to infrastructure, don't bundle in extensive refactoring or rename every variable. This makes it difficult to review
Some extra improvements are fine, but be mindful of the reviewer's time
Significant refactoring can be done in their own PRs, or in distinct commits to help reviewers
You can submit "Draft PRs" if you are not ready yet for reviewers or otherwise want to indicate that it is not yet ready to be merged
The submitter of the PR assigns reviewers, typically 1-2
The reviewers may be selected because they have experience in this area, or they have mentioned they have spare time. We also try to spread reviews out across the team for knowledge sharing.
Only one reviewer needs to approve
See also:

https://chris.beams.io/posts/git-commit/
Commenting on PRs
Comments given should be constructive. We're all on the same team trying to meet the same goals.
Comments should be neutral, focused on the code, never directed at the submitter
Comments are not a bad thing. They may simply be to ask questions or to praise certain changes
Comments should explain the reasoning when appropriate. PRs are also one of the key knowledge sharing activities in the team.
It can be helpful to note that a comment is not a blocking issue
Example comments

Ex 1
Bad: "this is wrong"
Good: "if N=3 in this case, it looks like this function may return an incorrect result because of Y"
Ex 2
Bad: "your formatting is bad"
Good: "this code seems to be indented too much compared to other blocks"
Ex 3
Bad: "set this to 35"
Good: "should we consider increasing this maximum limit to 35 to give some buffer for scaling?"
PR Feedback Cycle
If you are waiting on a PR review and unsure of the status for ~1 day, you should ask the reviewers if they saw the PR. They may have missed it, or it may be necessary to re-assign if they are unavailable.
Merging and Releasing PRs
Once approved, the submitter will merge and perform any follow-up activities (e.g., deployments)
In the case of tf_live_infra repositories, deploys happen via Jenkins
For Terraform modules, after any functional change (even small), we create a Release in GitHub
following semver versioning
Release Note is typically just the title of the PR with a PR number (e.g., #123) which automatically becomes a link in GitHub
For production releases/changes, you must complete a CHG request ticket