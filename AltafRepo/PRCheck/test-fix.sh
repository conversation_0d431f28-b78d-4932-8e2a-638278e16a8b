#!/bin/bash

echo "🧪 Testing PR Review Tool v2.0 - Error Fixes"
echo "============================================="

# Test 1: Basic API health check
echo "1. Testing API health..."
if curl -s http://localhost:3000/ > /dev/null; then
    echo "✅ Application is responding"
else
    echo "❌ Application is not responding"
    exit 1
fi

# Test 2: Valid PR analysis
echo "2. Testing valid PR analysis..."
RESPONSE=$(curl -X POST http://localhost:3000/api/analyze \
  -H "Content-Type: application/json" \
  -d '{"repoUrl":"https://github.com/saltaf07/assistila-web","prNumber":"1","analysisType":"public","checklistId":"default"}' \
  --silent)

SUCCESS=$(echo "$RESPONSE" | jq -r '.success')
if [ "$SUCCESS" = "true" ]; then
    REVIEW_ID=$(echo "$RESPONSE" | jq -r '.reviewId')
    SCORE=$(echo "$RESPONSE" | jq -r '.analysis.summary.score')
    TOTAL=$(echo "$RESPONSE" | jq -r '.analysis.summary.totalChecks')
    echo "✅ Valid PR analysis successful: $SCORE/$TOTAL checks passed"
    echo "   Review ID: $REVIEW_ID"
else
    ERROR=$(echo "$RESPONSE" | jq -r '.error')
    echo "❌ Valid PR analysis failed: $ERROR"
    exit 1
fi

# Test 3: Invalid PR analysis (should handle gracefully)
echo "3. Testing invalid PR analysis..."
RESPONSE=$(curl -X POST http://localhost:3000/api/analyze \
  -H "Content-Type: application/json" \
  -d '{"repoUrl":"https://github.com/nonexistent/repo","prNumber":"999","analysisType":"public","checklistId":"default"}' \
  --silent)

SUCCESS=$(echo "$RESPONSE" | jq -r '.success')
if [ "$SUCCESS" = "true" ]; then
    STATUS=$(echo "$RESPONSE" | jq -r '.analysis.summary.status')
    if [ "$STATUS" = "error" ]; then
        echo "✅ Invalid PR handled gracefully with error status"
    else
        echo "⚠️  Invalid PR returned success but should have error status"
    fi
else
    echo "✅ Invalid PR correctly returned failure"
fi

# Test 4: Results page accessibility
echo "4. Testing results page..."
if curl -s "http://localhost:3000/results/$REVIEW_ID" | grep -q "PR Analysis Results"; then
    echo "✅ Results page is accessible and contains expected content"
else
    echo "❌ Results page is not accessible or missing content"
fi

# Test 5: Dashboard accessibility
echo "5. Testing dashboard..."
if curl -s http://localhost:3000/ | grep -q "Enhanced PR Review Tool"; then
    echo "✅ Dashboard is accessible and contains expected content"
else
    echo "❌ Dashboard is not accessible or missing content"
fi

# Test 6: Checklist management
echo "6. Testing checklist API..."
CHECKLISTS=$(curl -s http://localhost:3000/api/checklists)
if echo "$CHECKLISTS" | jq -e '.success' > /dev/null; then
    COUNT=$(echo "$CHECKLISTS" | jq -r '.checklists | keys | length')
    echo "✅ Checklist API working: $COUNT checklists available"
else
    echo "❌ Checklist API failed"
fi

echo ""
echo "🎉 All tests completed!"
echo "=============================="
echo "✅ Error fixes verified:"
echo "   • PR data validation and error handling"
echo "   • Results page error handling"
echo "   • Graceful failure for invalid PRs"
echo "   • API endpoints working correctly"
echo ""
echo "🌐 Application ready at: http://localhost:3000"
echo "📊 Test your repository:"
echo "   Repository: https://github.com/saltaf07/assistila-web"
echo "   PR Number: 1"
