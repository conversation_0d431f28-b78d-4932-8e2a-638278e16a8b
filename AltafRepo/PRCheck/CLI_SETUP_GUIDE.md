# 🚀 CLI Authentication Setup Guide

## Quick Start (Personal Access Token - Recommended)

The **easiest and fastest** way to get CLI authentication working:

### 1. Create a GitHub Personal Access Token

1. Go to: https://github.com/settings/tokens
2. Click **"Generate new token (classic)"**
3. Give it a name like "PR Review Tool"
4. Select these scopes:
   - ✅ `repo` (Full control of private repositories)
   - ✅ `user:email` (Access user email addresses)
5. Click **"Generate token"**
6. **Copy the token immediately** (you won't see it again!)

### 2. Use the CLI Authentication Script

```bash
# Interactive mode (recommended for first time)
node github-cli-auth.js

# Direct token authentication
node github-cli-auth.js --token ghp_your_token_here

# Use saved token (after first authentication)
node github-cli-auth.js --saved

# Show help
node github-cli-auth.js --help
```

### 3. Example Usage

```bash
# Start the PR Review Tool
docker compose -f docker-compose.v2.yml up -d

# Authenticate and analyze a PR
node github-cli-auth.js
# Follow the prompts to:
# 1. Enter your token
# 2. Run demo analysis
# 3. Open web interface
```

## 🔧 Current Configuration

Your GitHub App is configured with:
- **Client ID**: `Ov23libp9MfZXWUt3Ult`
- **Device Flow**: ❌ Not enabled (optional)
- **Personal Token Auth**: ✅ Working

## 🎯 What Works Right Now

### ✅ Personal Access Token Authentication
- **Status**: ✅ Fully working
- **Setup**: Just create a token and use the CLI script
- **Benefits**: No additional configuration needed

### ❌ Device Flow Authentication  
- **Status**: ❌ Requires GitHub App configuration
- **Error**: "Device Flow must be explicitly enabled for this App"
- **Solution**: Enable in GitHub App settings (optional)

## 🔧 Optional: Enable Device Flow

If you want to enable Device Flow (the most CLI-friendly option), follow these steps:

### 1. Go to Your GitHub App Settings
1. Visit: https://github.com/settings/apps
2. Find your app with Client ID `Ov23libp9MfZXWUt3Ult`
3. Click on it to edit

### 2. Enable Device Flow
1. Scroll down to **"Device Flow"** section
2. Check the box: ✅ **"Enable Device Flow"**
3. Click **"Save changes"**

### 3. Test Device Flow
```bash
# Test device flow endpoint
curl -X POST http://localhost:3000/auth/github/device \
  -H "Content-Type: application/json"

# Should return user_code and verification_uri
```

## 🚀 CLI Authentication Features

### Current Working Features:
- ✅ Personal Access Token authentication
- ✅ Token validation and user info retrieval
- ✅ Secure token storage (local file with restricted permissions)
- ✅ Interactive PR analysis
- ✅ Browser integration
- ✅ Cross-platform support (macOS, Linux, Windows)

### Enhanced Web Interface:
- ✅ Multiple authentication options dropdown
- ✅ Personal token input modal
- ✅ Device flow modal (ready when enabled)
- ✅ Step-by-step instructions

## 📋 CLI Script Commands

```bash
# Show help
node github-cli-auth.js --help

# Interactive authentication (first time)
node github-cli-auth.js

# Use specific token
node github-cli-auth.js --token ghp_your_token_here

# Use previously saved token
node github-cli-auth.js --saved
```

## 🔒 Security Notes

1. **Token Storage**: Tokens are stored locally with restricted file permissions (600)
2. **Token Scope**: Only request necessary scopes (`repo`, `user:email`)
3. **Token Rotation**: Regularly rotate your tokens for security
4. **Never Commit**: Never commit tokens to version control

## 🐳 Docker Integration

The CLI authentication works perfectly with your Docker setup:

```bash
# Start the application
docker compose -f docker-compose.v2.yml up -d

# Authenticate from host machine
node github-cli-auth.js

# The authentication session works with the Docker container
```

## 🎯 Next Steps

1. **Try it now**: Run `node github-cli-auth.js` to test authentication
2. **Create a token**: Visit https://github.com/settings/tokens
3. **Analyze a PR**: Use the demo or your own repository
4. **Optional**: Enable Device Flow in GitHub App settings for even better CLI experience

## 🆘 Troubleshooting

### Token Authentication Issues:
```bash
# Check if the application is running
curl http://localhost:3000/

# Test token endpoint directly
curl -X POST http://localhost:3000/auth/github/token \
  -H "Content-Type: application/json" \
  -d '{"token":"your_token_here"}'
```

### Common Issues:
1. **Invalid token**: Check scopes and expiration
2. **Application not running**: Start with `docker compose up -d`
3. **Network issues**: Ensure port 3000 is accessible

## 🎉 Success!

You now have CLI-friendly GitHub authentication without needing:
- ❌ Client secrets
- ❌ Callback URL configuration  
- ❌ Complex OAuth setup

Just create a Personal Access Token and start analyzing PRs! 🚀
