{"name": "pr-review-assistant", "version": "1.0.0", "description": "AI-powered PR review assistant with checklist validation", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "setup": "node scripts/setup.js"}, "dependencies": {"express": "^4.18.2", "express-session": "^1.17.3", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "cors": "^2.8.5", "dotenv": "^16.0.3", "axios": "^1.4.0", "@octokit/rest": "^19.0.11", "@octokit/webhooks": "^10.9.2", "redis": "^4.6.7", "pg": "^8.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "marked": "^5.1.0", "highlight.js": "^11.8.0", "winston": "^3.9.0", "winston-daily-rotate-file": "^4.7.1", "node-cron": "^3.0.2", "socket.io": "^4.7.1", "ejs": "^3.1.9", "express-validator": "^7.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.0", "crypto": "^1.0.1"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "eslint": "^8.42.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}, "keywords": ["github", "pr-review", "ai", "checklist", "automation", "code-review"], "author": "Internal Team", "license": "MIT"}