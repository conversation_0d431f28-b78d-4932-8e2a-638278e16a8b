# Simple PR Review Tool

A lightweight, Docker-based tool for analyzing GitHub Pull Requests without heavy dependencies.

**✅ WORKING & TESTED** - Application is running successfully at http://localhost:3000

## ✨ Features

- 🔍 **Rule-based PR Analysis** - No AI required, fast and reliable
- 🔐 **GitHub OAuth Login** - Secure access to private and public repos
- 💾 **Local Data Storage** - No external databases, all data stored locally
- 🐳 **Docker Deployment** - Easy setup and deployment
- 📊 **Review Dashboard** - Track your PR reviews and scores
- 📝 **Manual PR Input** - Simply paste GitHub repo URL and PR number

## 🚀 Quick Start

### 1. Start the Application

```bash
./start-simple.sh
```

This will:
- Build and start the Docker container
- Create necessary configuration files
- Start the application on http://localhost:3000

### 2. Configure GitHub OAuth (Optional)

To enable GitHub login and access private repositories:

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Fill in the details:
   - **Application name**: Simple PR Review Tool
   - **Homepage URL**: http://localhost:3000
   - **Authorization callback URL**: http://localhost:3000/auth/github/callback
4. Copy the Client ID and Client Secret
5. Edit the `.env` file and update:
   ```
   GITHUB_CLIENT_ID=your_client_id_here
   GITHUB_CLIENT_SECRET=your_client_secret_here
   ```
6. Restart the application: `./start-simple.sh`

### 3. Use the Tool

1. Open http://localhost:3000 in your browser
2. Login with GitHub (if configured) or use without login for public repos
3. Click "New Review"
4. Enter a GitHub repository URL and PR number
5. Click "Analyze PR" to get instant feedback

## 📋 Analysis Checks

The tool performs these rule-based checks:

- ✅ **Descriptive Title** - PR title should be meaningful (>10 characters)
- ✅ **Has Description** - PR should include context (>20 characters)
- ✅ **Reasonable Scope** - Not too many files changed (≤10 files)
- ✅ **Manageable Size** - Not too many additions (≤500 lines)
- ✅ **Feature Branch** - Should not target main/master directly

## 🛠️ Commands

```bash
# Start the application
./start-simple.sh

# View logs
docker compose -f docker-compose.simple.yml logs -f

# Stop the application
docker compose -f docker-compose.simple.yml down

# Restart the application
./start-simple.sh
```

## 📁 File Structure

```
├── simple-app.js              # Main application file
├── views/                     # EJS templates
│   ├── layout.ejs            # Base layout
│   ├── simple-index.ejs      # Home page
│   ├── simple-dashboard.ejs  # Dashboard
│   ├── new-review.ejs        # New review form
│   └── review-detail.ejs     # Review details
├── Dockerfile.simple         # Docker configuration
├── docker-compose.simple.yml # Docker Compose configuration
├── start-simple.sh          # Startup script
├── .env.simple              # Environment template
└── data/                    # Local data storage (created automatically)
    ├── reviews.json         # PR reviews
    └── users.json          # User sessions
```

## 🔧 Configuration

### Environment Variables

- `PORT` - Application port (default: 3000)
- `NODE_ENV` - Environment (production/development)
- `SESSION_SECRET` - Session encryption key
- `GITHUB_CLIENT_ID` - GitHub OAuth Client ID
- `GITHUB_CLIENT_SECRET` - GitHub OAuth Client Secret
- `GITHUB_CALLBACK_URL` - OAuth callback URL

### Data Storage

All data is stored locally in JSON files:
- `data/reviews.json` - Stores all PR review results
- `data/users.json` - Stores user session data

## 🆚 Differences from Full Version

| Feature | Simple Version | Full Version |
|---------|---------------|--------------|
| Database | Local JSON files | PostgreSQL + Redis |
| AI Analysis | Rule-based only | AI-powered with Ollama |
| Setup Complexity | Minimal | Complex |
| Resource Usage | Low | High |
| Dependencies | Few | Many |
| Webhooks | Not supported | Supported |

## 🐛 Troubleshooting

### Application won't start
```bash
# Check if port 3000 is available
lsof -i :3000

# View detailed logs
docker compose -f docker-compose.simple.yml logs
```

### GitHub OAuth not working
1. Verify your OAuth app settings on GitHub
2. Check that the callback URL matches exactly
3. Ensure `.env` file has correct credentials
4. Restart the application after changes

### Can't access private repos
- Make sure GitHub OAuth is configured
- Login with your GitHub account
- Verify your GitHub token has repo access

## 📝 Example Usage

1. **Public Repository Analysis**:
   - Repository: `https://github.com/facebook/react`
   - PR Number: `25123`

2. **Private Repository Analysis** (requires GitHub login):
   - Repository: `https://github.com/yourorg/private-repo`
   - PR Number: `42`

## 🔒 Security

- No external API calls except to GitHub
- All data stored locally in Docker volume
- GitHub OAuth follows standard security practices
- No sensitive data logged or transmitted

## 📊 Data Export

Review data is stored in JSON format and can be easily exported:

```bash
# Copy data from Docker volume
docker cp prcheck-pr-review-simple-1:/app/data ./backup-data
```

## 🤝 Contributing

This is a simplified version focused on ease of use. For advanced features, see the full version with AI capabilities.

## 📄 License

MIT License - see LICENSE file for details.
