# PR Review Assistant

A comprehensive Docker-based web application for internal use that integrates with your private GitHub account to analyze Pull Requests using AI-powered checklist validation and provides intelligent suggestions for improvement.

## 🚀 Features

### ✅ Comprehensive PR Analysis
- **Automated Checklist Validation**: Validates PRs against team standards
- **AI-Powered Code Review**: Intelligent analysis using your Ollama models
- **Real-time Scoring**: Weighted scoring system for PR quality
- **Detailed Reporting**: Comprehensive analysis with actionable insights

### 🤖 AI Integration
- **Ollama Integration**: Uses your existing Docker Ollama setup
- **Multiple Models**: Supports GandalfBaum/llama3.2-claude3.7:latest and fallback models
- **Smart Suggestions**: AI-generated improvement recommendations
- **Auto-Apply Options**: Automatic PR description and review improvements

### 🔧 GitHub Integration
- **Private Repository Support**: Full access to your private GitHub repos
- **Webhook Support**: Real-time PR event processing
- **Direct Updates**: Auto-apply improvements directly to PRs
- **Review Automation**: Generate and post AI reviews automatically

### 📊 Quality Metrics
- **Progress Tracking**: Monitor PR quality improvements over time
- **Team Standards**: Enforce consistent PR practices
- **Bulk Analysis**: Analyze multiple PRs simultaneously
- **Custom Checklist**: Based on your team's specific requirements

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   Node.js API   │    │   AI Service    │
│   (Bootstrap)   │◄──►│   (Express)     │◄──►│   (Ollama)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                    ┌─────────────────┐    ┌─────────────────┐
                    │   PostgreSQL    │    │   Your Docker   │
                    │   (Data Store)  │    │   Ollama Setup  │
                    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │     Redis       │
                    │   (Sessions)    │
                    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- GitHub Personal Access Token
- Running Ollama Docker container with models

### 1. Clone and Setup
```bash
git clone <your-repo>
cd PRCheck

# Copy environment template
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### 2. Configure Environment
Edit `.env` file with your settings:

```bash
# Required: GitHub Token
GITHUB_TOKEN=ghp_your_token_here

# Required: Ollama URL (if using Docker Ollama)
OLLAMA_URL=http://host.docker.internal:11434

# Optional: Customize other settings
SESSION_SECRET=your_secret_here
```

### 3. Start Services
```bash
# Start all services
docker-compose up -d

# Check logs
docker-compose logs -f pr-review-app
```

### 4. Access Application
- **Web Interface**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api
- **Health Check**: http://localhost:3000/health

## 📋 PR Review Checklist

The application validates PRs against these criteria:

| Check | Weight | Description |
|-------|--------|-------------|
| **Descriptive Title** | 10 | Title includes component and brief description |
| **Meaningful Description** | 15 | Explains WHY the change is needed |
| **Testing Information** | 15 | Includes how changes were tested |
| **No Merge Conflicts** | 15 | PR is mergeable without conflicts |
| **Appropriate Scope** | 10 | Reasonable number of files/lines changed |
| **Quality Commit Messages** | 10 | Follows conventional commit format |
| **Reviewers Assigned** | 5 | Has 1-2 reviewers assigned |
| **Proper Branch Name** | 5 | Uses feature branch with good naming |
| **Ready for Review** | 5 | Not in draft state |
| **Appropriate Labels** | 5 | Has relevant categorization labels |

**Scoring:**
- 🟢 **80-100%**: Excellent - Ready for review
- 🟡 **60-79%**: Good - Minor improvements needed  
- 🔴 **<60%**: Needs work - Address critical issues

## 🤖 AI Features

### Automatic Analysis
- **PR Description Analysis**: Evaluates context and clarity
- **Commit Message Review**: Checks conventional format compliance
- **Code Change Analysis**: Reviews for quality and security issues
- **Suggestion Generation**: Provides specific improvement recommendations

### Auto-Apply Options
- **Manual Mode**: Generate suggestions for manual application
- **Auto Mode**: Automatically apply improvements to PRs
- **Batch Processing**: Handle multiple PRs simultaneously

### Supported Models
- **Primary**: GandalfBaum/llama3.2-claude3.7:latest
- **Fallback**: llama3.2:1b (for faster responses)
- **Custom**: Configure any Ollama model

## 🔧 Configuration

### GitHub Setup
1. **Personal Access Token**: Create with `repo`, `read:user` permissions
2. **Webhook** (Optional): Configure for real-time updates
3. **Repository Access**: Ensure token has access to target repositories

### Ollama Integration
```bash
# Ensure your Ollama container is running
docker ps | grep ollama

# Verify models are available
docker exec ollama ollama list

# Install required model if needed
docker exec ollama ollama pull GandalfBaum/llama3.2-claude3.7:latest
```

### Database Setup
The application automatically creates required tables on startup. No manual database setup required.

## 📖 Usage Guide

### 1. Dashboard Overview
- View recent PRs across your repositories
- Quick quality metrics and trends
- Access to bulk analysis tools

### 2. PR Analysis
```
Navigate to: /pr/{owner}/{repo}/{number}
```
- Comprehensive checklist validation
- AI-powered insights and suggestions
- One-click improvement applications

### 3. API Usage
```javascript
// Analyze a PR
const response = await fetch('/api/pr/owner/repo/123/analyze');
const analysis = await response.json();

// Generate improvements
const improvement = await fetch('/api/pr/owner/repo/123/improve', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ type: 'description', autoApply: true })
});
```

### 4. Bulk Operations
- Analyze multiple PRs simultaneously
- Generate team quality reports
- Identify patterns and improvement opportunities

## 🛠️ Development

### Local Development
```bash
# Install dependencies
npm install

# Start in development mode
npm run dev

# Run tests
npm test

# Lint code
npm run lint
```

### Adding Custom Checks
Edit `services/checklistService.js` to add new validation rules:

```javascript
newCheck: {
    name: 'Your Check Name',
    description: 'Description of what this checks',
    weight: 10,
    check: (pr) => {
        // Your validation logic
        return {
            passed: true/false,
            reason: 'Explanation if failed'
        };
    }
}
```

## 🔒 Security

- **Environment Variables**: All sensitive data in environment variables
- **Rate Limiting**: API endpoints are rate-limited
- **Input Validation**: All inputs are validated and sanitized
- **HTTPS Support**: SSL/TLS configuration included
- **Session Security**: Secure session management

## 📊 Monitoring

### Health Checks
- **Application**: `/health`
- **GitHub API**: `/api/github/health`
- **AI Service**: `/api/ai/health`

### Logging
- **Application Logs**: `/app/logs/`
- **Error Tracking**: Structured error logging
- **Performance Metrics**: Request timing and AI response times

## 🚀 Deployment

### Production Deployment
```bash
# Set production environment
export NODE_ENV=production

# Start with production compose
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Enable SSL (optional)
# Configure nginx.conf for your domain
```

### Scaling
- **Horizontal**: Multiple app instances behind load balancer
- **Database**: PostgreSQL with read replicas
- **Caching**: Redis for session and response caching

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request (and watch it get analyzed! 🎉)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Common Issues

**Ollama Connection Failed**
```bash
# Check if Ollama is running
docker ps | grep ollama

# Verify network connectivity
docker exec pr-review-app curl http://host.docker.internal:11434/api/tags
```

**GitHub API Rate Limits**
- Use authenticated requests with personal access token
- Implement request caching for frequently accessed data

**Model Not Found**
```bash
# Install the required model
docker exec ollama ollama pull GandalfBaum/llama3.2-claude3.7:latest
```

### Getting Help
- Check application logs: `docker-compose logs pr-review-app`
- Review health endpoints for service status
- Verify environment configuration

---

**Built with ❤️ for better code reviews and team collaboration**
