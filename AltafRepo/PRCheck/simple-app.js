const express = require('express');
const session = require('express-session');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Data storage paths
const DATA_DIR = path.join(__dirname, 'data');
const REVIEWS_FILE = path.join(DATA_DIR, 'reviews.json');
const USERS_FILE = path.join(DATA_DIR, 'users.json');

// Ensure data directory exists
async function ensureDataDir() {
    try {
        await fs.mkdir(DATA_DIR, { recursive: true });
        
        // Initialize files if they don't exist
        try {
            await fs.access(REVIEWS_FILE);
        } catch {
            await fs.writeFile(REVIEWS_FILE, JSON.stringify([], null, 2));
        }
        
        try {
            await fs.access(USERS_FILE);
        } catch {
            await fs.writeFile(USERS_FILE, JSON.stringify({}, null, 2));
        }
    } catch (error) {
        console.error('Error setting up data directory:', error);
    }
}

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));

app.use(session({
    secret: process.env.SESSION_SECRET || 'your-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 } // 24 hours
}));

// View engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// GitHub OAuth configuration
const GITHUB_CLIENT_ID = process.env.GITHUB_CLIENT_ID;
const GITHUB_CLIENT_SECRET = process.env.GITHUB_CLIENT_SECRET;
const GITHUB_CALLBACK_URL = process.env.GITHUB_CALLBACK_URL || 'http://localhost:3000/auth/github/callback';

// Helper functions for data storage
async function loadReviews() {
    try {
        const data = await fs.readFile(REVIEWS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return [];
    }
}

async function saveReviews(reviews) {
    await fs.writeFile(REVIEWS_FILE, JSON.stringify(reviews, null, 2));
}

async function loadUsers() {
    try {
        const data = await fs.readFile(USERS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return {};
    }
}

async function saveUsers(users) {
    await fs.writeFile(USERS_FILE, JSON.stringify(users, null, 2));
}

// Load checklist configuration
let checklistConfig = null;
async function loadChecklistConfig() {
    if (!checklistConfig) {
        try {
            const configData = await fs.readFile('./checklist-config.json', 'utf8');
            checklistConfig = JSON.parse(configData);
        } catch (error) {
            console.error('Error loading checklist config:', error);
            // Fallback to basic configuration
            checklistConfig = getDefaultConfig();
        }
    }
    return checklistConfig;
}

// Enhanced PR analysis function with configurable rules
async function analyzePR(prData) {
    const config = await loadChecklistConfig();
    const checks = [];
    let totalWeight = 0;
    let earnedWeight = 0;
    const suggestions = [];
    const categories = {};

    console.log('Analyzing PR:', {
        title: prData.title,
        body: prData.body?.substring(0, 100) + '...',
        changed_files: prData.changed_files,
        additions: prData.additions,
        head_ref: prData.head?.ref,
        base_ref: prData.base?.ref
    });

    // Process each category
    for (const [categoryId, category] of Object.entries(config.categories)) {
        categories[categoryId] = {
            name: category.name,
            description: category.description,
            icon: category.icon,
            checks: [],
            passed: 0,
            total: 0
        };

        // Process each check in the category
        for (const check of category.checks) {
            const result = await evaluateCheck(check, prData);

            checks.push({
                id: check.id,
                name: check.name,
                category: categoryId,
                passed: result.passed,
                reason: result.reason,
                weight: check.weight,
                reference: check.reference,
                details: result.details
            });

            categories[categoryId].checks.push(result);
            categories[categoryId].total++;
            if (result.passed) {
                categories[categoryId].passed++;
                earnedWeight += check.weight;
            } else {
                suggestions.push({
                    title: check.name,
                    reason: result.reason,
                    category: category.name,
                    reference: check.reference,
                    priority: check.weight
                });
            }
            totalWeight += check.weight;
        }
    }

    // Analyze files for detailed review comments
    const fileAnalysis = await analyzeFiles(prData.files || [], prData.commits || []);

    const percentage = totalWeight > 0 ? Math.round((earnedWeight / totalWeight) * 100) : 0;
    const score = checks.filter(c => c.passed).length;
    const totalChecks = checks.length;

    // Sort suggestions by priority (weight)
    suggestions.sort((a, b) => b.priority - a.priority);

    return {
        score,
        totalChecks,
        percentage,
        checks,
        suggestions,
        categories,
        totalWeight,
        earnedWeight,
        analysis_timestamp: new Date().toISOString(),
        pr_info: {
            title: prData.title,
            body: prData.body?.substring(0, 200) + (prData.body?.length > 200 ? '...' : ''),
            changed_files: prData.changed_files,
            additions: prData.additions,
            deletions: prData.deletions,
            head_ref: prData.head?.ref,
            base_ref: prData.base?.ref,
            mergeable: prData.mergeable,
            html_url: prData.html_url,
            user: prData.user?.login,
            created_at: prData.created_at,
            updated_at: prData.updated_at
        },
        file_analysis: fileAnalysis,
        detailed_review: {
            files_changed: fileAnalysis.files.length,
            total_comments: fileAnalysis.total_comments,
            critical_issues: fileAnalysis.critical_issues,
            suggestions_count: fileAnalysis.suggestions_count,
            security_issues: fileAnalysis.security_issues
        }
    };
}

// Evaluate individual check
async function evaluateCheck(check, prData) {
    const details = {};

    try {
        switch (check.type) {
            case 'title_analysis':
                return evaluateTitleCheck(check, prData.title, details);

            case 'description_analysis':
                return evaluateDescriptionCheck(check, prData.body, details);

            case 'branch_analysis':
                return evaluateBranchCheck(check, prData, details);

            case 'scope_analysis':
                return evaluateScopeCheck(check, prData, details);

            case 'size_analysis':
                return evaluateSizeCheck(check, prData, details);

            case 'pr_status':
                return evaluatePRStatusCheck(check, prData, details);

            default:
                return {
                    passed: false,
                    reason: `Unknown check type: ${check.type}`,
                    details
                };
        }
    } catch (error) {
        console.error(`Error evaluating check ${check.id}:`, error);
        return {
            passed: false,
            reason: `Error evaluating check: ${error.message}`,
            details
        };
    }
}

// Title evaluation functions
function evaluateTitleCheck(check, title, details) {
    if (!title) {
        return {
            passed: false,
            reason: 'No title provided',
            details: { ...details, title: null }
        };
    }

    details.title = title;
    details.title_length = title.length;

    switch (check.rule) {
        case 'length <= 50':
            const passed = title.length <= 50;
            return {
                passed,
                reason: passed ? check.pass_message : check.fail_message.replace('{length}', title.length),
                details
            };

        case 'starts_with_capital':
            const isCapitalized = /^[A-Z]/.test(title);
            return {
                passed: isCapitalized,
                reason: isCapitalized ? check.pass_message : check.fail_message,
                details
            };

        case "!ends_with('.')":
            const noPeriod = !title.endsWith('.');
            return {
                passed: noPeriod,
                reason: noPeriod ? check.pass_message : check.fail_message,
                details
            };

        case 'imperative_mood':
            const imperativeWords = check.imperative_words || [];
            const isImperative = imperativeWords.some(word =>
                title.toLowerCase().startsWith(word.toLowerCase())
            );
            return {
                passed: isImperative,
                reason: isImperative ? check.pass_message : check.fail_message,
                details: { ...details, imperative_words_checked: imperativeWords }
            };

        default:
            return {
                passed: false,
                reason: `Unknown title rule: ${check.rule}`,
                details
            };
    }
}

// Description evaluation functions
function evaluateDescriptionCheck(check, body, details) {
    details.body_length = body ? body.length : 0;
    details.body_preview = body ? body.substring(0, 100) + '...' : null;

    switch (check.rule) {
        case 'length > 50':
            const hasDescription = body && body.length > 50;
            return {
                passed: hasDescription,
                reason: hasDescription ? check.pass_message : check.fail_message,
                details
            };

        case 'contains_testing_keywords':
            if (!body) {
                return {
                    passed: false,
                    reason: check.fail_message,
                    details
                };
            }

            const keywords = check.keywords || [];
            const bodyLower = body.toLowerCase();
            const foundKeywords = keywords.filter(keyword =>
                bodyLower.includes(keyword.toLowerCase())
            );

            const hasTestingInfo = foundKeywords.length > 0;
            return {
                passed: hasTestingInfo,
                reason: hasTestingInfo ? check.pass_message : check.fail_message,
                details: { ...details, found_keywords: foundKeywords }
            };

        case 'contains_issue_references':
            if (!body) {
                return {
                    passed: false,
                    reason: check.fail_message,
                    details
                };
            }

            const patterns = check.patterns || [];
            const foundReferences = patterns.some(pattern => {
                const regex = new RegExp(pattern, 'i');
                return regex.test(body);
            });

            return {
                passed: foundReferences,
                reason: foundReferences ? check.pass_message : check.fail_message,
                details
            };

        default:
            return {
                passed: false,
                reason: `Unknown description rule: ${check.rule}`,
                details
            };
    }
}

// Branch evaluation functions
function evaluateBranchCheck(check, prData, details) {
    const headRef = prData.head?.ref;
    const baseRef = prData.base?.ref;

    details.head_ref = headRef;
    details.base_ref = baseRef;

    switch (check.rule) {
        case 'matches_pattern':
            if (!headRef) {
                return {
                    passed: false,
                    reason: 'No branch information available',
                    details
                };
            }

            const pattern = new RegExp(check.pattern);
            const matches = pattern.test(headRef);
            return {
                passed: matches,
                reason: matches ? check.pass_message : check.fail_message,
                details
            };

        case 'target_branch_valid':
            if (!baseRef) {
                return {
                    passed: false,
                    reason: 'No target branch information available',
                    details
                };
            }

            const validTargets = check.valid_targets || ['main', 'master'];
            const isValidTarget = validTargets.includes(baseRef.toLowerCase());
            return {
                passed: isValidTarget,
                reason: isValidTarget ? check.pass_message : check.fail_message,
                details: { ...details, valid_targets: validTargets }
            };

        default:
            return {
                passed: false,
                reason: `Unknown branch rule: ${check.rule}`,
                details
            };
    }
}

// Scope evaluation functions
function evaluateScopeCheck(check, prData, details) {
    const changedFiles = prData.changed_files || 0;
    details.changed_files = changedFiles;

    switch (check.rule) {
        case 'changed_files <= 10':
            const reasonableScope = changedFiles <= 10;
            return {
                passed: reasonableScope,
                reason: reasonableScope ? check.pass_message : check.fail_message.replace('{changed_files}', changedFiles),
                details
            };

        default:
            return {
                passed: false,
                reason: `Unknown scope rule: ${check.rule}`,
                details
            };
    }
}

// Size evaluation functions
function evaluateSizeCheck(check, prData, details) {
    const additions = prData.additions || 0;
    details.additions = additions;

    switch (check.rule) {
        case 'additions <= 500':
            const manageableSize = additions <= 500;
            return {
                passed: manageableSize,
                reason: manageableSize ? check.pass_message : check.fail_message.replace('{additions}', additions),
                details
            };

        default:
            return {
                passed: false,
                reason: `Unknown size rule: ${check.rule}`,
                details
            };
    }
}

// PR Status evaluation functions
function evaluatePRStatusCheck(check, prData, details) {
    details.mergeable = prData.mergeable;
    details.state = prData.state;

    switch (check.rule) {
        case 'mergeable == true':
            const noConflicts = prData.mergeable === true;
            return {
                passed: noConflicts,
                reason: noConflicts ? check.pass_message : check.fail_message,
                details
            };

        default:
            return {
                passed: false,
                reason: `Unknown PR status rule: ${check.rule}`,
                details
            };
    }
}

// File analysis function for detailed line-by-line review
async function analyzeFiles(files, commits) {
    const fileAnalysis = {
        files: [],
        total_comments: 0,
        critical_issues: 0,
        suggestions_count: 0,
        security_issues: 0,
        code_quality_issues: 0
    };

    for (const file of files) {
        const fileReview = {
            filename: file.filename,
            status: file.status,
            additions: file.additions,
            deletions: file.deletions,
            changes: file.changes,
            patch: file.patch,
            comments: [],
            issues: [],
            suggestions: [],
            security_concerns: [],
            code_quality: {
                complexity: 'low',
                maintainability: 'good',
                readability: 'good'
            }
        };

        // Analyze file content and changes
        if (file.patch) {
            const lineComments = analyzeFilePatch(file);
            fileReview.comments = lineComments.comments;
            fileReview.issues = lineComments.issues;
            fileReview.suggestions = lineComments.suggestions;
            fileReview.security_concerns = lineComments.security_concerns;

            // Update counters
            fileAnalysis.total_comments += lineComments.comments.length;
            fileAnalysis.critical_issues += lineComments.issues.filter(i => i.severity === 'critical').length;
            fileAnalysis.suggestions_count += lineComments.suggestions.length;
            fileAnalysis.security_issues += lineComments.security_concerns.length;
        }

        // Analyze file type and provide specific recommendations
        const fileTypeAnalysis = analyzeFileType(file);
        fileReview.file_type_recommendations = fileTypeAnalysis;

        fileAnalysis.files.push(fileReview);
    }

    return fileAnalysis;
}

// Analyze individual file patch for line-by-line comments
function analyzeFilePatch(file) {
    const comments = [];
    const issues = [];
    const suggestions = [];
    const security_concerns = [];

    if (!file.patch) {
        return { comments, issues, suggestions, security_concerns };
    }

    const lines = file.patch.split('\n');
    let currentLine = 0;
    let inHunk = false;

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        // Track line numbers
        if (line.startsWith('@@')) {
            const match = line.match(/@@ -\d+,?\d* \+(\d+),?\d* @@/);
            if (match) {
                currentLine = parseInt(match[1]) - 1;
                inHunk = true;
            }
            continue;
        }

        if (!inHunk) continue;

        // Only analyze added lines (starting with +)
        if (line.startsWith('+') && !line.startsWith('+++')) {
            currentLine++;
            const codeContent = line.substring(1);

            // Analyze the line for various issues
            const lineAnalysis = analyzeCodeLine(codeContent, currentLine, file.filename);

            if (lineAnalysis.comments.length > 0) {
                comments.push(...lineAnalysis.comments.map(c => ({
                    ...c,
                    line: currentLine,
                    file: file.filename
                })));
            }

            if (lineAnalysis.issues.length > 0) {
                issues.push(...lineAnalysis.issues.map(i => ({
                    ...i,
                    line: currentLine,
                    file: file.filename
                })));
            }

            if (lineAnalysis.suggestions.length > 0) {
                suggestions.push(...lineAnalysis.suggestions.map(s => ({
                    ...s,
                    line: currentLine,
                    file: file.filename
                })));
            }

            if (lineAnalysis.security_concerns.length > 0) {
                security_concerns.push(...lineAnalysis.security_concerns.map(sc => ({
                    ...sc,
                    line: currentLine,
                    file: file.filename
                })));
            }
        } else if (line.startsWith(' ')) {
            currentLine++;
        }
    }

    return { comments, issues, suggestions, security_concerns };
}

// Analyze individual code line
function analyzeCodeLine(code, lineNumber, filename) {
    const comments = [];
    const issues = [];
    const suggestions = [];
    const security_concerns = [];

    const trimmedCode = code.trim();

    // Skip empty lines and comments
    if (!trimmedCode || trimmedCode.startsWith('//') || trimmedCode.startsWith('#') || trimmedCode.startsWith('/*')) {
        return { comments, issues, suggestions, security_concerns };
    }

    // Security analysis
    const securityPatterns = [
        { pattern: /password\s*=\s*["'][^"']+["']/i, message: "Hardcoded password detected", severity: "critical" },
        { pattern: /api[_-]?key\s*=\s*["'][^"']+["']/i, message: "Hardcoded API key detected", severity: "critical" },
        { pattern: /secret\s*=\s*["'][^"']+["']/i, message: "Hardcoded secret detected", severity: "critical" },
        { pattern: /eval\s*\(/i, message: "Use of eval() can be dangerous", severity: "high" },
        { pattern: /innerHTML\s*=/i, message: "Direct innerHTML assignment can lead to XSS", severity: "medium" },
        { pattern: /document\.write\s*\(/i, message: "document.write can be unsafe", severity: "medium" }
    ];

    securityPatterns.forEach(pattern => {
        if (pattern.pattern.test(trimmedCode)) {
            security_concerns.push({
                type: "security",
                severity: pattern.severity,
                message: pattern.message,
                code: trimmedCode,
                suggestion: "Consider using environment variables or secure storage for sensitive data"
            });
        }
    });

    // Code quality analysis
    const qualityPatterns = [
        { pattern: /console\.log\s*\(/i, message: "Console.log statement found", type: "cleanup", severity: "low" },
        { pattern: /debugger\s*;?/i, message: "Debugger statement found", type: "cleanup", severity: "medium" },
        { pattern: /TODO|FIXME|HACK/i, message: "TODO/FIXME comment found", type: "maintenance", severity: "low" },
        { pattern: /var\s+\w+/i, message: "Consider using 'let' or 'const' instead of 'var'", type: "modernization", severity: "low" },
        { pattern: /==\s*[^=]/i, message: "Consider using strict equality (===)", type: "best_practice", severity: "low" }
    ];

    qualityPatterns.forEach(pattern => {
        if (pattern.pattern.test(trimmedCode)) {
            if (pattern.severity === 'medium' || pattern.severity === 'high') {
                issues.push({
                    type: pattern.type,
                    severity: pattern.severity,
                    message: pattern.message,
                    code: trimmedCode
                });
            } else {
                suggestions.push({
                    type: pattern.type,
                    severity: pattern.severity,
                    message: pattern.message,
                    code: trimmedCode
                });
            }
        }
    });

    // Line length check
    if (trimmedCode.length > 120) {
        suggestions.push({
            type: "formatting",
            severity: "low",
            message: `Line is too long (${trimmedCode.length} characters). Consider breaking it up.`,
            code: trimmedCode.substring(0, 50) + "..."
        });
    }

    // Complex line detection
    const complexity = calculateLineComplexity(trimmedCode);
    if (complexity > 10) {
        suggestions.push({
            type: "complexity",
            severity: "medium",
            message: `Line appears complex (complexity: ${complexity}). Consider simplifying.`,
            code: trimmedCode.substring(0, 50) + "..."
        });
    }

    // Add general code review comments for significant changes
    if (trimmedCode.length > 20 && !trimmedCode.includes('import') && !trimmedCode.includes('require')) {
        comments.push({
            type: "review",
            message: generateCodeReviewComment(trimmedCode, filename),
            code: trimmedCode.substring(0, 100) + (trimmedCode.length > 100 ? "..." : "")
        });
    }

    return { comments, issues, suggestions, security_concerns };
}

// Calculate line complexity
function calculateLineComplexity(code) {
    let complexity = 1;

    // Count operators and keywords that increase complexity
    const complexityPatterns = [
        /if\s*\(/g, /else/g, /for\s*\(/g, /while\s*\(/g, /switch\s*\(/g,
        /case\s+/g, /catch\s*\(/g, /&&/g, /\|\|/g, /\?/g, /:/g
    ];

    complexityPatterns.forEach(pattern => {
        const matches = code.match(pattern);
        if (matches) {
            complexity += matches.length;
        }
    });

    return complexity;
}

// Generate contextual code review comment
function generateCodeReviewComment(code, filename) {
    const fileExt = filename.split('.').pop().toLowerCase();

    // Language-specific comments
    const languageComments = {
        'js': 'Consider adding JSDoc comments for better documentation',
        'ts': 'Ensure proper TypeScript types are defined',
        'py': 'Consider following PEP 8 style guidelines',
        'java': 'Ensure proper exception handling',
        'css': 'Consider using CSS custom properties for maintainability',
        'html': 'Ensure proper semantic HTML structure',
        'json': 'Validate JSON structure and consider schema validation'
    };

    if (languageComments[fileExt]) {
        return languageComments[fileExt];
    }

    // Generic comments based on code patterns
    if (code.includes('function') || code.includes('def ') || code.includes('method')) {
        return 'Consider adding documentation for this function/method';
    }

    if (code.includes('class ')) {
        return 'Ensure class follows single responsibility principle';
    }

    if (code.includes('async') || code.includes('await') || code.includes('Promise')) {
        return 'Ensure proper error handling for async operations';
    }

    return 'Code looks good, consider adding tests if not already covered';
}

// Analyze file type for specific recommendations
function analyzeFileType(file) {
    const filename = file.filename;
    const fileExt = filename.split('.').pop().toLowerCase();
    const recommendations = [];

    const fileTypeRules = {
        'js': [
            'Consider using ESLint for code quality',
            'Add unit tests with Jest or similar framework',
            'Use modern ES6+ features where appropriate'
        ],
        'ts': [
            'Ensure strict TypeScript configuration',
            'Use proper type definitions',
            'Consider using interfaces for object shapes'
        ],
        'py': [
            'Follow PEP 8 style guidelines',
            'Add type hints for better code documentation',
            'Use virtual environments for dependencies'
        ],
        'css': [
            'Consider using CSS preprocessors like Sass',
            'Use CSS custom properties for theming',
            'Ensure responsive design principles'
        ],
        'html': [
            'Use semantic HTML elements',
            'Ensure accessibility standards (WCAG)',
            'Validate HTML structure'
        ],
        'md': [
            'Follow Markdown best practices',
            'Use proper heading hierarchy',
            'Include table of contents for long documents'
        ]
    };

    if (fileTypeRules[fileExt]) {
        recommendations.push(...fileTypeRules[fileExt]);
    }

    // File size recommendations
    if (file.additions > 100) {
        recommendations.push('Large file change detected. Consider breaking into smaller commits.');
    }

    if (file.deletions > 50) {
        recommendations.push('Significant code deletion. Ensure no breaking changes.');
    }

    return recommendations;
}

// Default configuration fallback
function getDefaultConfig() {
    return {
        categories: {
            git_commit_guidelines: {
                name: "Git Commit Guidelines",
                description: "Basic commit message rules",
                icon: "fab fa-git-alt",
                checks: [
                    {
                        id: "title_length",
                        name: "Title Length",
                        type: "title_analysis",
                        rule: "length <= 50",
                        weight: 1,
                        pass_message: "Title follows 50-character guideline",
                        fail_message: "Title too long"
                    }
                ]
            }
        },
        scoring: {
            excellent: { min_percentage: 90, color: "success" },
            good: { min_percentage: 75, color: "primary" },
            needs_improvement: { min_percentage: 60, color: "warning" },
            poor: { min_percentage: 0, color: "danger" }
        }
    };
}

// Routes
app.get('/', (req, res) => {
    res.render('layout', {
        title: 'Simple PR Review Tool',
        user: req.session.user || null,
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
        page: 'index'
    });
});

// GitHub OAuth routes
app.get('/auth/github', (req, res) => {
    if (!GITHUB_CLIENT_ID) {
        return res.status(500).send('GitHub OAuth not configured');
    }
    
    const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${GITHUB_CLIENT_ID}&scope=repo&redirect_uri=${encodeURIComponent(GITHUB_CALLBACK_URL)}`;
    res.redirect(githubAuthUrl);
});

app.get('/auth/github/callback', async (req, res) => {
    const { code } = req.query;
    
    if (!code) {
        return res.redirect('/?error=no_code');
    }
    
    try {
        // Exchange code for access token
        const tokenResponse = await axios.post('https://github.com/login/oauth/access_token', {
            client_id: GITHUB_CLIENT_ID,
            client_secret: GITHUB_CLIENT_SECRET,
            code: code
        }, {
            headers: { Accept: 'application/json' }
        });
        
        const accessToken = tokenResponse.data.access_token;
        
        // Get user info
        const userResponse = await axios.get('https://api.github.com/user', {
            headers: { Authorization: `token ${accessToken}` }
        });
        
        const user = {
            id: userResponse.data.id,
            login: userResponse.data.login,
            name: userResponse.data.name,
            avatar_url: userResponse.data.avatar_url,
            accessToken: accessToken
        };
        
        // Save user session
        req.session.user = user;
        
        // Save user to local storage
        const users = await loadUsers();
        users[user.id] = user;
        await saveUsers(users);
        
        res.redirect('/dashboard');
    } catch (error) {
        console.error('GitHub OAuth error:', error);
        res.redirect('/?error=oauth_failed');
    }
});

app.get('/logout', (req, res) => {
    req.session.destroy();
    res.redirect('/');
});

app.get('/dashboard', async (req, res) => {
    if (!req.session.user) {
        return res.redirect('/');
    }

    const reviews = await loadReviews();
    const userReviews = reviews.filter(r => r.userId === req.session.user.id);

    res.render('layout', {
        title: 'Dashboard - PR Review Tool',
        user: req.session.user,
        reviews: userReviews,
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
        page: 'dashboard'
    });
});

app.get('/review/new', (req, res) => {
    res.render('layout', {
        title: 'New PR Review',
        user: req.session.user || null,
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
        page: 'new-review'
    });
});

// Results page route
app.get('/review/results', (req, res) => {
    res.render('layout', {
        title: 'PR Analysis Results',
        user: req.session.user || null,
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
        page: 'results'
    });
});

// Individual review details route
app.get('/review/:id', async (req, res) => {
    try {
        const reviewId = req.params.id;
        const reviews = await loadReviews();
        const review = reviews.find(r => r.id === reviewId);

        if (!review) {
            return res.status(404).render('layout', {
                title: 'Review Not Found',
                user: req.session.user || null,
                githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
                page: 'not-found'
            });
        }

        // Check if user has access to this review
        if (req.session.user && review.userId !== req.session.user.id) {
            return res.status(403).render('layout', {
                title: 'Access Denied',
                user: req.session.user || null,
                githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
                page: 'access-denied'
            });
        }

        res.render('layout', {
            title: `Review: ${review.prData.title}`,
            user: req.session.user || null,
            githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
            page: 'review-details',
            review: review
        });
    } catch (error) {
        console.error('Error loading review:', error);
        res.status(500).render('layout', {
            title: 'Error',
            user: req.session.user || null,
            githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
            page: 'error'
        });
    }
});

app.post('/review/analyze', async (req, res) => {
    try {
        const { repoUrl, prNumber } = req.body;

        if (!repoUrl || !prNumber) {
            return res.status(400).json({ error: 'Repository URL and PR number are required' });
        }

        // Parse GitHub URL
        const urlMatch = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
        if (!urlMatch) {
            return res.status(400).json({ error: 'Invalid GitHub repository URL' });
        }

        const [, owner, repo] = urlMatch;

        // Prepare headers for GitHub API
        const headers = {
            'User-Agent': 'Simple-PR-Review-Tool',
            'Accept': 'application/vnd.github.v3+json'
        };

        // Add authorization if user is logged in (required for private repos)
        if (req.session.user && req.session.user.accessToken) {
            headers['Authorization'] = `token ${req.session.user.accessToken}`;
        }

        // Fetch PR data from GitHub
        const prResponse = await axios.get(`https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}`, {
            headers
        });

        const prData = prResponse.data;

        // Fetch PR files and diff data for detailed analysis
        const filesResponse = await axios.get(`https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}/files`, {
            headers
        });

        const prFiles = filesResponse.data;

        // Fetch commits for commit message analysis
        const commitsResponse = await axios.get(`https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}/commits`, {
            headers
        });

        const prCommits = commitsResponse.data;
        
        // Analyze PR with enhanced data
        const analysis = await analyzePR({
            title: prData.title,
            body: prData.body,
            changed_files: prData.changed_files,
            additions: prData.additions,
            deletions: prData.deletions,
            head: prData.head,
            base: prData.base,
            mergeable: prData.mergeable,
            state: prData.state,
            files: prFiles,
            commits: prCommits,
            html_url: prData.html_url,
            user: prData.user,
            created_at: prData.created_at,
            updated_at: prData.updated_at
        });

        // Save review only if user is logged in
        let reviewId = null;
        if (req.session.user) {
            const review = {
                id: Date.now().toString(),
                userId: req.session.user.id,
                repoUrl,
                prNumber: parseInt(prNumber),
                prData: {
                    title: prData.title,
                    body: prData.body,
                    state: prData.state,
                    user: prData.user.login,
                    created_at: prData.created_at,
                    html_url: prData.html_url,
                    changed_files: prData.changed_files,
                    additions: prData.additions,
                    deletions: prData.deletions
                },
                analysis,
                createdAt: new Date().toISOString()
            };

            const reviews = await loadReviews();
            reviews.push(review);
            await saveReviews(reviews);
            reviewId = review.id;
        }

        res.json({
            success: true,
            analysis,
            reviewId,
            message: req.session.user ? 'Analysis completed and saved' : 'Analysis completed (login to save results)'
        });
    } catch (error) {
        console.error('Error analyzing PR:', error.response?.data || error.message);

        // Handle specific GitHub API errors
        if (error.response?.status === 404) {
            return res.status(404).json({
                error: 'Repository or PR not found',
                message: 'Please check the repository URL and PR number. For private repositories, please login with GitHub.'
            });
        }

        if (error.response?.status === 403) {
            return res.status(403).json({
                error: 'Access denied',
                message: 'This repository may be private. Please login with GitHub to access private repositories.'
            });
        }

        if (error.response?.status === 401) {
            return res.status(401).json({
                error: 'Authentication required',
                message: 'This repository is private. Please login with GitHub to access private repositories.'
            });
        }

        res.status(500).json({
            error: 'Failed to analyze PR',
            message: error.response?.data?.message || error.message
        });
    }
});

app.get('/review/:id', async (req, res) => {
    if (!req.session.user) {
        return res.redirect('/');
    }
    
    const reviews = await loadReviews();
    const review = reviews.find(r => r.id === req.params.id && r.userId === req.session.user.id);
    
    if (!review) {
        return res.status(404).send('Review not found');
    }
    
    res.render('layout', {
        title: `PR Review - ${review.prData.title}`,
        user: req.session.user,
        review,
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
        page: 'review-detail'
    });
});

// API endpoints
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        github_oauth: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET)
    });
});

// Get user repositories
app.get('/api/repositories', async (req, res) => {
    if (!req.session.user) {
        return res.status(401).json({ error: 'Not authenticated' });
    }

    try {
        const response = await axios.get('https://api.github.com/user/repos', {
            headers: {
                'Authorization': `token ${req.session.user.accessToken}`,
                'User-Agent': 'Simple-PR-Review-Tool'
            },
            params: {
                sort: 'updated',
                per_page: 100,
                type: 'all'
            }
        });

        const repos = response.data.map(repo => ({
            id: repo.id,
            name: repo.name,
            full_name: repo.full_name,
            private: repo.private,
            description: repo.description,
            updated_at: repo.updated_at,
            html_url: repo.html_url
        }));

        res.json(repos);
    } catch (error) {
        console.error('Error fetching repositories:', error.response?.data || error.message);
        res.status(500).json({
            error: 'Failed to fetch repositories',
            message: error.response?.data?.message || error.message
        });
    }
});

// Get repository pull requests
app.get('/api/repositories/:owner/:repo/pulls', async (req, res) => {
    if (!req.session.user) {
        return res.status(401).json({ error: 'Not authenticated' });
    }

    const { owner, repo } = req.params;
    const { state = 'open' } = req.query;

    try {
        const response = await axios.get(`https://api.github.com/repos/${owner}/${repo}/pulls`, {
            headers: {
                'Authorization': `token ${req.session.user.accessToken}`,
                'User-Agent': 'Simple-PR-Review-Tool'
            },
            params: {
                state,
                sort: 'updated',
                per_page: 50
            }
        });

        const pulls = response.data.map(pr => ({
            number: pr.number,
            title: pr.title,
            state: pr.state,
            created_at: pr.created_at,
            updated_at: pr.updated_at,
            user: pr.user.login,
            html_url: pr.html_url,
            head: {
                ref: pr.head.ref
            },
            base: {
                ref: pr.base.ref
            }
        }));

        res.json(pulls);
    } catch (error) {
        console.error('Error fetching pull requests:', error.response?.data || error.message);
        res.status(500).json({
            error: 'Failed to fetch pull requests',
            message: error.response?.data?.message || error.message
        });
    }
});

// Error handling
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({ error: 'Internal Server Error' });
});

app.use((req, res) => {
    res.status(404).send('Page not found');
});

// Initialize and start server
async function startServer() {
    await ensureDataDir();
    
    app.listen(PORT, () => {
        console.log(`🚀 Simple PR Review Tool running on port ${PORT}`);
        console.log(`📱 Access: http://localhost:${PORT}`);
        console.log(`🔐 GitHub OAuth: ${GITHUB_CLIENT_ID ? 'Configured' : 'Not configured'}`);
        
        if (!GITHUB_CLIENT_ID) {
            console.log('⚠️  Set GITHUB_CLIENT_ID and GITHUB_CLIENT_SECRET for GitHub login');
        }
    });
}

startServer().catch(console.error);

module.exports = app;
