const express = require('express');
const session = require('express-session');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Data storage paths
const DATA_DIR = path.join(__dirname, 'data');
const REVIEWS_FILE = path.join(DATA_DIR, 'reviews.json');
const USERS_FILE = path.join(DATA_DIR, 'users.json');

// Ensure data directory exists
async function ensureDataDir() {
    try {
        await fs.mkdir(DATA_DIR, { recursive: true });
        
        // Initialize files if they don't exist
        try {
            await fs.access(REVIEWS_FILE);
        } catch {
            await fs.writeFile(REVIEWS_FILE, JSON.stringify([], null, 2));
        }
        
        try {
            await fs.access(USERS_FILE);
        } catch {
            await fs.writeFile(USERS_FILE, JSON.stringify({}, null, 2));
        }
    } catch (error) {
        console.error('Error setting up data directory:', error);
    }
}

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));

app.use(session({
    secret: process.env.SESSION_SECRET || 'your-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 } // 24 hours
}));

// View engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// GitHub OAuth configuration
const GITHUB_CLIENT_ID = process.env.GITHUB_CLIENT_ID;
const GITHUB_CLIENT_SECRET = process.env.GITHUB_CLIENT_SECRET;
const GITHUB_CALLBACK_URL = process.env.GITHUB_CALLBACK_URL || 'http://localhost:3000/auth/github/callback';

// Helper functions for data storage
async function loadReviews() {
    try {
        const data = await fs.readFile(REVIEWS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return [];
    }
}

async function saveReviews(reviews) {
    await fs.writeFile(REVIEWS_FILE, JSON.stringify(reviews, null, 2));
}

async function loadUsers() {
    try {
        const data = await fs.readFile(USERS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return {};
    }
}

async function saveUsers(users) {
    await fs.writeFile(USERS_FILE, JSON.stringify(users, null, 2));
}

// Enhanced PR analysis function with comprehensive rules
// Based on prchecklist.txt and https://cbea.ms/git-commit/
function analyzePR(prData) {
    const checks = [];
    let score = 0;
    const suggestions = [];

    // Git Commit Message Rules (from https://cbea.ms/git-commit/)

    // Check 1: Title follows 50-character limit
    const titleLengthCheck = prData.title && prData.title.length <= 50;
    checks.push({
        name: 'Title Length (≤50 chars)',
        passed: titleLengthCheck,
        reason: titleLengthCheck ? 'Title follows 50-character guideline' : `Title too long (${prData.title?.length || 0} chars). Keep under 50 characters`
    });
    if (titleLengthCheck) score++;

    // Check 2: Title is capitalized
    const titleCapCheck = prData.title && /^[A-Z]/.test(prData.title);
    checks.push({
        name: 'Capitalized Title',
        passed: titleCapCheck,
        reason: titleCapCheck ? 'Title is properly capitalized' : 'Title should start with a capital letter'
    });
    if (titleCapCheck) score++;

    // Check 3: Title doesn't end with period
    const titlePeriodCheck = prData.title && !prData.title.endsWith('.');
    checks.push({
        name: 'No Period in Title',
        passed: titlePeriodCheck,
        reason: titlePeriodCheck ? 'Title correctly omits trailing period' : 'Title should not end with a period'
    });
    if (titlePeriodCheck) score++;

    // Check 4: Title uses imperative mood
    const imperativeWords = ['add', 'fix', 'update', 'remove', 'refactor', 'implement', 'create', 'delete', 'modify', 'improve', 'enhance', 'optimize'];
    const titleImperativeCheck = prData.title && imperativeWords.some(word =>
        prData.title.toLowerCase().startsWith(word)
    );
    checks.push({
        name: 'Imperative Mood',
        passed: titleImperativeCheck,
        reason: titleImperativeCheck ? 'Title uses imperative mood' : 'Title should use imperative mood (e.g., "Fix bug" not "Fixed bug")'
    });
    if (titleImperativeCheck) score++;

    // Check 5: Has meaningful description (explains WHY)
    const descCheck = prData.body && prData.body.length > 50;
    checks.push({
        name: 'Meaningful Description',
        passed: descCheck,
        reason: descCheck ? 'PR has detailed description explaining context' : 'PR should include detailed description explaining WHY (>50 characters)'
    });
    if (descCheck) score++;

    // Check 6: Branch naming follows convention (TICKET-123_description)
    const branchNameCheck = prData.head && prData.head.ref &&
                           /^[A-Z]+-\d+_/.test(prData.head.ref);
    checks.push({
        name: 'Branch Naming Convention',
        passed: branchNameCheck,
        reason: branchNameCheck ? 'Branch follows naming convention' : 'Branch should follow format: TICKET-123_description'
    });
    if (branchNameCheck) score++;

    // Check 7: Reasonable scope (≤10 files)
    const scopeCheck = !prData.changed_files || prData.changed_files <= 10;
    checks.push({
        name: 'Reasonable Scope',
        passed: scopeCheck,
        reason: scopeCheck ? 'PR has reasonable scope' : `Too many files changed (${prData.changed_files}). Consider breaking into smaller PRs`
    });
    if (scopeCheck) score++;

    // Check 8: Manageable size (≤500 additions)
    const sizeCheck = !prData.additions || prData.additions <= 500;
    checks.push({
        name: 'Manageable Size',
        passed: sizeCheck,
        reason: sizeCheck ? 'PR has manageable size' : `Too many additions (${prData.additions}). Consider breaking into smaller PRs`
    });
    if (sizeCheck) score++;

    // Check 9: Targets main/master branch
    const targetBranchCheck = prData.base && prData.base.ref &&
                             ['main', 'master'].includes(prData.base.ref.toLowerCase());
    checks.push({
        name: 'Targets Main/Master',
        passed: targetBranchCheck,
        reason: targetBranchCheck ? 'Correctly targets main/master branch' : 'Should target main/master branch for integration'
    });
    if (targetBranchCheck) score++;

    // Check 10: Includes testing information
    const testingInfoCheck = !prData.body ||
                            prData.body.toLowerCase().includes('test') ||
                            prData.body.toLowerCase().includes('manual') ||
                            prData.body.toLowerCase().includes('verify');
    checks.push({
        name: 'Testing Information',
        passed: testingInfoCheck,
        reason: testingInfoCheck ? 'Includes testing information' : 'Should include information about how changes were tested'
    });
    if (testingInfoCheck) score++;

    const totalChecks = checks.length;
    const percentage = Math.round((score / totalChecks) * 100);

    // Generate suggestions based on failed checks
    checks.forEach(check => {
        if (!check.passed) {
            suggestions.push({
                title: check.name,
                reason: check.reason
            });
        }
    });

    return {
        score,
        totalChecks,
        percentage,
        checks,
        suggestions
    };
}

// Routes
app.get('/', (req, res) => {
    res.render('layout', {
        title: 'Simple PR Review Tool',
        user: req.session.user || null,
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
        page: 'index'
    });
});

// GitHub OAuth routes
app.get('/auth/github', (req, res) => {
    if (!GITHUB_CLIENT_ID) {
        return res.status(500).send('GitHub OAuth not configured');
    }
    
    const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${GITHUB_CLIENT_ID}&scope=repo&redirect_uri=${encodeURIComponent(GITHUB_CALLBACK_URL)}`;
    res.redirect(githubAuthUrl);
});

app.get('/auth/github/callback', async (req, res) => {
    const { code } = req.query;
    
    if (!code) {
        return res.redirect('/?error=no_code');
    }
    
    try {
        // Exchange code for access token
        const tokenResponse = await axios.post('https://github.com/login/oauth/access_token', {
            client_id: GITHUB_CLIENT_ID,
            client_secret: GITHUB_CLIENT_SECRET,
            code: code
        }, {
            headers: { Accept: 'application/json' }
        });
        
        const accessToken = tokenResponse.data.access_token;
        
        // Get user info
        const userResponse = await axios.get('https://api.github.com/user', {
            headers: { Authorization: `token ${accessToken}` }
        });
        
        const user = {
            id: userResponse.data.id,
            login: userResponse.data.login,
            name: userResponse.data.name,
            avatar_url: userResponse.data.avatar_url,
            accessToken: accessToken
        };
        
        // Save user session
        req.session.user = user;
        
        // Save user to local storage
        const users = await loadUsers();
        users[user.id] = user;
        await saveUsers(users);
        
        res.redirect('/dashboard');
    } catch (error) {
        console.error('GitHub OAuth error:', error);
        res.redirect('/?error=oauth_failed');
    }
});

app.get('/logout', (req, res) => {
    req.session.destroy();
    res.redirect('/');
});

app.get('/dashboard', async (req, res) => {
    if (!req.session.user) {
        return res.redirect('/');
    }

    const reviews = await loadReviews();
    const userReviews = reviews.filter(r => r.userId === req.session.user.id);

    res.render('layout', {
        title: 'Dashboard - PR Review Tool',
        user: req.session.user,
        reviews: userReviews,
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
        page: 'dashboard'
    });
});

app.get('/review/new', (req, res) => {
    if (!req.session.user) {
        return res.redirect('/');
    }

    res.render('layout', {
        title: 'New PR Review',
        user: req.session.user,
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
        page: 'new-review'
    });
});

app.post('/review/analyze', async (req, res) => {
    if (!req.session.user) {
        return res.status(401).json({ error: 'Not authenticated' });
    }
    
    try {
        const { repoUrl, prNumber } = req.body;
        
        // Parse GitHub URL
        const urlMatch = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
        if (!urlMatch) {
            return res.status(400).json({ error: 'Invalid GitHub repository URL' });
        }
        
        const [, owner, repo] = urlMatch;
        
        // Fetch PR data from GitHub
        const prResponse = await axios.get(`https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}`, {
            headers: { Authorization: `token ${req.session.user.accessToken}` }
        });
        
        const prData = prResponse.data;
        
        // Analyze PR
        const analysis = analyzePR({
            title: prData.title,
            body: prData.body,
            changed_files: prData.changed_files,
            additions: prData.additions,
            head_ref: prData.head.ref
        });
        
        // Save review
        const review = {
            id: Date.now().toString(),
            userId: req.session.user.id,
            repoUrl,
            prNumber: parseInt(prNumber),
            prData: {
                title: prData.title,
                body: prData.body,
                state: prData.state,
                user: prData.user.login,
                created_at: prData.created_at,
                html_url: prData.html_url
            },
            analysis,
            createdAt: new Date().toISOString()
        };
        
        const reviews = await loadReviews();
        reviews.push(review);
        await saveReviews(reviews);
        
        res.json({ success: true, reviewId: review.id, analysis });
    } catch (error) {
        console.error('Error analyzing PR:', error);
        res.status(500).json({ 
            error: 'Failed to analyze PR', 
            message: error.response?.data?.message || error.message 
        });
    }
});

app.get('/review/:id', async (req, res) => {
    if (!req.session.user) {
        return res.redirect('/');
    }
    
    const reviews = await loadReviews();
    const review = reviews.find(r => r.id === req.params.id && r.userId === req.session.user.id);
    
    if (!review) {
        return res.status(404).send('Review not found');
    }
    
    res.render('layout', {
        title: `PR Review - ${review.prData.title}`,
        user: req.session.user,
        review,
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
        page: 'review-detail'
    });
});

// API endpoints
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        github_oauth: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET)
    });
});

// Get user repositories
app.get('/api/repositories', async (req, res) => {
    if (!req.session.user) {
        return res.status(401).json({ error: 'Not authenticated' });
    }

    try {
        const response = await axios.get('https://api.github.com/user/repos', {
            headers: {
                'Authorization': `token ${req.session.user.accessToken}`,
                'User-Agent': 'Simple-PR-Review-Tool'
            },
            params: {
                sort: 'updated',
                per_page: 100,
                type: 'all'
            }
        });

        const repos = response.data.map(repo => ({
            id: repo.id,
            name: repo.name,
            full_name: repo.full_name,
            private: repo.private,
            description: repo.description,
            updated_at: repo.updated_at,
            html_url: repo.html_url
        }));

        res.json(repos);
    } catch (error) {
        console.error('Error fetching repositories:', error.response?.data || error.message);
        res.status(500).json({
            error: 'Failed to fetch repositories',
            message: error.response?.data?.message || error.message
        });
    }
});

// Get repository pull requests
app.get('/api/repositories/:owner/:repo/pulls', async (req, res) => {
    if (!req.session.user) {
        return res.status(401).json({ error: 'Not authenticated' });
    }

    const { owner, repo } = req.params;
    const { state = 'open' } = req.query;

    try {
        const response = await axios.get(`https://api.github.com/repos/${owner}/${repo}/pulls`, {
            headers: {
                'Authorization': `token ${req.session.user.accessToken}`,
                'User-Agent': 'Simple-PR-Review-Tool'
            },
            params: {
                state,
                sort: 'updated',
                per_page: 50
            }
        });

        const pulls = response.data.map(pr => ({
            number: pr.number,
            title: pr.title,
            state: pr.state,
            created_at: pr.created_at,
            updated_at: pr.updated_at,
            user: pr.user.login,
            html_url: pr.html_url,
            head: {
                ref: pr.head.ref
            },
            base: {
                ref: pr.base.ref
            }
        }));

        res.json(pulls);
    } catch (error) {
        console.error('Error fetching pull requests:', error.response?.data || error.message);
        res.status(500).json({
            error: 'Failed to fetch pull requests',
            message: error.response?.data?.message || error.message
        });
    }
});

// Error handling
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({ error: 'Internal Server Error' });
});

app.use((req, res) => {
    res.status(404).send('Page not found');
});

// Initialize and start server
async function startServer() {
    await ensureDataDir();
    
    app.listen(PORT, () => {
        console.log(`🚀 Simple PR Review Tool running on port ${PORT}`);
        console.log(`📱 Access: http://localhost:${PORT}`);
        console.log(`🔐 GitHub OAuth: ${GITHUB_CLIENT_ID ? 'Configured' : 'Not configured'}`);
        
        if (!GITHUB_CLIENT_ID) {
            console.log('⚠️  Set GITHUB_CLIENT_ID and GITHUB_CLIENT_SECRET for GitHub login');
        }
    });
}

startServer().catch(console.error);

module.exports = app;
