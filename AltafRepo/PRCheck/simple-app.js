const express = require('express');
const session = require('express-session');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Data storage paths
const DATA_DIR = path.join(__dirname, 'data');
const REVIEWS_FILE = path.join(DATA_DIR, 'reviews.json');
const USERS_FILE = path.join(DATA_DIR, 'users.json');

// Ensure data directory exists
async function ensureDataDir() {
    try {
        await fs.mkdir(DATA_DIR, { recursive: true });
        
        // Initialize files if they don't exist
        try {
            await fs.access(REVIEWS_FILE);
        } catch {
            await fs.writeFile(REVIEWS_FILE, JSON.stringify([], null, 2));
        }
        
        try {
            await fs.access(USERS_FILE);
        } catch {
            await fs.writeFile(USERS_FILE, JSON.stringify({}, null, 2));
        }
    } catch (error) {
        console.error('Error setting up data directory:', error);
    }
}

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));

app.use(session({
    secret: process.env.SESSION_SECRET || 'your-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 } // 24 hours
}));

// View engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// GitHub OAuth configuration
const GITHUB_CLIENT_ID = process.env.GITHUB_CLIENT_ID;
const GITHUB_CLIENT_SECRET = process.env.GITHUB_CLIENT_SECRET;
const GITHUB_CALLBACK_URL = process.env.GITHUB_CALLBACK_URL || 'http://localhost:3000/auth/github/callback';

// Helper functions for data storage
async function loadReviews() {
    try {
        const data = await fs.readFile(REVIEWS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return [];
    }
}

async function saveReviews(reviews) {
    await fs.writeFile(REVIEWS_FILE, JSON.stringify(reviews, null, 2));
}

async function loadUsers() {
    try {
        const data = await fs.readFile(USERS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return {};
    }
}

async function saveUsers(users) {
    await fs.writeFile(USERS_FILE, JSON.stringify(users, null, 2));
}

// Simple PR analysis function (rule-based, no AI)
function analyzePR(prData) {
    const checks = [];
    let score = 0;
    
    // Title check
    const titleCheck = {
        name: 'Descriptive Title',
        passed: prData.title && prData.title.length > 10,
        reason: prData.title && prData.title.length > 10 ? '' : 'Title should be more descriptive (>10 characters)'
    };
    checks.push(titleCheck);
    if (titleCheck.passed) score++;
    
    // Description check
    const descCheck = {
        name: 'Has Description',
        passed: prData.body && prData.body.length > 20,
        reason: prData.body && prData.body.length > 20 ? '' : 'Description should provide more context (>20 characters)'
    };
    checks.push(descCheck);
    if (descCheck.passed) score++;
    
    // File changes check
    const filesCheck = {
        name: 'Reasonable Scope',
        passed: prData.changed_files <= 10,
        reason: prData.changed_files <= 10 ? '' : 'Consider breaking down large PRs (>10 files changed)'
    };
    checks.push(filesCheck);
    if (filesCheck.passed) score++;
    
    // Additions check
    const additionsCheck = {
        name: 'Manageable Size',
        passed: prData.additions <= 500,
        reason: prData.additions <= 500 ? '' : 'Large PRs are harder to review (>500 additions)'
    };
    checks.push(additionsCheck);
    if (additionsCheck.passed) score++;
    
    // Branch check
    const branchCheck = {
        name: 'Feature Branch',
        passed: prData.head_ref !== 'main' && prData.head_ref !== 'master',
        reason: prData.head_ref !== 'main' && prData.head_ref !== 'master' ? '' : 'Should use feature branch, not main/master'
    };
    checks.push(branchCheck);
    if (branchCheck.passed) score++;
    
    return {
        checks,
        score,
        totalChecks: checks.length,
        percentage: Math.round((score / checks.length) * 100),
        suggestions: checks.filter(c => !c.passed).map(c => ({
            title: c.name,
            reason: c.reason,
            priority: 'medium'
        }))
    };
}

// Routes
app.get('/', (req, res) => {
    res.render('simple-index', {
        title: 'Simple PR Review Tool',
        user: req.session.user || null,
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET)
    });
});

// GitHub OAuth routes
app.get('/auth/github', (req, res) => {
    if (!GITHUB_CLIENT_ID) {
        return res.status(500).send('GitHub OAuth not configured');
    }
    
    const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${GITHUB_CLIENT_ID}&scope=repo&redirect_uri=${encodeURIComponent(GITHUB_CALLBACK_URL)}`;
    res.redirect(githubAuthUrl);
});

app.get('/auth/github/callback', async (req, res) => {
    const { code } = req.query;
    
    if (!code) {
        return res.redirect('/?error=no_code');
    }
    
    try {
        // Exchange code for access token
        const tokenResponse = await axios.post('https://github.com/login/oauth/access_token', {
            client_id: GITHUB_CLIENT_ID,
            client_secret: GITHUB_CLIENT_SECRET,
            code: code
        }, {
            headers: { Accept: 'application/json' }
        });
        
        const accessToken = tokenResponse.data.access_token;
        
        // Get user info
        const userResponse = await axios.get('https://api.github.com/user', {
            headers: { Authorization: `token ${accessToken}` }
        });
        
        const user = {
            id: userResponse.data.id,
            login: userResponse.data.login,
            name: userResponse.data.name,
            avatar_url: userResponse.data.avatar_url,
            accessToken: accessToken
        };
        
        // Save user session
        req.session.user = user;
        
        // Save user to local storage
        const users = await loadUsers();
        users[user.id] = user;
        await saveUsers(users);
        
        res.redirect('/dashboard');
    } catch (error) {
        console.error('GitHub OAuth error:', error);
        res.redirect('/?error=oauth_failed');
    }
});

app.get('/logout', (req, res) => {
    req.session.destroy();
    res.redirect('/');
});

app.get('/dashboard', async (req, res) => {
    if (!req.session.user) {
        return res.redirect('/');
    }

    const reviews = await loadReviews();
    const userReviews = reviews.filter(r => r.userId === req.session.user.id);

    res.render('simple-dashboard', {
        title: 'Dashboard - PR Review Tool',
        user: req.session.user,
        reviews: userReviews,
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET)
    });
});

app.get('/review/new', (req, res) => {
    if (!req.session.user) {
        return res.redirect('/');
    }
    
    res.render('new-review', {
        title: 'New PR Review',
        user: req.session.user
    });
});

app.post('/review/analyze', async (req, res) => {
    if (!req.session.user) {
        return res.status(401).json({ error: 'Not authenticated' });
    }
    
    try {
        const { repoUrl, prNumber } = req.body;
        
        // Parse GitHub URL
        const urlMatch = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
        if (!urlMatch) {
            return res.status(400).json({ error: 'Invalid GitHub repository URL' });
        }
        
        const [, owner, repo] = urlMatch;
        
        // Fetch PR data from GitHub
        const prResponse = await axios.get(`https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}`, {
            headers: { Authorization: `token ${req.session.user.accessToken}` }
        });
        
        const prData = prResponse.data;
        
        // Analyze PR
        const analysis = analyzePR({
            title: prData.title,
            body: prData.body,
            changed_files: prData.changed_files,
            additions: prData.additions,
            head_ref: prData.head.ref
        });
        
        // Save review
        const review = {
            id: Date.now().toString(),
            userId: req.session.user.id,
            repoUrl,
            prNumber: parseInt(prNumber),
            prData: {
                title: prData.title,
                body: prData.body,
                state: prData.state,
                user: prData.user.login,
                created_at: prData.created_at,
                html_url: prData.html_url
            },
            analysis,
            createdAt: new Date().toISOString()
        };
        
        const reviews = await loadReviews();
        reviews.push(review);
        await saveReviews(reviews);
        
        res.json({ success: true, reviewId: review.id, analysis });
    } catch (error) {
        console.error('Error analyzing PR:', error);
        res.status(500).json({ 
            error: 'Failed to analyze PR', 
            message: error.response?.data?.message || error.message 
        });
    }
});

app.get('/review/:id', async (req, res) => {
    if (!req.session.user) {
        return res.redirect('/');
    }
    
    const reviews = await loadReviews();
    const review = reviews.find(r => r.id === req.params.id && r.userId === req.session.user.id);
    
    if (!review) {
        return res.status(404).send('Review not found');
    }
    
    res.render('review-detail', {
        title: `PR Review - ${review.prData.title}`,
        user: req.session.user,
        review
    });
});

// API endpoints
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        github_oauth: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET)
    });
});

// Error handling
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({ error: 'Internal Server Error' });
});

app.use((req, res) => {
    res.status(404).send('Page not found');
});

// Initialize and start server
async function startServer() {
    await ensureDataDir();
    
    app.listen(PORT, () => {
        console.log(`🚀 Simple PR Review Tool running on port ${PORT}`);
        console.log(`📱 Access: http://localhost:${PORT}`);
        console.log(`🔐 GitHub OAuth: ${GITHUB_CLIENT_ID ? 'Configured' : 'Not configured'}`);
        
        if (!GITHUB_CLIENT_ID) {
            console.log('⚠️  Set GITHUB_CLIENT_ID and GITHUB_CLIENT_SECRET for GitHub login');
        }
    });
}

startServer().catch(console.error);

module.exports = app;
