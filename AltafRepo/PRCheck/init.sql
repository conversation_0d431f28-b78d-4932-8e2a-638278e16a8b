-- Initialize database for PR Review Assistant

-- Create database if it doesn't exist
-- (This is handled by the postgres Docker image)

-- Create tables for the application

-- PR Analysis Results
CREATE TABLE IF NOT EXISTS pr_analyses (
    id SERIAL PRIMARY KEY,
    owner VARCHAR(255) NOT NULL,
    repo VARCHAR(255) NOT NULL,
    pr_number INTEGER NOT NULL,
    pr_title TEXT,
    pr_state VARCHAR(50),
    analysis_data JSONB NOT NULL,
    checklist_score INTEGER,
    weighted_score INTEGER,
    total_checks INTEGER,
    passed_checks INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(owner, repo, pr_number, created_at)
);

-- AI Analysis Cache
CREATE TABLE IF NOT EXISTS ai_analyses (
    id SERIAL PRIMARY KEY,
    content_hash VARCHAR(64) NOT NULL UNIQUE,
    analysis_type VARCHAR(50) NOT NULL,
    input_data JSONB NOT NULL,
    result_data JSONB NOT NULL,
    model_used VARCHAR(255),
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours')
);

-- User Sessions (if needed for extended functionality)
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    user_data JSONB,
    github_user VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours')
);

-- Webhook Events Log
CREATE TABLE IF NOT EXISTS webhook_events (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    event_action VARCHAR(100),
    repository VARCHAR(255),
    pr_number INTEGER,
    payload JSONB NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    processing_error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Application Metrics
CREATE TABLE IF NOT EXISTS app_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(255) NOT NULL,
    metric_value NUMERIC,
    metric_data JSONB,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_pr_analyses_repo ON pr_analyses(owner, repo);
CREATE INDEX IF NOT EXISTS idx_pr_analyses_pr_number ON pr_analyses(pr_number);
CREATE INDEX IF NOT EXISTS idx_pr_analyses_created_at ON pr_analyses(created_at);
CREATE INDEX IF NOT EXISTS idx_pr_analyses_score ON pr_analyses(weighted_score);

CREATE INDEX IF NOT EXISTS idx_ai_analyses_hash ON ai_analyses(content_hash);
CREATE INDEX IF NOT EXISTS idx_ai_analyses_type ON ai_analyses(analysis_type);
CREATE INDEX IF NOT EXISTS idx_ai_analyses_expires ON ai_analyses(expires_at);

CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);

CREATE INDEX IF NOT EXISTS idx_webhook_events_type ON webhook_events(event_type);
CREATE INDEX IF NOT EXISTS idx_webhook_events_repo ON webhook_events(repository);
CREATE INDEX IF NOT EXISTS idx_webhook_events_processed ON webhook_events(processed);
CREATE INDEX IF NOT EXISTS idx_webhook_events_created_at ON webhook_events(created_at);

CREATE INDEX IF NOT EXISTS idx_app_metrics_name ON app_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_app_metrics_recorded_at ON app_metrics(recorded_at);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_pr_analyses_updated_at 
    BEFORE UPDATE ON pr_analyses 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_sessions_updated_at 
    BEFORE UPDATE ON user_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a function to clean up expired records
CREATE OR REPLACE FUNCTION cleanup_expired_records()
RETURNS void AS $$
BEGIN
    -- Clean up expired AI analyses
    DELETE FROM ai_analyses WHERE expires_at < CURRENT_TIMESTAMP;
    
    -- Clean up expired user sessions
    DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP;
    
    -- Clean up old webhook events (keep last 30 days)
    DELETE FROM webhook_events WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    -- Clean up old metrics (keep last 90 days)
    DELETE FROM app_metrics WHERE recorded_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
    
    -- Clean up old PR analyses (keep last 6 months)
    DELETE FROM pr_analyses WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '6 months';
END;
$$ LANGUAGE plpgsql;

-- Create some useful views
CREATE OR REPLACE VIEW pr_analysis_summary AS
SELECT 
    owner,
    repo,
    COUNT(*) as total_analyses,
    AVG(weighted_score) as avg_score,
    COUNT(CASE WHEN weighted_score >= 80 THEN 1 END) as excellent_prs,
    COUNT(CASE WHEN weighted_score >= 60 AND weighted_score < 80 THEN 1 END) as good_prs,
    COUNT(CASE WHEN weighted_score < 60 THEN 1 END) as needs_work_prs,
    MAX(created_at) as last_analysis
FROM pr_analyses
GROUP BY owner, repo;

CREATE OR REPLACE VIEW recent_pr_analyses AS
SELECT 
    pa.*,
    EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - pa.created_at)) / 3600 as hours_ago
FROM pr_analyses pa
WHERE pa.created_at > CURRENT_TIMESTAMP - INTERVAL '7 days'
ORDER BY pa.created_at DESC;

-- Insert some initial metrics
INSERT INTO app_metrics (metric_name, metric_value, metric_data) VALUES
('app_initialized', 1, '{"version": "1.0.0", "initialized_at": "' || CURRENT_TIMESTAMP || '"}')
ON CONFLICT DO NOTHING;

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO pr_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO pr_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO pr_user;
