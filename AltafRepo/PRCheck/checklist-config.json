{"version": "1.0", "description": "Customizable PR Review Checklist Configuration", "categories": {"git_commit_guidelines": {"name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "icon": "fab fa-git-alt", "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "weight": 1, "type": "title_analysis", "rule": "length <= 50", "pass_message": "Title follows 50-character guideline", "fail_message": "Title too long ({length} chars). Keep under 50 characters", "reference": "https://cbea.ms/git-commit/#limit-50"}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "weight": 1, "type": "title_analysis", "rule": "starts_with_capital", "pass_message": "Title is properly capitalized", "fail_message": "Title should start with a capital letter", "reference": "https://cbea.ms/git-commit/#capitalize"}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "weight": 1, "type": "title_analysis", "rule": "!ends_with('.')", "pass_message": "Title correctly omits trailing period", "fail_message": "Title should not end with a period", "reference": "https://cbea.ms/git-commit/#end"}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "weight": 1, "type": "title_analysis", "rule": "imperative_mood", "pass_message": "Title uses imperative mood", "fail_message": "Title should use imperative mood (e.g., 'Fix bug' not 'Fixed bug')", "reference": "https://cbea.ms/git-commit/#imperative", "imperative_words": ["add", "fix", "update", "remove", "refactor", "implement", "create", "delete", "modify", "improve", "enhance", "optimize", "resolve", "correct", "adjust", "change", "merge", "revert", "bump", "upgrade", "downgrade"]}]}, "pr_quality": {"name": "PR Quality Checks", "description": "Best practices for pull request quality", "icon": "fas fa-code-branch", "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "weight": 2, "type": "description_analysis", "rule": "length > 50", "pass_message": "PR has detailed description explaining context", "fail_message": "PR should include detailed description explaining WHY (>50 characters)", "reference": "https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/proposing-changes-to-your-work-with-pull-requests/creating-a-pull-request"}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "weight": 1, "type": "branch_analysis", "rule": "matches_pattern", "pattern": "^(feature|bugfix|hotfix|chore|docs)/[a-z0-9-]+$|^[A-Z]+-\\d+_[a-z0-9-]+$", "pass_message": "Branch follows naming convention", "fail_message": "Branch should follow format: feature/description or TICKET-123_description", "reference": "https://nvie.com/posts/a-successful-git-branching-model/"}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "weight": 1, "type": "scope_analysis", "rule": "changed_files <= 10", "pass_message": "PR has reasonable scope", "fail_message": "Too many files changed ({changed_files}). Consider breaking into smaller PRs", "reference": "https://google.github.io/eng-practices/review/developer/small-cls.html"}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "weight": 1, "type": "size_analysis", "rule": "additions <= 500", "pass_message": "PR has manageable size", "fail_message": "Too many additions ({additions}). Consider breaking into smaller PRs", "reference": "https://google.github.io/eng-practices/review/developer/small-cls.html"}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "weight": 1, "type": "branch_analysis", "rule": "target_branch_valid", "valid_targets": ["main", "master", "develop"], "pass_message": "Correctly targets main/master branch", "fail_message": "Should target main/master branch for integration", "reference": "https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/proposing-changes-to-your-work-with-pull-requests/about-pull-requests"}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "weight": 2, "type": "description_analysis", "rule": "contains_testing_keywords", "keywords": ["test", "testing", "tested", "manual", "verify", "verified", "validation", "qa", "unit test", "integration test", "e2e"], "pass_message": "Includes testing information", "fail_message": "Should include information about how changes were tested", "reference": "https://google.github.io/eng-practices/review/developer/cl-descriptions.html"}]}, "code_quality": {"name": "Code Quality", "description": "Code quality and best practices", "icon": "fas fa-code", "checks": [{"id": "no_merge_conflicts", "name": "No Merge Conflicts", "description": "PR should not have merge conflicts", "weight": 3, "type": "pr_status", "rule": "mergeable == true", "pass_message": "No merge conflicts detected", "fail_message": "PR has merge conflicts that need to be resolved", "reference": "https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/addressing-merge-conflicts"}, {"id": "linked_issues", "name": "Linked Issues", "description": "PR should reference related issues", "weight": 1, "type": "description_analysis", "rule": "contains_issue_references", "patterns": ["#\\d+", "fixes #\\d+", "closes #\\d+", "resolves #\\d+"], "pass_message": "PR references related issues", "fail_message": "Consider linking related issues using #issue-number", "reference": "https://docs.github.com/en/issues/tracking-your-work-with-issues/linking-a-pull-request-to-an-issue"}]}}, "scoring": {"excellent": {"min_percentage": 90, "color": "success", "icon": "fas fa-star", "message": "Excellent PR! Follows best practices."}, "good": {"min_percentage": 75, "color": "primary", "icon": "fas fa-thumbs-up", "message": "Good PR with minor improvements needed."}, "needs_improvement": {"min_percentage": 60, "color": "warning", "icon": "fas fa-exclamation-triangle", "message": "PR needs some improvements."}, "poor": {"min_percentage": 0, "color": "danger", "icon": "fas fa-times-circle", "message": "PR needs significant improvements."}}}