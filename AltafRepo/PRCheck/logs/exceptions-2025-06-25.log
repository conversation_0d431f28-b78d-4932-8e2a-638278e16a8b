{"date":"Wed Jun 25 2025 16:44:53 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[1.25,0.6,0.52],"uptime":37959.43},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17456048,"rss":76152832},"pid":27,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:44:53","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:44:56 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[1.31,0.62,0.52],"uptime":37962.94},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17410760,"rss":76242944},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:44:56","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:45:00 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[1.31,0.62,0.52],"uptime":37966.55},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17490792,"rss":76218368},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:45:00","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:45:04 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[1.36,0.65,0.53],"uptime":37970.43},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23044096,"heapUsed":17482880,"rss":75988992},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:45:04","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:45:08 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[1.33,0.65,0.54],"uptime":37974.62},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23044096,"heapUsed":17481792,"rss":76259328},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:45:08","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:45:13 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[1.23,0.64,0.53],"uptime":37979.65},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23044096,"heapUsed":17482896,"rss":76079104},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:45:13","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:45:20 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[1.21,0.65,0.54],"uptime":37986.27},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23044096,"heapUsed":17308912,"rss":76058624},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:45:20","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:45:30 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[1.4,0.71,0.56],"uptime":37996.15},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17453848,"rss":76263424},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:45:30","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:45:46 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[1.3,0.72,0.56],"uptime":38012.58},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17445168,"rss":76308480},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:45:46","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:46:15 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[1.16,0.75,0.58],"uptime":38041.64},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23236608,"heapUsed":17693744,"rss":76152832},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:46:15","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:47:10 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.65,0.68,0.56],"uptime":38096.43},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23044096,"heapUsed":17334744,"rss":76345344},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:47:10","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 22:17:34 GMT+0530 (India Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier 'conflicts'\n/Users/<USER>/sfly/AltafRepo/PRCheck/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier 'conflicts'\n    at wrapSafe (node:internal/modules/cjs/loader:1469:18)\n    at Module._compile (node:internal/modules/cjs/loader:1491:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1691:10)\n    at Module.load (node:internal/modules/cjs/loader:1317:32)\n    at Module._load (node:internal/modules/cjs/loader:1127:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1339:12)\n    at require (node:internal/modules/helpers:126:16)\n    at Object.<anonymous> (/Users/<USER>/sfly/AltafRepo/PRCheck/server.js:13:26)","os":{"loadavg":[3.6640625,4.49072265625,4.12451171875],"uptime":198106},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.7.0/bin/node","/Users/<USER>/sfly/AltafRepo/PRCheck/server.js"],"cwd":"/Users/<USER>/sfly/AltafRepo/PRCheck","execPath":"/Users/<USER>/.nvm/versions/node/v22.7.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":65708,"external":3095615,"heapTotal":21626880,"heapUsed":16093552,"rss":74530816},"pid":39584,"uid":502,"version":"v22.7.0"},"service":"pr-review-assistant","stack":"/Users/<USER>/sfly/AltafRepo/PRCheck/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier 'conflicts'\n    at wrapSafe (node:internal/modules/cjs/loader:1469:18)\n    at Module._compile (node:internal/modules/cjs/loader:1491:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1691:10)\n    at Module.load (node:internal/modules/cjs/loader:1317:32)\n    at Module._load (node:internal/modules/cjs/loader:1127:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1339:12)\n    at require (node:internal/modules/helpers:126:16)\n    at Object.<anonymous> (/Users/<USER>/sfly/AltafRepo/PRCheck/server.js:13:26)","timestamp":"2025-06-25 22:17:34","trace":[{"column":18,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1469,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1491,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1691,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1317,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1127,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":315,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":217,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1339,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":126,"method":null,"native":false},{"column":26,"file":"/Users/<USER>/sfly/AltafRepo/PRCheck/server.js","function":null,"line":13,"method":null,"native":false}]}
{"date":"Wed Jun 25 2025 22:18:12 GMT+0530 (India Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier 'conflicts'\n/Users/<USER>/sfly/AltafRepo/PRCheck/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier 'conflicts'\n    at wrapSafe (node:internal/modules/cjs/loader:1469:18)\n    at Module._compile (node:internal/modules/cjs/loader:1491:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1691:10)\n    at Module.load (node:internal/modules/cjs/loader:1317:32)\n    at Module._load (node:internal/modules/cjs/loader:1127:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1339:12)\n    at require (node:internal/modules/helpers:126:16)\n    at Object.<anonymous> (/Users/<USER>/sfly/AltafRepo/PRCheck/routes/checklist.js:3:26)","os":{"loadavg":[4.*********,4.70166015625,4.21484375],"uptime":198144},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.7.0/bin/node","/Users/<USER>/sfly/AltafRepo/PRCheck/server.js"],"cwd":"/Users/<USER>/sfly/AltafRepo/PRCheck","execPath":"/Users/<USER>/.nvm/versions/node/v22.7.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":65708,"external":3095615,"heapTotal":22396928,"heapUsed":16583512,"rss":73924608},"pid":39903,"uid":502,"version":"v22.7.0"},"service":"pr-review-assistant","stack":"/Users/<USER>/sfly/AltafRepo/PRCheck/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier 'conflicts'\n    at wrapSafe (node:internal/modules/cjs/loader:1469:18)\n    at Module._compile (node:internal/modules/cjs/loader:1491:20)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1691:10)\n    at Module.load (node:internal/modules/cjs/loader:1317:32)\n    at Module._load (node:internal/modules/cjs/loader:1127:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:315:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Module.require (node:internal/modules/cjs/loader:1339:12)\n    at require (node:internal/modules/helpers:126:16)\n    at Object.<anonymous> (/Users/<USER>/sfly/AltafRepo/PRCheck/routes/checklist.js:3:26)","timestamp":"2025-06-25 22:18:12","trace":[{"column":18,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1469,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1491,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1691,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1317,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1127,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":315,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":217,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1339,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":126,"method":null,"native":false},{"column":26,"file":"/Users/<USER>/sfly/AltafRepo/PRCheck/routes/checklist.js","function":null,"line":3,"method":null,"native":false}]}
{"date":"Wed Jun 25 2025 16:49:35 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.55,0.62,0.56],"uptime":38241.86},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17387496,"rss":76529664},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:49:35","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:49:39 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.5,0.61,0.55],"uptime":38245.4},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17458672,"rss":76193792},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:49:39","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:49:43 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.46,0.6,0.55],"uptime":38249.12},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23044096,"heapUsed":17328608,"rss":76484608},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:49:43","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:49:46 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.51,0.61,0.55],"uptime":38252.99},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23236608,"heapUsed":17668720,"rss":76259328},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:49:46","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:49:51 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.51,0.61,0.55],"uptime":38257.19},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23044096,"heapUsed":17308536,"rss":75984896},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:49:51","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:49:56 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.47,0.6,0.55],"uptime":38262.25},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23236608,"heapUsed":17693168,"rss":76324864},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:49:56","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:50:02 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.39,0.58,0.54],"uptime":38268.93},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17440376,"rss":76378112},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:50:02","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:50:12 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.33,0.56,0.54],"uptime":38278.76},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17458160,"rss":76115968},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:50:12","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:50:29 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.26,0.53,0.53],"uptime":38295.12},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17444848,"rss":76292096},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:50:29","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:50:34 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.24,0.52,0.52],"uptime":38300.78},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17410800,"rss":76283904},"pid":22,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:50:34","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:50:38 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.22,0.51,0.52],"uptime":38304.34},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17608824,"rss":76255232},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:50:38","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:50:41 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.36,0.54,0.53],"uptime":38307.92},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17435736,"rss":76271616},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:50:41","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:50:45 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.36,0.54,0.53],"uptime":38311.74},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17498680,"rss":76210176},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:50:45","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:50:49 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.33,0.53,0.53],"uptime":38315.96},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":22974464,"heapUsed":17608088,"rss":76214272},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:50:49","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:50:54 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.3,0.52,0.52],"uptime":38320.96},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23236608,"heapUsed":17747712,"rss":76333056},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:50:54","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
{"date":"Wed Jun 25 2025 16:51:01 GMT+0000 (Coordinated Universal Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: Unexpected identifier\n/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","os":{"loadavg":[0.28,0.51,0.52],"uptime":38327.58},"process":{"argv":["/usr/local/bin/node","/app/server.js"],"cwd":"/app","execPath":"/usr/local/bin/node","gid":1000,"memoryUsage":{"arrayBuffers":312789,"external":2972987,"heapTotal":23236608,"heapUsed":17709936,"rss":76369920},"pid":23,"uid":1000,"version":"v18.20.8"},"service":"pr-review-assistant","stack":"/app/services/checklistService.js:139\n            conflicts: {\n            ^^^^^^^^^\n\nSyntaxError: Unexpected identifier\n    at internalCompileFunction (node:internal/vm:76:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1283:20)\n    at Module._compile (node:internal/modules/cjs/loader:1328:27)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)\n    at Module.load (node:internal/modules/cjs/loader:1203:32)\n    at Module._load (node:internal/modules/cjs/loader:1019:12)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:177:18)\n    at Object.<anonymous> (/app/server.js:13:26)\n    at Module._compile (node:internal/modules/cjs/loader:1364:14)","timestamp":"2025-06-25 16:51:01","trace":[{"column":18,"file":"node:internal/vm","function":"internalCompileFunction","line":76,"method":null,"native":false},{"column":20,"file":"node:internal/modules/cjs/loader","function":"wrapSafe","line":1283,"method":null,"native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1328,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1422,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1203,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1019,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1231,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":177,"method":null,"native":false},{"column":26,"file":"/app/server.js","function":null,"line":13,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1364,"method":"_compile","native":false}]}
