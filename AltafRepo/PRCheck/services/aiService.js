const axios = require('axios');
const logger = require('../utils/logger');

class AIService {
    constructor() {
        this.ollamaUrl = process.env.OLLAMA_URL || 'http://ollama:11434';
        this.model = process.env.AI_MODEL || 'smollm2:360m';
        this.fallbackModel = 'smollm2:360m';
    }

    async generateText(prompt, options = {}) {
        const payload = {
            model: options.model || this.model,
            prompt,
            stream: false,
            options: {
                temperature: options.temperature || 0.7,
                max_tokens: options.maxTokens || 2000,
                top_p: options.topP || 0.9,
                ...options.modelOptions
            }
        };

        try {
            const response = await axios.post(`${this.ollamaUrl}/api/generate`, payload, {
                timeout: options.timeout || 120000 // 2 minutes
            });

            return response.data.response;
        } catch (error) {
            logger.error('Error generating text with AI:', error);
            
            // Try fallback model if main model fails
            if (payload.model !== this.fallbackModel) {
                logger.info('Retrying with fallback model...');
                return this.generateText(prompt, { ...options, model: this.fallbackModel });
            }
            
            throw error;
        }
    }

    async analyzePRDescription(pr) {
        const prompt = `
Analyze this GitHub Pull Request and provide feedback based on best practices:

**PR Title:** ${pr.title}
**PR Description:** 
${pr.body || 'No description provided'}

**Files Changed:** ${pr.changed_files} files
**Additions:** +${pr.additions} lines
**Deletions:** -${pr.deletions} lines

Please analyze and provide:
1. **Title Quality**: Is the title descriptive and follows good conventions?
2. **Description Quality**: Does it explain WHY the change is needed?
3. **Context**: Is there enough context for reviewers?
4. **Testing Information**: Is testing information provided?
5. **Scope**: Is the change scope appropriate (not too large)?

Provide specific suggestions for improvement in a structured format.
`;

        try {
            return await this.generateText(prompt, { maxTokens: 1500 });
        } catch (error) {
            logger.error('Error analyzing PR description:', error);
            return 'AI analysis temporarily unavailable. Please review manually.';
        }
    }

    async analyzeCommitMessages(commits) {
        const commitMessages = commits.map(c => c.commit.message).join('\n---\n');
        
        const prompt = `
Analyze these commit messages for quality and best practices:

${commitMessages}

Based on https://cbea.ms/git-commit/, evaluate:
1. **Format**: Do they follow conventional format?
2. **Subject Lines**: Are they concise and descriptive?
3. **Body Content**: Do they explain WHY when needed?
4. **Consistency**: Are they consistent in style?

Provide specific suggestions for improvement.
`;

        try {
            return await this.generateText(prompt, { maxTokens: 1000 });
        } catch (error) {
            logger.error('Error analyzing commit messages:', error);
            return 'AI analysis temporarily unavailable.';
        }
    }

    async analyzeCodeChanges(files) {
        // Limit analysis to first 5 files to avoid token limits
        const filesToAnalyze = files.slice(0, 5);
        
        const filesSummary = filesToAnalyze.map(file => ({
            filename: file.filename,
            status: file.status,
            additions: file.additions,
            deletions: file.deletions,
            changes: file.changes,
            patch: file.patch ? file.patch.substring(0, 2000) : 'No patch available' // Limit patch size
        }));

        const prompt = `
Analyze these code changes for quality and potential issues:

${JSON.stringify(filesSummary, null, 2)}

Please evaluate:
1. **Code Quality**: Any obvious issues or improvements?
2. **Security**: Any potential security concerns?
3. **Performance**: Any performance implications?
4. **Best Practices**: Following language/framework conventions?
5. **Testing**: Are tests included for new functionality?

Provide constructive feedback and suggestions.
`;

        try {
            return await this.generateText(prompt, { 
                maxTokens: 2000,
                temperature: 0.3 // Lower temperature for code analysis
            });
        } catch (error) {
            logger.error('Error analyzing code changes:', error);
            return 'AI code analysis temporarily unavailable.';
        }
    }

    async generatePRSuggestions(checklistResults, pr) {
        const failedChecks = checklistResults.checks.filter(check => !check.passed);
        
        const prompt = `
Based on this PR review checklist analysis, generate specific suggestions for improvement:

**PR Title:** ${pr.title}
**Failed Checklist Items:**
${failedChecks.map(check => `- ${check.name}: ${check.reason}`).join('\n')}

**Overall Score:** ${checklistResults.score}/${checklistResults.totalChecks}

Please provide:
1. **Priority Issues**: Most important items to address
2. **Specific Actions**: Concrete steps to improve the PR
3. **Suggested Changes**: Exact text/format improvements
4. **Best Practices**: Relevant guidelines to follow

Format as actionable recommendations.
`;

        try {
            return await this.generateText(prompt, { maxTokens: 1500 });
        } catch (error) {
            logger.error('Error generating PR suggestions:', error);
            return 'AI suggestions temporarily unavailable.';
        }
    }

    async improveCommitMessage(originalMessage) {
        const prompt = `
Improve this commit message following best practices from https://cbea.ms/git-commit/:

Original: "${originalMessage}"

Requirements:
1. Separate subject from body with a blank line
2. Limit the subject line to 50 characters
3. Capitalize the subject line
4. Do not end the subject line with a period
5. Use the imperative mood in the subject line
6. Wrap the body at 72 characters
7. Use the body to explain what and why vs. how

Provide the improved commit message:
`;

        try {
            return await this.generateText(prompt, { 
                maxTokens: 500,
                temperature: 0.5
            });
        } catch (error) {
            logger.error('Error improving commit message:', error);
            return originalMessage;
        }
    }

    async improvePRDescription(pr) {
        const prompt = `
Improve this PR description to include all necessary context:

**Current Title:** ${pr.title}
**Current Description:** 
${pr.body || 'No description provided'}

**Files Changed:** ${pr.changed_files}
**Lines Changed:** +${pr.additions}/-${pr.deletions}

Create an improved PR description that includes:
1. **Context**: Why this change is needed
2. **What**: What changes were made (if not obvious from code)
3. **Testing**: How it was tested
4. **References**: Links to related issues/PRs if applicable
5. **Deployment Notes**: Any special deployment considerations

Format as a proper PR description with clear sections.
`;

        try {
            return await this.generateText(prompt, { maxTokens: 1000 });
        } catch (error) {
            logger.error('Error improving PR description:', error);
            return pr.body || '';
        }
    }

    async isModelAvailable(modelName = null) {
        try {
            const response = await axios.get(`${this.ollamaUrl}/api/tags`, {
                timeout: 5000
            });
            
            const models = response.data.models || [];
            const targetModel = modelName || this.model;
            
            return models.some(model => model.name === targetModel);
        } catch (error) {
            logger.error('Error checking model availability:', error);
            return false;
        }
    }

    async getAvailableModels() {
        try {
            const response = await axios.get(`${this.ollamaUrl}/api/tags`, {
                timeout: 5000
            });
            
            return response.data.models || [];
        } catch (error) {
            logger.error('Error fetching available models:', error);
            return [];
        }
    }
}

module.exports = new AIService();
