const logger = require('../utils/logger');

class ChecklistService {
    constructor() {
        this.checklist = this.loadChecklist();
    }

    loadChecklist() {
        return {
            title: {
                name: 'Descriptive Title',
                description: 'PR title should be descriptive and follow format: "component: Brief description"',
                weight: 10,
                check: (pr) => {
                    const title = pr.title || '';
                    const hasComponent = title.includes(':') || title.includes('-');
                    const isDescriptive = title.length > 10 && title.length < 100;
                    const notJustTicket = !(/^[A-Z]+-\d+$/.test(title.trim()));
                    
                    return {
                        passed: hasComponent && isDescriptive && notJustTicket,
                        reason: !hasComponent ? 'Title should include component (e.g., "api: Add new endpoint")' :
                               !isDescriptive ? 'Title should be descriptive (10-100 characters)' :
                               !notJustTicket ? 'Title should not be just a ticket number' : ''
                    };
                }
            },
            description: {
                name: 'Meaningful Description',
                description: 'PR should include context explaining WHY the change is needed',
                weight: 15,
                check: (pr) => {
                    const body = pr.body || '';
                    const hasContent = body.length > 50;
                    const hasContext = body.toLowerCase().includes('why') || 
                                     body.toLowerCase().includes('because') ||
                                     body.toLowerCase().includes('context') ||
                                     body.toLowerCase().includes('purpose');
                    
                    return {
                        passed: hasContent && hasContext,
                        reason: !hasContent ? 'Description should be at least 50 characters' :
                               !hasContext ? 'Description should explain WHY the change is needed' : ''
                    };
                }
            },
            testing: {
                name: 'Testing Information',
                description: 'PR should include information about how changes were tested',
                weight: 15,
                check: (pr) => {
                    const body = (pr.body || '').toLowerCase();
                    const hasTestingInfo = body.includes('test') || 
                                         body.includes('verify') ||
                                         body.includes('manual') ||
                                         body.includes('automated');
                    
                    return {
                        passed: hasTestingInfo,
                        reason: 'Include information about how changes were tested'
                    };
                }
            },
            scope: {
                name: 'Appropriate Scope',
                description: 'PR should have reasonable scope (not too many changes)',
                weight: 10,
                check: (pr) => {
                    const filesChanged = pr.changed_files || 0;
                    const linesChanged = (pr.additions || 0) + (pr.deletions || 0);
                    
                    const reasonableFiles = filesChanged <= 20;
                    const reasonableLines = linesChanged <= 500;
                    
                    return {
                        passed: reasonableFiles && reasonableLines,
                        reason: !reasonableFiles ? `Too many files changed (${filesChanged}). Consider splitting into smaller PRs` :
                               !reasonableLines ? `Too many lines changed (${linesChanged}). Consider splitting into smaller PRs` : ''
                    };
                }
            },
            commits: {
                name: 'Quality Commit Messages',
                description: 'Commit messages should follow conventional format',
                weight: 10,
                check: (pr) => {
                    if (!pr.commits || pr.commits.length === 0) {
                        return { passed: false, reason: 'No commits found' };
                    }
                    
                    const goodCommits = pr.commits.filter(commit => {
                        const message = commit.commit.message;
                        const hasGoodLength = message.length > 10 && message.length < 100;
                        const isCapitalized = /^[A-Z]/.test(message);
                        const noTrailingPeriod = !message.trim().endsWith('.');
                        
                        return hasGoodLength && isCapitalized && noTrailingPeriod;
                    });
                    
                    const ratio = goodCommits.length / pr.commits.length;
                    
                    return {
                        passed: ratio >= 0.8,
                        reason: `${Math.round(ratio * 100)}% of commits follow good practices. Improve commit message format.`
                    };
                }
            },
            reviewers: {
                name: 'Reviewers Assigned',
                description: 'PR should have 1-2 reviewers assigned',
                weight: 5,
                check: (pr) => {
                    const reviewers = pr.requested_reviewers || [];
                    const hasReviewers = reviewers.length >= 1 && reviewers.length <= 3;
                    
                    return {
                        passed: hasReviewers,
                        reason: reviewers.length === 0 ? 'Assign 1-2 reviewers to the PR' :
                               reviewers.length > 3 ? 'Too many reviewers assigned (max 3)' : ''
                    };
                }
            },
            branch: {
                name: 'Proper Branch Name',
                description: 'Feature branch should follow naming convention',
                weight: 5,
                check: (pr) => {
                    const branchName = pr.head?.ref || '';
                    const isFeatureBranch = !['master', 'main', 'develop'].includes(branchName);
                    const hasGoodFormat = /^[a-zA-Z0-9_-]+$/.test(branchName) && branchName.length > 3;
                    
                    return {
                        passed: isFeatureBranch && hasGoodFormat,
                        reason: !isFeatureBranch ? 'Should use feature branch, not main/master' :
                               !hasGoodFormat ? 'Branch name should follow convention (e.g., TICKET-123_feature_name)' : ''
                    };
                }
            },
            conflicts: {
                name: 'No Merge Conflicts',
                description: 'PR should not have merge conflicts',
                weight: 15,
                check: (pr) => {
                    const mergeable = pr.mergeable !== false;

                    return {
                        passed: mergeable,
                        reason: 'Resolve merge conflicts before review'
                    };
                }
            },
            draft: {
                name: 'Ready for Review',
                description: 'PR should not be in draft state when requesting review',
                weight: 5,
                check: (pr) => {
                    const notDraft = !pr.draft;

                    return {
                        passed: notDraft,
                        reason: 'Remove draft status when ready for review'
                    };
                }
            },
            labels: {
                name: 'Appropriate Labels',
                description: 'PR should have relevant labels for categorization',
                weight: 5,
                check: (pr) => {
                    const labels = pr.labels || [];
                    const hasLabels = labels.length > 0;

                    return {
                        passed: hasLabels,
                        reason: 'Add relevant labels (e.g., feature, bugfix, documentation)'
                    };
                }
            }
        };
    }

    async analyzePR(pr) {
        const results = {
            checks: [],
            score: 0,
            totalChecks: Object.keys(this.checklist).length,
            totalWeight: 0,
            passedWeight: 0,
            suggestions: []
        };

        for (const [key, check] of Object.entries(this.checklist)) {
            try {
                const result = check.check(pr);
                results.checks.push({
                    key,
                    name: check.name,
                    description: check.description,
                    weight: check.weight,
                    passed: result.passed,
                    reason: result.reason
                });

                results.totalWeight += check.weight;
                if (result.passed) {
                    results.passedWeight += check.weight;
                    results.score++;
                }
            } catch (error) {
                logger.error(`Error checking ${key}:`, error);
                results.checks.push({
                    key,
                    name: check.name,
                    description: check.description,
                    weight: check.weight,
                    passed: false,
                    reason: 'Error during check'
                });
                results.totalWeight += check.weight;
            }
        }

        results.weightedScore = Math.round((results.passedWeight / results.totalWeight) * 100);
        results.suggestions = this.generateSuggestions(results.checks);

        return results;
    }

    generateSuggestions(checks) {
        const failedChecks = checks.filter(check => !check.passed);
        const suggestions = [];

        // Priority suggestions based on weight
        const highPriorityChecks = failedChecks.filter(check => check.weight >= 15);
        const mediumPriorityChecks = failedChecks.filter(check => check.weight >= 10 && check.weight < 15);
        const lowPriorityChecks = failedChecks.filter(check => check.weight < 10);

        if (highPriorityChecks.length > 0) {
            suggestions.push({
                priority: 'high',
                title: 'Critical Issues',
                items: highPriorityChecks.map(check => ({
                    name: check.name,
                    reason: check.reason,
                    action: this.getActionForCheck(check.key)
                }))
            });
        }

        if (mediumPriorityChecks.length > 0) {
            suggestions.push({
                priority: 'medium',
                title: 'Important Improvements',
                items: mediumPriorityChecks.map(check => ({
                    name: check.name,
                    reason: check.reason,
                    action: this.getActionForCheck(check.key)
                }))
            });
        }

        if (lowPriorityChecks.length > 0) {
            suggestions.push({
                priority: 'low',
                title: 'Minor Improvements',
                items: lowPriorityChecks.map(check => ({
                    name: check.name,
                    reason: check.reason,
                    action: this.getActionForCheck(check.key)
                }))
            });
        }

        return suggestions;
    }

    getActionForCheck(checkKey) {
        const actions = {
            title: 'Update the PR title to include component and brief description',
            description: 'Add a detailed description explaining the context and purpose',
            testing: 'Include testing information in the PR description',
            scope: 'Consider splitting this PR into smaller, focused changes',
            commits: 'Improve commit messages following conventional format',
            reviewers: 'Assign appropriate reviewers to the PR',
            branch: 'Use a descriptive feature branch name',
            conflicts: 'Resolve merge conflicts with the target branch',
            draft: 'Mark the PR as ready for review',
            labels: 'Add relevant labels to categorize the PR'
        };

        return actions[checkKey] || 'Review and improve this aspect of the PR';
    }

    getChecklistTemplate() {
        return Object.entries(this.checklist).map(([key, check]) => ({
            key,
            name: check.name,
            description: check.description,
            weight: check.weight
        }));
    }
}

module.exports = new ChecklistService();
