const { Octokit } = require('@octokit/rest');
const logger = require('../utils/logger');

class GitHubService {
    constructor() {
        this.octokit = new Octokit({
            auth: process.env.GITHUB_TOKEN,
            userAgent: 'PR-Review-Assistant/1.0.0'
        });
    }

    async getPR(owner, repo, number) {
        try {
            const { data: pr } = await this.octokit.pulls.get({
                owner,
                repo,
                pull_number: number
            });

            // Get PR files
            const { data: files } = await this.octokit.pulls.listFiles({
                owner,
                repo,
                pull_number: number
            });

            // Get PR commits
            const { data: commits } = await this.octokit.pulls.listCommits({
                owner,
                repo,
                pull_number: number
            });

            // Get PR reviews
            const { data: reviews } = await this.octokit.pulls.listReviews({
                owner,
                repo,
                pull_number: number
            });

            // Get PR comments
            const { data: comments } = await this.octokit.pulls.listReviewComments({
                owner,
                repo,
                pull_number: number
            });

            return {
                ...pr,
                files,
                commits,
                reviews,
                comments
            };
        } catch (error) {
            logger.error(`Error fetching PR ${owner}/${repo}#${number}:`, error);
            throw error;
        }
    }

    async getUserRepos() {
        try {
            const { data } = await this.octokit.repos.listForAuthenticatedUser({
                sort: 'updated',
                per_page: 100
            });
            return data;
        } catch (error) {
            logger.error('Error fetching user repos:', error);
            throw error;
        }
    }

    async getRepoPRs(owner, repo, state = 'open') {
        try {
            const { data } = await this.octokit.pulls.list({
                owner,
                repo,
                state,
                sort: 'updated',
                direction: 'desc',
                per_page: 50
            });
            return data;
        } catch (error) {
            logger.error(`Error fetching PRs for ${owner}/${repo}:`, error);
            throw error;
        }
    }

    async createPRComment(owner, repo, number, body) {
        try {
            const { data } = await this.octokit.issues.createComment({
                owner,
                repo,
                issue_number: number,
                body
            });
            return data;
        } catch (error) {
            logger.error(`Error creating comment on PR ${owner}/${repo}#${number}:`, error);
            throw error;
        }
    }

    async createPRReview(owner, repo, number, event, body, comments = []) {
        try {
            const { data } = await this.octokit.pulls.createReview({
                owner,
                repo,
                pull_number: number,
                event, // 'APPROVE', 'REQUEST_CHANGES', 'COMMENT'
                body,
                comments
            });
            return data;
        } catch (error) {
            logger.error(`Error creating review on PR ${owner}/${repo}#${number}:`, error);
            throw error;
        }
    }

    async updatePRDescription(owner, repo, number, body) {
        try {
            const { data } = await this.octokit.pulls.update({
                owner,
                repo,
                pull_number: number,
                body
            });
            return data;
        } catch (error) {
            logger.error(`Error updating PR description ${owner}/${repo}#${number}:`, error);
            throw error;
        }
    }

    async getFileContent(owner, repo, path, ref) {
        try {
            const { data } = await this.octokit.repos.getContent({
                owner,
                repo,
                path,
                ref
            });
            
            if (data.type === 'file') {
                return Buffer.from(data.content, 'base64').toString('utf8');
            }
            return null;
        } catch (error) {
            logger.error(`Error fetching file content ${owner}/${repo}:${path}:`, error);
            throw error;
        }
    }

    async searchCode(query, repo = null) {
        try {
            const searchQuery = repo ? `${query} repo:${repo}` : query;
            const { data } = await this.octokit.search.code({
                q: searchQuery,
                per_page: 20
            });
            return data.items;
        } catch (error) {
            logger.error('Error searching code:', error);
            throw error;
        }
    }

    async getAuthenticatedUser() {
        try {
            const { data } = await this.octokit.users.getAuthenticated();
            return data;
        } catch (error) {
            logger.error('Error fetching authenticated user:', error);
            throw error;
        }
    }

    async validateWebhookSignature(payload, signature) {
        const crypto = require('crypto');
        const secret = process.env.GITHUB_WEBHOOK_SECRET;
        
        if (!secret) {
            logger.warn('GitHub webhook secret not configured');
            return false;
        }

        const expectedSignature = 'sha256=' + crypto
            .createHmac('sha256', secret)
            .update(payload)
            .digest('hex');

        return crypto.timingSafeEqual(
            Buffer.from(signature),
            Buffer.from(expectedSignature)
        );
    }
}

module.exports = new GitHubService();
