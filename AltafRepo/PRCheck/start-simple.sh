#!/bin/bash

# Simple PR Review Tool Startup Script

echo "🚀 Starting Simple PR Review Tool"
echo "=================================="

# Check if .env file exists
if [ ! -f .env ]; then
    if [ -f .env.simple ]; then
        echo "📝 Creating .env file from .env.simple template..."
        cp .env.simple .env
        echo "⚠️  Please edit .env file with your GitHub OAuth credentials"
        echo "   1. Go to GitHub Settings → Developer settings → OAuth Apps"
        echo "   2. Create a new OAuth App"
        echo "   3. Set callback URL to: http://localhost:3000/auth/github/callback"
        echo "   4. Update GITHUB_CLIENT_ID and GITHUB_CLIENT_SECRET in .env"
        echo ""
        echo "   You can run the app without OAuth, but GitHub login won't work."
        echo ""
    else
        echo "❌ .env file not found and no template available"
        exit 1
    fi
fi

# Check if GitHub OAuth is configured
if grep -q "your_github_client_id_here" .env 2>/dev/null; then
    echo "⚠️  GitHub OAuth not configured - app will run without GitHub login"
    echo "   Edit .env file to enable GitHub OAuth"
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker compose -f docker-compose.simple.yml down 2>/dev/null || true

# Build and start the simple version
echo "🔨 Building and starting Simple PR Review Tool..."
docker compose -f docker-compose.simple.yml up --build -d

# Wait for the service to start
echo "⏳ Waiting for service to start..."
sleep 10

# Check service status
echo "📊 Service Status:"
echo "=================="

if curl -s http://localhost:3000/api/health > /dev/null; then
    echo "✅ Application: Running (http://localhost:3000)"
    
    # Check GitHub OAuth status
    oauth_status=$(curl -s http://localhost:3000/api/health | grep -o '"github_oauth":[^,}]*' | cut -d':' -f2)
    if [ "$oauth_status" = "true" ]; then
        echo "✅ GitHub OAuth: Configured"
    else
        echo "⚠️  GitHub OAuth: Not configured"
    fi
else
    echo "❌ Application: Not responding"
    echo "📝 Check logs with: docker compose -f docker-compose.simple.yml logs"
    exit 1
fi

echo ""
echo "🎉 Simple PR Review Tool is ready!"
echo "=================================="
echo "📱 Access your application at: http://localhost:3000"
echo ""
echo "📋 Features:"
echo "• ✅ Rule-based PR analysis (no AI required)"
echo "• ✅ Local data storage (no external database)"
echo "• ✅ GitHub OAuth login (if configured)"
echo "• ✅ Manual PR input and analysis"
echo "• ✅ Review history and dashboard"
echo ""
echo "📝 Useful commands:"
echo "• View logs: docker compose -f docker-compose.simple.yml logs -f"
echo "• Stop app: docker compose -f docker-compose.simple.yml down"
echo "• Restart: ./start-simple.sh"
echo ""

# Show GitHub OAuth setup if not configured
if grep -q "your_github_client_id_here" .env 2>/dev/null; then
    echo "🔧 To enable GitHub OAuth:"
    echo "1. Go to: https://github.com/settings/developers"
    echo "2. Click 'New OAuth App'"
    echo "3. Set callback URL: http://localhost:3000/auth/github/callback"
    echo "4. Update .env file with your Client ID and Secret"
    echo "5. Restart: ./start-simple.sh"
fi
