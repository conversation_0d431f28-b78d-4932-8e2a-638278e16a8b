#!/usr/bin/env node

/**
 * CLI Authentication Demo for PR Review Tool
 *
 * This script demonstrates how to authenticate with GitHub using:
 * 1. Device Flow (recommended for CLI)
 * 2. Personal Access Token
 *
 * No client secret required!
 */

const axios = require('axios');
const readline = require('readline');

const BASE_URL = 'http://localhost:3000';
const GITHUB_CLIENT_ID = 'Ov23libp9MfZXWUt3Ult'; // Your actual client ID

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Utility function to prompt user
function prompt(question) {
    return new Promise((resolve) => {
        rl.question(question, resolve);
    });
}

// Method 1: GitHub Device Flow Authentication
async function deviceFlowAuth() {
    console.log('\n🔐 GitHub Device Flow Authentication');
    console.log('=====================================');
    console.log('This method is perfect for CLI usage - no client secret needed!\n');

    try {
        // Step 1: Start device flow
        console.log('📱 Starting device flow...');
        const deviceResponse = await axios.post(`${BASE_URL}/auth/github/device`);

        if (!deviceResponse.data.success) {
            throw new Error(deviceResponse.data.error);
        }

        const { user_code, verification_uri, device_code, expires_in, interval } = deviceResponse.data;

        // Step 2: Show user instructions
        console.log('\n✅ Device flow initiated successfully!');
        console.log('\n📋 Please follow these steps:');
        console.log(`1. Copy this code: ${user_code}`);
        console.log(`2. Visit: ${verification_uri}`);
        console.log('3. Enter the code and authorize the application');
        console.log('4. Return here and press Enter to continue\n');

        await prompt('Press Enter after completing authorization...');

        // Step 3: Poll for token
        console.log('⏳ Checking for authorization...');

        const pollStart = Date.now();
        const pollTimeout = expires_in * 1000;

        while (Date.now() - pollStart < pollTimeout) {
            try {
                const pollResponse = await axios.post(`${BASE_URL}/auth/github/device/poll`, {
                    device_code
                });

                if (pollResponse.data.success) {
                    console.log('🎉 Authentication successful!');
                    console.log(`Welcome, ${pollResponse.data.user.login}!`);
                    return pollResponse.data.user;
                } else if (!pollResponse.data.pending) {
                    throw new Error(pollResponse.data.error);
                }

                // Wait before next poll
                await new Promise(resolve => setTimeout(resolve, interval * 1000));

            } catch (error) {
                if (error.response?.data?.pending) {
                    continue; // Still waiting for user authorization
                }
                throw error;
            }
        }

        throw new Error('Authentication timed out');

    } catch (error) {
        console.error('❌ Device flow authentication failed:', error.message);
        return null;
    }
}

// Method 2: Personal Access Token Authentication
// ****************************************
async function tokenAuth() {
    console.log('\n🔑 Personal Access Token Authentication');
    console.log('=======================================');
    console.log('Use your GitHub Personal Access Token for quick authentication.\n');

    try {
        // Get token from user
        const token = await prompt('Enter your GitHub Personal Access Token: ');

        if (!token.trim()) {
            throw new Error('Token is required');
        }

        console.log('🔍 Validating token...');

        // Authenticate with token
        const response = await axios.post(`${BASE_URL}/auth/github/token`, {
            token: token.trim()
        });

        if (response.data.success) {
            console.log('🎉 Authentication successful!');
            console.log(`Welcome, ${response.data.user.login}!`);
            return response.data.user;
        } else {
            throw new Error(response.data.error);
        }

    } catch (error) {
        console.error('❌ Token authentication failed:', error.message);

        if (error.response?.status === 401) {
            console.log('\n💡 Token troubleshooting:');
            console.log('1. Make sure your token has "repo" and "user:email" scopes');
            console.log('2. Check that the token is not expired');
            console.log('3. Verify the token is copied correctly');
            console.log('4. Create a new token at: https://github.com/settings/tokens');
        }

        return null;
    }
}

// Demo PR Analysis
async function demoAnalysis(user) {
    console.log('\n🔬 Demo: Analyzing a Pull Request');
    console.log('==================================');

    const repoUrl = await prompt('Enter repository URL (or press Enter for demo): ');
    const prNumber = await prompt('Enter PR number (or press Enter for demo): ');

    const demoRepo = repoUrl.trim() || 'https://github.com/saltaf07/assistila-web';
    const demoPR = prNumber.trim() || '1';

    try {
        console.log(`\n🔍 Analyzing ${demoRepo} PR #${demoPR}...`);

        const analysisResponse = await axios.post(`${BASE_URL}/api/analyze`, {
            repoUrl: demoRepo,
            prNumber: demoPR,
            analysisType: 'private',
            checklistId: 'default'
        });

        if (analysisResponse.data.success) {
            console.log('✅ Analysis completed successfully!');
            console.log(`📊 Review ID: ${analysisResponse.data.reviewId}`);
            console.log(`🌐 View results: ${BASE_URL}/results/${analysisResponse.data.reviewId}`);
        } else {
            throw new Error(analysisResponse.data.error);
        }

    } catch (error) {
        console.error('❌ Analysis failed:', error.message);
    }
}

// Main CLI function
async function main() {
    console.log('🚀 PR Review Tool - CLI Authentication Demo');
    console.log('===========================================\n');

    console.log('Choose authentication method:');
    console.log('1. Device Flow (recommended for CLI)');
    console.log('2. Personal Access Token');
    console.log('3. Exit\n');

    const choice = await prompt('Enter your choice (1-3): ');

    let user = null;

    switch (choice.trim()) {
        case '1':
            user = await deviceFlowAuth();
            break;
        case '2':
            user = await tokenAuth();
            break;
        case '3':
            console.log('👋 Goodbye!');
            rl.close();
            return;
        default:
            console.log('❌ Invalid choice');
            rl.close();
            return;
    }

    if (user) {
        const runDemo = await prompt('\nWould you like to run a demo PR analysis? (y/n): ');
        if (runDemo.toLowerCase() === 'y' || runDemo.toLowerCase() === 'yes') {
            await demoAnalysis(user);
        }

        console.log('\n🎉 Authentication demo completed!');
        console.log(`🌐 You can now use the web interface at: ${BASE_URL}`);
    }

    rl.close();
}

// Handle errors and cleanup
process.on('SIGINT', () => {
    console.log('\n\n👋 Goodbye!');
    rl.close();
    process.exit(0);
});

// Run the demo
if (require.main === module) {
    main().catch(error => {
        console.error('💥 Unexpected error:', error.message);
        rl.close();
        process.exit(1);
    });
}

module.exports = { deviceFlowAuth, tokenAuth };