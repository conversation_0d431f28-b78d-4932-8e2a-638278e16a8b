const express = require('express');
const router = express.Router();
const githubService = require('../services/githubService');
const logger = require('../utils/logger');

// Get user repositories
router.get('/repos', async (req, res) => {
    try {
        const repos = await githubService.getUserRepos();
        res.json({
            repos: repos.map(repo => ({
                id: repo.id,
                name: repo.name,
                full_name: repo.full_name,
                private: repo.private,
                description: repo.description,
                language: repo.language,
                updated_at: repo.updated_at,
                open_issues_count: repo.open_issues_count
            })),
            total: repos.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Error fetching repositories:', error);
        res.status(500).json({
            error: 'Failed to fetch repositories',
            message: error.message
        });
    }
});

// Get PRs for a specific repository
router.get('/repos/:owner/:repo/pulls', async (req, res) => {
    try {
        const { owner, repo } = req.params;
        const { state = 'open', limit = 20 } = req.query;
        
        const prs = await githubService.getRepoPRs(owner, repo, state);
        const limitedPRs = prs.slice(0, parseInt(limit));
        
        res.json({
            prs: limitedPRs.map(pr => ({
                id: pr.id,
                number: pr.number,
                title: pr.title,
                state: pr.state,
                draft: pr.draft,
                user: pr.user.login,
                created_at: pr.created_at,
                updated_at: pr.updated_at,
                mergeable: pr.mergeable,
                changed_files: pr.changed_files,
                additions: pr.additions,
                deletions: pr.deletions,
                comments: pr.comments,
                review_comments: pr.review_comments,
                commits: pr.commits
            })),
            repository: { owner, repo },
            state,
            total: limitedPRs.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error(`Error fetching PRs for ${req.params.owner}/${req.params.repo}:`, error);
        res.status(500).json({
            error: 'Failed to fetch pull requests',
            message: error.message
        });
    }
});

// Get specific PR details
router.get('/repos/:owner/:repo/pulls/:number', async (req, res) => {
    try {
        const { owner, repo, number } = req.params;
        const pr = await githubService.getPR(owner, repo, number);
        
        res.json({
            pr: {
                id: pr.id,
                number: pr.number,
                title: pr.title,
                body: pr.body,
                state: pr.state,
                draft: pr.draft,
                mergeable: pr.mergeable,
                mergeable_state: pr.mergeable_state,
                user: {
                    login: pr.user.login,
                    avatar_url: pr.user.avatar_url
                },
                head: {
                    ref: pr.head.ref,
                    sha: pr.head.sha
                },
                base: {
                    ref: pr.base.ref,
                    sha: pr.base.sha
                },
                created_at: pr.created_at,
                updated_at: pr.updated_at,
                changed_files: pr.changed_files,
                additions: pr.additions,
                deletions: pr.deletions,
                commits: pr.commits.length,
                comments: pr.comments.length,
                review_comments: pr.review_comments || 0,
                reviews: pr.reviews.length,
                requested_reviewers: pr.requested_reviewers.map(r => r.login),
                labels: pr.labels.map(l => ({ name: l.name, color: l.color }))
            },
            files: pr.files.map(file => ({
                filename: file.filename,
                status: file.status,
                additions: file.additions,
                deletions: file.deletions,
                changes: file.changes,
                patch: file.patch
            })),
            commits: pr.commits.map(commit => ({
                sha: commit.sha,
                message: commit.commit.message,
                author: commit.commit.author,
                date: commit.commit.author.date
            })),
            reviews: pr.reviews.map(review => ({
                id: review.id,
                user: review.user.login,
                state: review.state,
                body: review.body,
                submitted_at: review.submitted_at
            })),
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error(`Error fetching PR ${req.params.owner}/${req.params.repo}#${req.params.number}:`, error);
        res.status(500).json({
            error: 'Failed to fetch pull request',
            message: error.message
        });
    }
});

// Search repositories
router.get('/search/repos', async (req, res) => {
    try {
        const { q, sort = 'updated', order = 'desc' } = req.query;
        
        if (!q) {
            return res.status(400).json({ error: 'Search query is required' });
        }
        
        // This would require implementing search in githubService
        // For now, return user repos filtered by query
        const repos = await githubService.getUserRepos();
        const filteredRepos = repos.filter(repo => 
            repo.name.toLowerCase().includes(q.toLowerCase()) ||
            (repo.description && repo.description.toLowerCase().includes(q.toLowerCase()))
        );
        
        res.json({
            repos: filteredRepos.map(repo => ({
                id: repo.id,
                name: repo.name,
                full_name: repo.full_name,
                description: repo.description,
                language: repo.language,
                updated_at: repo.updated_at
            })),
            query: q,
            total: filteredRepos.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Error searching repositories:', error);
        res.status(500).json({
            error: 'Failed to search repositories',
            message: error.message
        });
    }
});

// Get authenticated user info
router.get('/user', async (req, res) => {
    try {
        const user = await githubService.getAuthenticatedUser();
        res.json({
            user: {
                id: user.id,
                login: user.login,
                name: user.name,
                email: user.email,
                avatar_url: user.avatar_url,
                company: user.company,
                location: user.location,
                public_repos: user.public_repos,
                private_repos: user.total_private_repos,
                created_at: user.created_at
            },
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Error fetching user info:', error);
        res.status(500).json({
            error: 'Failed to fetch user information',
            message: error.message
        });
    }
});

// Health check for GitHub API
router.get('/health', async (req, res) => {
    try {
        const user = await githubService.getAuthenticatedUser();
        res.json({
            status: 'healthy',
            github_api: 'connected',
            user: user.login,
            rate_limit: 'available', // Could implement rate limit checking
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('GitHub API health check failed:', error);
        res.status(500).json({
            status: 'unhealthy',
            github_api: 'disconnected',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

module.exports = router;
