const express = require('express');
const router = express.Router();
const checklistService = require('../services/checklistService');
const logger = require('../utils/logger');

// Get checklist template
router.get('/template', (req, res) => {
    try {
        const template = checklistService.getChecklistTemplate();
        res.json({
            template,
            total_checks: template.length,
            total_weight: template.reduce((sum, check) => sum + check.weight, 0),
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Error getting checklist template:', error);
        res.status(500).json({
            error: 'Failed to get checklist template',
            message: error.message
        });
    }
});

// Analyze PR against checklist
router.post('/analyze', async (req, res) => {
    try {
        const { pr } = req.body;
        
        if (!pr) {
            return res.status(400).json({ error: 'PR data is required' });
        }
        
        const results = await checklistService.analyzePR(pr);
        
        logger.info('Checklist analysis completed', {
            pr_number: pr.number,
            score: results.score,
            weighted_score: results.weightedScore,
            failed_checks: results.checks.filter(c => !c.passed).length
        });
        
        res.json({
            ...results,
            pr_info: {
                number: pr.number,
                title: pr.title,
                state: pr.state,
                draft: pr.draft
            },
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error analyzing PR with checklist:', error);
        res.status(500).json({
            error: 'Failed to analyze PR',
            message: error.message
        });
    }
});

// Get specific check details
router.get('/check/:checkKey', (req, res) => {
    try {
        const { checkKey } = req.params;
        const template = checklistService.getChecklistTemplate();
        const check = template.find(c => c.key === checkKey);
        
        if (!check) {
            return res.status(404).json({ error: 'Check not found' });
        }
        
        res.json({
            check,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error(`Error getting check ${req.params.checkKey}:`, error);
        res.status(500).json({
            error: 'Failed to get check details',
            message: error.message
        });
    }
});

// Validate specific check against PR
router.post('/check/:checkKey/validate', async (req, res) => {
    try {
        const { checkKey } = req.params;
        const { pr } = req.body;
        
        if (!pr) {
            return res.status(400).json({ error: 'PR data is required' });
        }
        
        const checklist = checklistService.checklist;
        const check = checklist[checkKey];
        
        if (!check) {
            return res.status(404).json({ error: 'Check not found' });
        }
        
        const result = check.check(pr);
        
        res.json({
            check_key: checkKey,
            check_name: check.name,
            result,
            weight: check.weight,
            pr_info: {
                number: pr.number,
                title: pr.title
            },
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error(`Error validating check ${req.params.checkKey}:`, error);
        res.status(500).json({
            error: 'Failed to validate check',
            message: error.message
        });
    }
});

// Get checklist statistics
router.get('/stats', (req, res) => {
    try {
        const template = checklistService.getChecklistTemplate();
        
        const stats = {
            total_checks: template.length,
            total_weight: template.reduce((sum, check) => sum + check.weight, 0),
            weight_distribution: {
                high: template.filter(c => c.weight >= 15).length,
                medium: template.filter(c => c.weight >= 10 && c.weight < 15).length,
                low: template.filter(c => c.weight < 10).length
            },
            checks_by_category: template.reduce((acc, check) => {
                const category = categorizeCheck(check.key);
                acc[category] = (acc[category] || 0) + 1;
                return acc;
            }, {}),
            average_weight: Math.round(template.reduce((sum, check) => sum + check.weight, 0) / template.length)
        };
        
        res.json({
            stats,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error getting checklist stats:', error);
        res.status(500).json({
            error: 'Failed to get checklist statistics',
            message: error.message
        });
    }
});

// Bulk analyze multiple PRs
router.post('/bulk-analyze', async (req, res) => {
    try {
        const { prs } = req.body;
        
        if (!prs || !Array.isArray(prs)) {
            return res.status(400).json({ error: 'PRs array is required' });
        }
        
        if (prs.length > 10) {
            return res.status(400).json({ error: 'Maximum 10 PRs per bulk analysis' });
        }
        
        const results = await Promise.allSettled(
            prs.map(async (pr) => {
                const analysis = await checklistService.analyzePR(pr);
                return {
                    pr_number: pr.number,
                    pr_title: pr.title,
                    score: analysis.score,
                    weighted_score: analysis.weightedScore,
                    total_checks: analysis.totalChecks,
                    failed_checks: analysis.checks.filter(c => !c.passed).length,
                    critical_issues: analysis.checks.filter(c => !c.passed && c.weight >= 15).length
                };
            })
        );
        
        const successful = results
            .filter(r => r.status === 'fulfilled')
            .map(r => r.value);
            
        const failed = results
            .filter(r => r.status === 'rejected')
            .map(r => ({ error: r.reason.message }));
        
        // Calculate aggregate statistics
        const aggregateStats = successful.length > 0 ? {
            average_score: Math.round(successful.reduce((sum, r) => sum + r.weighted_score, 0) / successful.length),
            total_prs: successful.length,
            passing_prs: successful.filter(r => r.weighted_score >= 80).length,
            warning_prs: successful.filter(r => r.weighted_score >= 60 && r.weighted_score < 80).length,
            failing_prs: successful.filter(r => r.weighted_score < 60).length,
            total_critical_issues: successful.reduce((sum, r) => sum + r.critical_issues, 0)
        } : null;
        
        res.json({
            results: successful,
            failed,
            aggregate_stats: aggregateStats,
            total: prs.length,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error in bulk checklist analysis:', error);
        res.status(500).json({
            error: 'Failed to perform bulk analysis',
            message: error.message
        });
    }
});

// Get improvement suggestions for failed checks
router.post('/suggestions', async (req, res) => {
    try {
        const { failedChecks } = req.body;
        
        if (!failedChecks || !Array.isArray(failedChecks)) {
            return res.status(400).json({ error: 'Failed checks array is required' });
        }
        
        const suggestions = failedChecks.map(check => ({
            check_key: check.key,
            check_name: check.name,
            reason: check.reason,
            action: checklistService.getActionForCheck(check.key),
            priority: check.weight >= 15 ? 'high' : check.weight >= 10 ? 'medium' : 'low',
            weight: check.weight
        }));
        
        // Group by priority
        const groupedSuggestions = {
            high: suggestions.filter(s => s.priority === 'high'),
            medium: suggestions.filter(s => s.priority === 'medium'),
            low: suggestions.filter(s => s.priority === 'low')
        };
        
        res.json({
            suggestions: groupedSuggestions,
            total_suggestions: suggestions.length,
            priority_counts: {
                high: groupedSuggestions.high.length,
                medium: groupedSuggestions.medium.length,
                low: groupedSuggestions.low.length
            },
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error generating suggestions:', error);
        res.status(500).json({
            error: 'Failed to generate suggestions',
            message: error.message
        });
    }
});

// Helper function to categorize checks
function categorizeCheck(checkKey) {
    const categories = {
        title: 'content',
        description: 'content',
        testing: 'quality',
        scope: 'structure',
        commits: 'git',
        reviewers: 'process',
        branch: 'git',
        conflicts: 'git',
        draft: 'process',
        labels: 'process'
    };
    
    return categories[checkKey] || 'other';
}

module.exports = router;
