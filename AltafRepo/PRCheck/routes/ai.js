const express = require('express');
const router = express.Router();
const aiService = require('../services/aiService');
const logger = require('../utils/logger');

// Health check for AI service
router.get('/health', async (req, res) => {
    try {
        const isAvailable = await aiService.isModelAvailable();
        const models = await aiService.getAvailableModels();
        
        res.json({
            status: isAvailable ? 'healthy' : 'degraded',
            ollama_connection: isAvailable ? 'connected' : 'disconnected',
            primary_model: aiService.model,
            fallback_model: aiService.fallbackModel,
            available_models: models.map(m => m.name),
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('AI service health check failed:', error);
        res.status(500).json({
            status: 'unhealthy',
            ollama_connection: 'failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Get available models
router.get('/models', async (req, res) => {
    try {
        const models = await aiService.getAvailableModels();
        res.json({
            models: models.map(model => ({
                name: model.name,
                size: model.size,
                modified_at: model.modified_at,
                digest: model.digest
            })),
            total: models.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Error fetching AI models:', error);
        res.status(500).json({
            error: 'Failed to fetch AI models',
            message: error.message
        });
    }
});

// Generate text with AI
router.post('/generate', async (req, res) => {
    try {
        const { prompt, options = {} } = req.body;
        
        if (!prompt) {
            return res.status(400).json({ error: 'Prompt is required' });
        }
        
        if (prompt.length > 10000) {
            return res.status(400).json({ error: 'Prompt too long (max 10000 characters)' });
        }
        
        const startTime = Date.now();
        const response = await aiService.generateText(prompt, options);
        const duration = Date.now() - startTime;
        
        logger.logAI('generate_text', {
            prompt_length: prompt.length,
            response_length: response.length,
            duration_ms: duration,
            model: options.model || aiService.model
        });
        
        res.json({
            response,
            metadata: {
                model: options.model || aiService.model,
                duration_ms: duration,
                prompt_length: prompt.length,
                response_length: response.length
            },
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error generating text:', error);
        res.status(500).json({
            error: 'Failed to generate text',
            message: error.message
        });
    }
});

// Analyze PR description
router.post('/analyze/description', async (req, res) => {
    try {
        const { pr } = req.body;
        
        if (!pr) {
            return res.status(400).json({ error: 'PR data is required' });
        }
        
        const analysis = await aiService.analyzePRDescription(pr);
        
        res.json({
            analysis,
            pr_info: {
                title: pr.title,
                body_length: (pr.body || '').length,
                changed_files: pr.changed_files,
                additions: pr.additions,
                deletions: pr.deletions
            },
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error analyzing PR description:', error);
        res.status(500).json({
            error: 'Failed to analyze PR description',
            message: error.message
        });
    }
});

// Analyze commit messages
router.post('/analyze/commits', async (req, res) => {
    try {
        const { commits } = req.body;
        
        if (!commits || !Array.isArray(commits)) {
            return res.status(400).json({ error: 'Commits array is required' });
        }
        
        const analysis = await aiService.analyzeCommitMessages(commits);
        
        res.json({
            analysis,
            commit_info: {
                total_commits: commits.length,
                messages: commits.map(c => c.commit.message)
            },
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error analyzing commits:', error);
        res.status(500).json({
            error: 'Failed to analyze commits',
            message: error.message
        });
    }
});

// Analyze code changes
router.post('/analyze/code', async (req, res) => {
    try {
        const { files } = req.body;
        
        if (!files || !Array.isArray(files)) {
            return res.status(400).json({ error: 'Files array is required' });
        }
        
        const analysis = await aiService.analyzeCodeChanges(files);
        
        res.json({
            analysis,
            file_info: {
                total_files: files.length,
                analyzed_files: Math.min(files.length, 5), // We only analyze first 5 files
                total_changes: files.reduce((sum, f) => sum + (f.changes || 0), 0)
            },
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error analyzing code:', error);
        res.status(500).json({
            error: 'Failed to analyze code',
            message: error.message
        });
    }
});

// Improve commit message
router.post('/improve/commit', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({ error: 'Commit message is required' });
        }
        
        const improved = await aiService.improveCommitMessage(message);
        
        res.json({
            original: message,
            improved,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error improving commit message:', error);
        res.status(500).json({
            error: 'Failed to improve commit message',
            message: error.message
        });
    }
});

// Improve PR description
router.post('/improve/description', async (req, res) => {
    try {
        const { pr } = req.body;
        
        if (!pr) {
            return res.status(400).json({ error: 'PR data is required' });
        }
        
        const improved = await aiService.improvePRDescription(pr);
        
        res.json({
            original: pr.body || '',
            improved,
            pr_info: {
                title: pr.title,
                changed_files: pr.changed_files,
                additions: pr.additions,
                deletions: pr.deletions
            },
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error improving PR description:', error);
        res.status(500).json({
            error: 'Failed to improve PR description',
            message: error.message
        });
    }
});

// Generate PR suggestions
router.post('/suggest', async (req, res) => {
    try {
        const { checklistResults, pr } = req.body;
        
        if (!checklistResults || !pr) {
            return res.status(400).json({ error: 'Checklist results and PR data are required' });
        }
        
        const suggestions = await aiService.generatePRSuggestions(checklistResults, pr);
        
        res.json({
            suggestions,
            checklist_summary: {
                score: checklistResults.score,
                total_checks: checklistResults.totalChecks,
                weighted_score: checklistResults.weightedScore,
                failed_checks: checklistResults.checks.filter(c => !c.passed).length
            },
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error generating suggestions:', error);
        res.status(500).json({
            error: 'Failed to generate suggestions',
            message: error.message
        });
    }
});

// Batch processing endpoint
router.post('/batch', async (req, res) => {
    try {
        const { operations } = req.body;
        
        if (!operations || !Array.isArray(operations)) {
            return res.status(400).json({ error: 'Operations array is required' });
        }
        
        if (operations.length > 5) {
            return res.status(400).json({ error: 'Maximum 5 operations per batch' });
        }
        
        const results = await Promise.allSettled(
            operations.map(async (op) => {
                switch (op.type) {
                    case 'analyze_description':
                        return await aiService.analyzePRDescription(op.data.pr);
                    case 'analyze_commits':
                        return await aiService.analyzeCommitMessages(op.data.commits);
                    case 'improve_commit':
                        return await aiService.improveCommitMessage(op.data.message);
                    case 'improve_description':
                        return await aiService.improvePRDescription(op.data.pr);
                    default:
                        throw new Error(`Unknown operation type: ${op.type}`);
                }
            })
        );
        
        const successful = results
            .filter(r => r.status === 'fulfilled')
            .map((r, i) => ({ operation: operations[i], result: r.value }));
            
        const failed = results
            .filter(r => r.status === 'rejected')
            .map((r, i) => ({ operation: operations[i], error: r.reason.message }));
        
        res.json({
            successful,
            failed,
            total: operations.length,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error in batch processing:', error);
        res.status(500).json({
            error: 'Failed to process batch operations',
            message: error.message
        });
    }
});

module.exports = router;
