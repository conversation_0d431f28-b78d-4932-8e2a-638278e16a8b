const express = require('express');
const router = express.Router();
const githubService = require('../services/githubService');
const aiService = require('../services/aiService');
const checklistService = require('../services/checklistService');
const logger = require('../utils/logger');

// Get PR analysis
router.get('/:owner/:repo/:number/analyze', async (req, res) => {
    try {
        const { owner, repo, number } = req.params;
        
        logger.info(`Analyzing PR ${owner}/${repo}#${number}`);
        
        // Get PR data
        const pr = await githubService.getPR(owner, repo, number);
        
        // Run checklist analysis
        const checklistResults = await checklistService.analyzePR(pr);
        
        // Get AI analysis
        const aiAnalysis = {
            description: await aiService.analyzePRDescription(pr),
            commits: await aiService.analyzeCommitMessages(pr.commits),
            code: await aiService.analyzeCodeChanges(pr.files),
            suggestions: await aiService.generatePRSuggestions(checklistResults, pr)
        };
        
        res.json({
            pr: {
                id: pr.id,
                number: pr.number,
                title: pr.title,
                body: pr.body,
                state: pr.state,
                draft: pr.draft,
                mergeable: pr.mergeable,
                changed_files: pr.changed_files,
                additions: pr.additions,
                deletions: pr.deletions,
                commits: pr.commits.length,
                reviews: pr.reviews.length,
                comments: pr.comments.length
            },
            checklist: checklistResults,
            ai: aiAnalysis,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error analyzing PR:', error);
        res.status(500).json({
            error: 'Failed to analyze PR',
            message: error.message
        });
    }
});

// Get PR suggestions for improvement
router.post('/:owner/:repo/:number/improve', async (req, res) => {
    try {
        const { owner, repo, number } = req.params;
        const { type, autoApply } = req.body; // type: 'title', 'description', 'commits'
        
        const pr = await githubService.getPR(owner, repo, number);
        let improvement;
        
        switch (type) {
            case 'title':
                improvement = await aiService.improveCommitMessage(pr.title);
                break;
            case 'description':
                improvement = await aiService.improvePRDescription(pr);
                break;
            case 'commits':
                const commitImprovements = await Promise.all(
                    pr.commits.map(async (commit) => ({
                        sha: commit.sha,
                        original: commit.commit.message,
                        improved: await aiService.improveCommitMessage(commit.commit.message)
                    }))
                );
                improvement = commitImprovements;
                break;
            default:
                return res.status(400).json({ error: 'Invalid improvement type' });
        }
        
        // Auto-apply if requested
        if (autoApply && type === 'description') {
            await githubService.updatePRDescription(owner, repo, number, improvement);
            logger.info(`Auto-applied description improvement to PR ${owner}/${repo}#${number}`);
        }
        
        res.json({
            type,
            improvement,
            autoApplied: autoApply && type === 'description',
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error improving PR:', error);
        res.status(500).json({
            error: 'Failed to improve PR',
            message: error.message
        });
    }
});

// Create AI-powered review comment
router.post('/:owner/:repo/:number/review', async (req, res) => {
    try {
        const { owner, repo, number } = req.params;
        const { event, body, autoPost } = req.body; // event: 'APPROVE', 'REQUEST_CHANGES', 'COMMENT'
        
        const pr = await githubService.getPR(owner, repo, number);
        const checklistResults = await checklistService.analyzePR(pr);
        
        let reviewBody = body;
        if (!reviewBody) {
            // Generate AI review
            const aiSuggestions = await aiService.generatePRSuggestions(checklistResults, pr);
            reviewBody = `## AI-Powered PR Review\n\n${aiSuggestions}`;
        }
        
        // Auto-post if requested
        if (autoPost) {
            const review = await githubService.createPRReview(owner, repo, number, event, reviewBody);
            logger.info(`Auto-posted review to PR ${owner}/${repo}#${number}`);
            
            res.json({
                review,
                autoPosted: true,
                timestamp: new Date().toISOString()
            });
        } else {
            res.json({
                reviewBody,
                event,
                autoPosted: false,
                timestamp: new Date().toISOString()
            });
        }
        
    } catch (error) {
        logger.error('Error creating review:', error);
        res.status(500).json({
            error: 'Failed to create review',
            message: error.message
        });
    }
});

// Get PR checklist template
router.get('/checklist/template', (req, res) => {
    try {
        const template = checklistService.getChecklistTemplate();
        res.json({
            template,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Error getting checklist template:', error);
        res.status(500).json({
            error: 'Failed to get checklist template',
            message: error.message
        });
    }
});

// Bulk analyze multiple PRs
router.post('/bulk-analyze', async (req, res) => {
    try {
        const { prs } = req.body; // Array of {owner, repo, number}
        
        if (!Array.isArray(prs) || prs.length === 0) {
            return res.status(400).json({ error: 'Invalid PRs array' });
        }
        
        if (prs.length > 10) {
            return res.status(400).json({ error: 'Maximum 10 PRs per bulk request' });
        }
        
        const results = await Promise.allSettled(
            prs.map(async ({ owner, repo, number }) => {
                const pr = await githubService.getPR(owner, repo, number);
                const checklistResults = await checklistService.analyzePR(pr);
                
                return {
                    owner,
                    repo,
                    number,
                    title: pr.title,
                    score: checklistResults.weightedScore,
                    issues: checklistResults.checks.filter(c => !c.passed).length,
                    status: pr.state
                };
            })
        );
        
        const successful = results
            .filter(r => r.status === 'fulfilled')
            .map(r => r.value);
            
        const failed = results
            .filter(r => r.status === 'rejected')
            .map(r => ({ error: r.reason.message }));
        
        res.json({
            successful,
            failed,
            total: prs.length,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        logger.error('Error in bulk analysis:', error);
        res.status(500).json({
            error: 'Failed to perform bulk analysis',
            message: error.message
        });
    }
});

module.exports = router;
