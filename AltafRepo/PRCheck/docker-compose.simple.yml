services:
  pr-review-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      - OLLAMA_URL=http://ollama:11434
      - AI_MODEL=smollm2:360m
      - SESSION_SECRET=${SESSION_SECRET:-your-secret-key-here}
    depends_on:
      - ollama
    restart: unless-stopped
    networks:
      - pr-review-network
    command: node server.js

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped
    networks:
      - pr-review-network
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  pr-review-network:
    driver: bridge

volumes:
  ollama_data:
