// PR Review page specific JavaScript

let currentPR = null;

function initializePRReview(prData) {
    currentPR = prData;
    console.log('Initialized PR review for:', prData);
}

// Refresh analysis
async function refreshAnalysis() {
    if (!currentPR) return;
    
    showToast('Refreshing analysis...', 'info');
    
    try {
        const result = await api.get(`/api/pr/${currentPR.owner}/${currentPR.repo}/${currentPR.number}/analyze`);
        showToast('Analysis refreshed successfully!', 'success');
        // Reload the page to show updated results
        window.location.reload();
    } catch (error) {
        showToast(`Failed to refresh analysis: ${error.message}`, 'danger');
    }
}

// Improve PR functions
async function improvePR(type, autoApply = false) {
    if (!currentPR) return;
    
    const actionText = autoApply ? 'Applying' : 'Generating';
    showToast(`${actionText} ${type} improvement...`, 'info');
    
    try {
        const result = await api.post(`/api/pr/${currentPR.owner}/${currentPR.repo}/${currentPR.number}/improve`, {
            type,
            autoApply
        });
        
        displayImprovementResult(type, result, autoApply);
        
        if (autoApply && result.autoApplied) {
            showToast(`${type} updated successfully!`, 'success');
            setTimeout(() => window.location.reload(), 2000);
        } else {
            showToast(`${type} improvement generated!`, 'success');
        }
    } catch (error) {
        showToast(`Failed to improve ${type}: ${error.message}`, 'danger');
    }
}

// Generate review
async function generateReview(event = 'COMMENT', autoPost = false) {
    if (!currentPR) return;
    
    const actionText = autoPost ? 'Posting' : 'Generating';
    showToast(`${actionText} AI review...`, 'info');
    
    try {
        const result = await api.post(`/api/pr/${currentPR.owner}/${currentPR.repo}/${currentPR.number}/review`, {
            event,
            autoPost
        });
        
        displayReviewResult(result, autoPost);
        
        if (autoPost && result.autoPosted) {
            showToast('Review posted successfully!', 'success');
            setTimeout(() => window.location.reload(), 2000);
        } else {
            showToast('Review generated!', 'success');
        }
    } catch (error) {
        showToast(`Failed to generate review: ${error.message}`, 'danger');
    }
}

// Auto-apply all improvements
async function autoApplyImprovements() {
    if (!currentPR) return;
    
    showToast('Applying all improvements...', 'info');
    
    try {
        // Apply description improvement
        await improvePR('description', true);
        
        // Wait a bit then generate and post review
        setTimeout(async () => {
            await generateReview('COMMENT', true);
            showToast('All improvements applied successfully!', 'success');
        }, 2000);
        
    } catch (error) {
        showToast(`Failed to apply improvements: ${error.message}`, 'danger');
    }
}

// Display improvement results
function displayImprovementResult(type, result, autoApplied) {
    const resultsSection = document.getElementById('improvementResults');
    const contentDiv = document.getElementById('improvementContent');
    
    let content = '';
    
    switch (type) {
        case 'title':
            content = `
                <div class="improvement-result">
                    <h6><i class="fas fa-heading me-2"></i>Improved Title</h6>
                    <div class="card">
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label"><strong>Current Title:</strong></label>
                                <div class="form-control-plaintext bg-light p-2 rounded">
                                    ${currentPR.title || 'No title'}
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><strong>Suggested Title:</strong></label>
                                <div class="form-control-plaintext bg-success-subtle p-2 rounded">
                                    ${result.improvement}
                                </div>
                            </div>
                            <button class="btn btn-primary" onclick="copyToClipboard('${result.improvement.replace(/'/g, "\\'")}')">
                                <i class="fas fa-copy me-2"></i>Copy Improved Title
                            </button>
                        </div>
                    </div>
                </div>
            `;
            break;
            
        case 'description':
            content = `
                <div class="improvement-result">
                    <h6><i class="fas fa-file-text me-2"></i>Improved Description</h6>
                    ${autoApplied ? 
                        '<div class="alert alert-success"><i class="fas fa-check me-2"></i>Description has been automatically updated!</div>' : 
                        ''
                    }
                    <div class="card">
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label"><strong>Improved Description:</strong></label>
                                <textarea class="form-control" rows="10" readonly>${result.improvement}</textarea>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="copyToClipboard(\`${result.improvement.replace(/`/g, '\\`')}\`)">
                                    <i class="fas fa-copy me-2"></i>Copy Description
                                </button>
                                ${!autoApplied ? 
                                    '<button class="btn btn-success" onclick="improvePR(\'description\', true)"><i class="fas fa-magic me-2"></i>Apply Now</button>' : 
                                    ''
                                }
                            </div>
                        </div>
                    </div>
                </div>
            `;
            break;
            
        case 'commits':
            content = `
                <div class="improvement-result">
                    <h6><i class="fas fa-git-alt me-2"></i>Improved Commit Messages</h6>
                    <div class="card">
                        <div class="card-body">
                            ${result.improvement.map(commit => `
                                <div class="commit-improvement mb-3">
                                    <div class="mb-2">
                                        <strong>Original:</strong>
                                        <div class="form-control-plaintext bg-light p-2 rounded small">
                                            ${commit.original}
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <strong>Improved:</strong>
                                        <div class="form-control-plaintext bg-success-subtle p-2 rounded small">
                                            ${commit.improved}
                                        </div>
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('${commit.improved.replace(/'/g, "\\'")}')">
                                        <i class="fas fa-copy me-1"></i>Copy
                                    </button>
                                    <hr>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
            break;
    }
    
    contentDiv.innerHTML = content;
    resultsSection.style.display = 'block';
    
    // Scroll to results
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}

// Display review results
function displayReviewResult(result, autoPosted) {
    const resultsSection = document.getElementById('improvementResults');
    const contentDiv = document.getElementById('improvementContent');
    
    const content = `
        <div class="improvement-result">
            <h6><i class="fas fa-robot me-2"></i>AI-Generated Review</h6>
            ${autoPosted ? 
                '<div class="alert alert-success"><i class="fas fa-check me-2"></i>Review has been automatically posted!</div>' : 
                ''
            }
            <div class="card">
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label"><strong>Review Comment:</strong></label>
                        <div class="review-content bg-light p-3 rounded">
                            <pre class="mb-0">${result.reviewBody || result.review?.body || 'No review content'}</pre>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="copyToClipboard(\`${(result.reviewBody || result.review?.body || '').replace(/`/g, '\\`')}\`)">
                            <i class="fas fa-copy me-2"></i>Copy Review
                        </button>
                        ${!autoPosted ? 
                            '<button class="btn btn-success" onclick="generateReview(\'COMMENT\', true)"><i class="fas fa-paper-plane me-2"></i>Post Review</button>' : 
                            ''
                        }
                    </div>
                </div>
            </div>
        </div>
    `;
    
    contentDiv.innerHTML = content;
    resultsSection.style.display = 'block';
    
    // Scroll to results
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}

// Keyboard shortcuts
document.addEventListener('keydown', function(event) {
    // Ctrl/Cmd + R: Refresh analysis
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        refreshAnalysis();
    }
    
    // Ctrl/Cmd + I: Improve description
    if ((event.ctrlKey || event.metaKey) && event.key === 'i') {
        event.preventDefault();
        improvePR('description', false);
    }
    
    // Ctrl/Cmd + Shift + I: Auto-apply improvements
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'I') {
        event.preventDefault();
        autoApplyImprovements();
    }
});
