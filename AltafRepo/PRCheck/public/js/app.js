// Global application JavaScript

// API helper functions
const api = {
    async get(url) {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    },

    async post(url, data) {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    }
};

// Utility functions
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="d-flex justify-content-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `;
    }
}

function showError(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }
}

function showSuccess(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                ${message}
            </div>
        `;
    }
}

// Toast notifications
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();
    
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${getToastIcon(type)} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}

function getToastIcon(type) {
    const icons = {
        success: 'check-circle',
        danger: 'exclamation-triangle',
        warning: 'exclamation-triangle',
        info: 'info-circle',
        primary: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Quick analysis functionality
function showQuickAnalysis() {
    const section = document.getElementById('quickAnalysisSection');
    if (section) {
        section.style.display = section.style.display === 'none' ? 'block' : 'none';
    }
}

async function handleQuickAnalysis(event) {
    event.preventDefault();
    
    const owner = document.getElementById('owner').value;
    const repo = document.getElementById('repo').value;
    const prNumber = document.getElementById('prNumber').value;
    
    if (!owner || !repo || !prNumber) {
        showError('quickAnalysisResult', 'Please fill in all fields');
        return;
    }
    
    showLoading('quickAnalysisResult');
    
    try {
        const result = await api.get(`/api/pr/${owner}/${repo}/${prNumber}/analyze`);
        displayQuickAnalysisResult(result);
    } catch (error) {
        showError('quickAnalysisResult', `Failed to analyze PR: ${error.message}`);
    }
}

function displayQuickAnalysisResult(result) {
    const resultElement = document.getElementById('quickAnalysisResult');
    
    const scoreClass = result.checklist.weightedScore >= 80 ? 'success' : 
                      result.checklist.weightedScore >= 60 ? 'warning' : 'danger';
    
    const failedChecks = result.checklist.checks.filter(check => !check.passed);
    
    resultElement.innerHTML = `
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>PR Analysis Results</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>Overall Score</span>
                                <span class="badge bg-${scoreClass} fs-6">${result.checklist.weightedScore}%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-${scoreClass}" style="width: ${result.checklist.weightedScore}%"></div>
                            </div>
                        </div>
                        <p class="text-muted">
                            ${result.checklist.score}/${result.checklist.totalChecks} checks passed
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>Failed Checks</h6>
                        ${failedChecks.length > 0 ? 
                            failedChecks.map(check => `
                                <div class="alert alert-warning alert-sm mb-2">
                                    <strong>${check.name}:</strong> ${check.reason}
                                </div>
                            `).join('') :
                            '<p class="text-success">All checks passed! 🎉</p>'
                        }
                    </div>
                </div>
                <div class="mt-3">
                    <a href="/pr/${result.pr.owner}/${result.pr.repo}/${result.pr.number}" class="btn btn-primary">
                        <i class="fas fa-eye me-2"></i>
                        View Detailed Analysis
                    </a>
                </div>
            </div>
        </div>
    `;
}

// Load checklist preview
async function loadChecklistPreview() {
    try {
        const result = await api.get('/api/pr/checklist/template');
        displayChecklistPreview(result.template);
    } catch (error) {
        showError('checklistPreview', 'Failed to load checklist template');
    }
}

function displayChecklistPreview(template) {
    const previewElement = document.getElementById('checklistPreview');
    
    previewElement.innerHTML = `
        <div class="row">
            ${template.map(check => `
                <div class="col-md-6 mb-3">
                    <div class="checklist-preview-item">
                        <div class="d-flex align-items-start">
                            <div class="check-icon me-3">
                                <i class="fas fa-check-circle text-muted"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">${check.name}</h6>
                                <p class="text-muted mb-1">${check.description}</p>
                                <span class="badge bg-secondary">Weight: ${check.weight}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// Format time ago
function timeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - new Date(date)) / 1000);
    
    const intervals = [
        { label: 'year', seconds: 31536000 },
        { label: 'month', seconds: 2592000 },
        { label: 'day', seconds: 86400 },
        { label: 'hour', seconds: 3600 },
        { label: 'minute', seconds: 60 }
    ];
    
    for (const interval of intervals) {
        const count = Math.floor(diffInSeconds / interval.seconds);
        if (count > 0) {
            return `${count} ${interval.label}${count !== 1 ? 's' : ''} ago`;
        }
    }
    
    return 'just now';
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('Copied to clipboard!', 'success');
    }).catch(() => {
        showToast('Failed to copy to clipboard', 'danger');
    });
}

// Initialize tooltips and popovers
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize Bootstrap popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});
