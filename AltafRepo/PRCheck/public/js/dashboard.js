// Dashboard specific JavaScript

let currentRepo = null;
let repositories = [];
let currentPRFilter = 'open';

async function initializeDashboard() {
    await checkSystemHealth();
    await loadRepositories();
    setupEventListeners();
}

function setupEventListeners() {
    // Single PR form
    document.getElementById('singlePRForm').addEventListener('submit', handleSinglePRAnalysis);
    
    // Repository selection
    document.getElementById('repoSelect').addEventListener('change', function() {
        const selectedRepo = this.value;
        if (selectedRepo) {
            const [owner, repo] = selectedRepo.split('/');
            currentRepo = { owner, repo };
            loadRepoPRs();
        }
    });
}

async function checkSystemHealth() {
    try {
        // Check GitHub API
        const githubHealth = await api.get('/api/github/health');
        updateStatusCard('githubStatus', 'Connected', 'success');
    } catch (error) {
        updateStatusCard('githubStatus', 'Error', 'danger');
    }
    
    try {
        // Check AI Service
        const aiHealth = await api.get('/api/ai/health');
        updateStatusCard('aiStatus', aiHealth.status === 'healthy' ? 'Ready' : 'Degraded', 
                        aiHealth.status === 'healthy' ? 'success' : 'warning');
    } catch (error) {
        updateStatusCard('aiStatus', 'Offline', 'danger');
    }
}

function updateStatusCard(elementId, text, status) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = text;
        element.className = `card-title mb-0 text-${status}`;
    }
}

async function loadRepositories() {
    try {
        const result = await api.get('/api/github/repos');
        repositories = result.repos;
        
        displayRepositories(repositories);
        populateRepoSelect(repositories);
        
    } catch (error) {
        showError('repositoriesList', `Failed to load repositories: ${error.message}`);
    }
}

function displayRepositories(repos) {
    const container = document.getElementById('repositoriesList');
    
    if (repos.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">No repositories found</p>';
        return;
    }
    
    container.innerHTML = repos.slice(0, 10).map(repo => `
        <div class="repo-item mb-2 p-2 border rounded cursor-pointer" onclick="selectRepository('${repo.full_name}')">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">
                        ${repo.private ? '<i class="fas fa-lock text-warning me-1"></i>' : '<i class="fas fa-book text-success me-1"></i>'}
                        ${repo.name}
                    </h6>
                    <small class="text-muted">${repo.description || 'No description'}</small>
                </div>
                <div class="text-end">
                    <small class="text-muted">${repo.language || 'Unknown'}</small>
                    <br>
                    <small class="text-muted">${timeAgo(repo.updated_at)}</small>
                </div>
            </div>
        </div>
    `).join('');
}

function populateRepoSelect(repos) {
    const select = document.getElementById('repoSelect');
    select.innerHTML = '<option value="">Select a repository...</option>' +
        repos.map(repo => `<option value="${repo.full_name}">${repo.full_name}</option>`).join('');
}

function selectRepository(fullName) {
    const [owner, repo] = fullName.split('/');
    currentRepo = { owner, repo };
    
    // Update select
    document.getElementById('repoSelect').value = fullName;
    
    // Load PRs
    loadRepoPRs();
    
    // Visual feedback
    document.querySelectorAll('.repo-item').forEach(item => {
        item.classList.remove('border-primary', 'bg-light');
    });
    event.currentTarget.classList.add('border-primary', 'bg-light');
}

async function loadRepoPRs() {
    if (!currentRepo) return;
    
    const container = document.getElementById('pullRequestsList');
    showLoading('pullRequestsList');
    
    try {
        const result = await api.get(`/api/github/repos/${currentRepo.owner}/${currentRepo.repo}/pulls?state=${currentPRFilter}`);
        displayPullRequests(result.prs);
    } catch (error) {
        showError('pullRequestsList', `Failed to load PRs: ${error.message}`);
    }
}

function displayPullRequests(prs) {
    const container = document.getElementById('pullRequestsList');
    
    if (prs.length === 0) {
        container.innerHTML = `<p class="text-muted text-center">No ${currentPRFilter} pull requests found</p>`;
        return;
    }
    
    container.innerHTML = prs.map(pr => `
        <div class="pr-item mb-2 p-2 border rounded">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="mb-1">
                        <span class="badge bg-${pr.state === 'open' ? 'success' : 'secondary'} me-2">#${pr.number}</span>
                        <a href="/pr/${currentRepo.owner}/${currentRepo.repo}/${pr.number}" class="text-decoration-none">
                            ${pr.title}
                        </a>
                    </h6>
                    <div class="d-flex gap-3 text-muted small">
                        <span><i class="fas fa-user me-1"></i>${pr.user}</span>
                        <span><i class="fas fa-clock me-1"></i>${timeAgo(pr.created_at)}</span>
                        <span><i class="fas fa-file-code me-1"></i>${pr.changed_files} files</span>
                        <span class="text-success"><i class="fas fa-plus me-1"></i>${pr.additions}</span>
                        <span class="text-danger"><i class="fas fa-minus me-1"></i>${pr.deletions}</span>
                    </div>
                </div>
                <div class="text-end">
                    <button class="btn btn-sm btn-outline-primary" onclick="quickAnalyzePR('${currentRepo.owner}', '${currentRepo.repo}', ${pr.number})">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

function filterPRs(state) {
    currentPRFilter = state;
    
    // Update button states
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Reload PRs
    loadRepoPRs();
}

async function handleSinglePRAnalysis(event) {
    event.preventDefault();
    
    const repoInput = document.getElementById('repoInput').value;
    const prInput = document.getElementById('prInput').value;
    
    if (!repoInput || !prInput) {
        showToast('Please fill in all fields', 'warning');
        return;
    }
    
    const [owner, repo] = repoInput.split('/');
    if (!owner || !repo) {
        showToast('Repository format should be owner/repo', 'warning');
        return;
    }
    
    // Redirect to PR analysis page
    window.location.href = `/pr/${owner}/${repo}/${prInput}`;
}

async function quickAnalyzePR(owner, repo, number) {
    showToast('Analyzing PR...', 'info');
    
    try {
        const result = await api.get(`/api/pr/${owner}/${repo}/${number}/analyze`);
        
        const scoreClass = result.checklist.weightedScore >= 80 ? 'success' : 
                          result.checklist.weightedScore >= 60 ? 'warning' : 'danger';
        
        showToast(`PR #${number} scored ${result.checklist.weightedScore}% (${result.checklist.score}/${result.checklist.totalChecks} checks passed)`, scoreClass);
        
    } catch (error) {
        showToast(`Failed to analyze PR: ${error.message}`, 'danger');
    }
}

function refreshRepositories() {
    loadRepositories();
    showToast('Repositories refreshed', 'success');
}

// Bulk Analysis
function showBulkAnalysis() {
    const modal = new bootstrap.Modal(document.getElementById('bulkAnalysisModal'));
    modal.show();
}

async function runBulkAnalysis() {
    const input = document.getElementById('bulkPRInput').value.trim();
    if (!input) {
        showToast('Please enter PR URLs or identifiers', 'warning');
        return;
    }
    
    const lines = input.split('\n').filter(line => line.trim());
    const prs = [];
    
    // Parse input lines
    for (const line of lines) {
        const trimmed = line.trim();
        let match;
        
        // GitHub URL format
        if (trimmed.includes('github.com')) {
            match = trimmed.match(/github\.com\/([^\/]+)\/([^\/]+)\/pull\/(\d+)/);
            if (match) {
                prs.push({ owner: match[1], repo: match[2], number: parseInt(match[3]) });
            }
        }
        // owner/repo#number format
        else if (trimmed.includes('#')) {
            match = trimmed.match(/([^\/]+)\/([^#]+)#(\d+)/);
            if (match) {
                prs.push({ owner: match[1], repo: match[2], number: parseInt(match[3]) });
            }
        }
    }
    
    if (prs.length === 0) {
        showToast('No valid PR identifiers found', 'warning');
        return;
    }
    
    if (prs.length > 10) {
        showToast('Maximum 10 PRs per bulk analysis', 'warning');
        return;
    }
    
    const resultsContainer = document.getElementById('bulkAnalysisResults');
    resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border"></div><p>Analyzing PRs...</p></div>';
    
    try {
        const result = await api.post('/api/pr/bulk-analyze', { prs });
        displayBulkResults(result);
    } catch (error) {
        resultsContainer.innerHTML = `<div class="alert alert-danger">Failed to analyze PRs: ${error.message}</div>`;
    }
}

function displayBulkResults(result) {
    const container = document.getElementById('bulkAnalysisResults');
    
    container.innerHTML = `
        <div class="mb-3">
            <h6>Analysis Results (${result.successful.length}/${result.total} successful)</h6>
        </div>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>PR</th>
                        <th>Title</th>
                        <th>Score</th>
                        <th>Issues</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    ${result.successful.map(pr => `
                        <tr>
                            <td>#${pr.number}</td>
                            <td class="text-truncate" style="max-width: 200px;">${pr.title}</td>
                            <td>
                                <span class="badge bg-${pr.score >= 80 ? 'success' : pr.score >= 60 ? 'warning' : 'danger'}">
                                    ${pr.score}%
                                </span>
                            </td>
                            <td>${pr.issues}</td>
                            <td>
                                <a href="/pr/${pr.owner}/${pr.repo}/${pr.number}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        ${result.failed.length > 0 ? `
            <div class="alert alert-warning">
                <strong>Failed analyses:</strong> ${result.failed.length} PRs could not be analyzed.
            </div>
        ` : ''}
    `;
}

// Health Check
function showHealthCheck() {
    const modal = new bootstrap.Modal(document.getElementById('healthCheckModal'));
    modal.show();
    runHealthCheck();
}

async function runHealthCheck() {
    const container = document.getElementById('healthCheckResults');
    container.innerHTML = '<div class="text-center"><div class="spinner-border"></div><p>Running health checks...</p></div>';
    
    const checks = [
        { name: 'Application', endpoint: '/health' },
        { name: 'GitHub API', endpoint: '/api/github/health' },
        { name: 'AI Service', endpoint: '/api/ai/health' },
        { name: 'Webhooks', endpoint: '/webhooks/health' }
    ];
    
    const results = await Promise.allSettled(
        checks.map(async check => {
            try {
                const result = await api.get(check.endpoint);
                return { ...check, status: 'healthy', data: result };
            } catch (error) {
                return { ...check, status: 'error', error: error.message };
            }
        })
    );
    
    container.innerHTML = `
        <div class="list-group">
            ${results.map((result, index) => {
                const check = result.status === 'fulfilled' ? result.value : { ...checks[index], status: 'error', error: result.reason.message };
                return `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${check.name}</h6>
                            <small class="text-muted">${check.endpoint}</small>
                        </div>
                        <span class="badge bg-${check.status === 'healthy' ? 'success' : 'danger'}">
                            ${check.status === 'healthy' ? 'Healthy' : 'Error'}
                        </span>
                    </div>
                `;
            }).join('')}
        </div>
    `;
}
