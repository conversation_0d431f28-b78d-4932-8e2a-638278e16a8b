/* Custom styles for PR Review Assistant */

:root {
    --primary-color: #0d6efd;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --dark-color: #212529;
    --light-color: #f8f9fa;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Global styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Hero section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-top: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #fff;
}

.feature-card h5 {
    color: #fff;
    margin-bottom: 0.5rem;
}

.feature-card p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 0.9rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* PR Stats */
.pr-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0;
}

.stat-item i {
    width: 1.2rem;
    text-align: center;
}

/* Checklist items */
.checklist-item {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.checklist-item.passed {
    background-color: #d1e7dd;
    border-left: 4px solid var(--success-color);
}

.checklist-item.failed {
    background-color: #f8d7da;
    border-left: 4px solid var(--danger-color);
}

.checklist-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
}

.check-icon {
    font-size: 1.2rem;
}

.check-weight {
    min-width: 80px;
}

/* Checklist preview */
.checklist-preview-item {
    padding: 1rem;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--info-color);
}

/* Suggestions */
.suggestion-card {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.suggestion-card.priority-high {
    background-color: #f8d7da;
    border-left-color: var(--danger-color);
}

.suggestion-card.priority-medium {
    background-color: #fff3cd;
    border-left-color: var(--warning-color);
}

.suggestion-card.priority-low {
    background-color: #d1ecf1;
    border-left-color: var(--info-color);
}

.suggestion-title {
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.suggestion-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.suggestion-list li {
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.suggestion-list li:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.action-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Score badge */
.score-badge .badge {
    font-size: 1rem;
    padding: 0.5rem 1rem;
}

/* Progress bars */
.progress {
    height: 0.75rem;
    border-radius: var(--border-radius);
}

/* Improvement results */
.improvement-result {
    margin-bottom: 2rem;
}

.commit-improvement {
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    background-color: #fff;
}

.review-content {
    max-height: 400px;
    overflow-y: auto;
}

.review-content pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: inherit;
    font-size: 0.9rem;
}

/* Alerts */
.alert-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Loading states */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .hero-section {
        min-height: 50vh;
        text-align: center;
    }
    
    .pr-stats {
        margin-top: 1rem;
    }
    
    .stat-item {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #343a40;
        --dark-color: #f8f9fa;
    }
    
    body {
        background-color: #121212;
        color: #e0e0e0;
    }
    
    .card {
        background-color: #1e1e1e;
        color: #e0e0e0;
    }
    
    .card-header {
        background-color: #2d2d2d;
        border-bottom-color: #404040;
    }
    
    .checklist-preview-item {
        background-color: #2d2d2d;
    }
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.border-start-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-start-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-start-warning {
    border-left: 4px solid var(--warning-color) !important;
}

.border-start-danger {
    border-left: 4px solid var(--danger-color) !important;
}

/* Enhanced PR Analysis Styles */
.pr-score {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.pr-score-container {
    padding: 1rem;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.check-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.check-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.check-passed {
    background-color: #d1e7dd;
    border-color: #badbcc;
}

.check-failed {
    background-color: #f8d7da;
    border-color: #f5c2c7;
}

.check-category {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin-bottom: 1.5rem;
}

.checks-container {
    max-height: 500px;
    overflow-y: auto;
}

.suggestions-container {
    max-height: 400px;
    overflow-y: auto;
}

/* Tab styling enhancements */
.nav-tabs .nav-link {
    border-radius: 0.375rem 0.375rem 0 0;
    border: 1px solid transparent;
    color: #495057;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-color: #007bff;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    isolation: isolate;
    transform: translateY(-1px);
}

/* Form styling enhancements */
.form-select:focus,
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Enhanced responsive design */
@media (max-width: 768px) {
    .pr-score {
        font-size: 2rem;
    }

    .check-item {
        padding: 0.5rem;
    }

    .check-category {
        padding-left: 0.5rem;
    }

    .suggestions-container,
    .checks-container {
        max-height: 300px;
    }
}
