# Simple Dockerfile for PR Review Tool
FROM node:18-alpine

# Install basic dependencies
RUN apk add --no-cache curl

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application files
COPY simple-app.js ./
COPY views/ ./views/
COPY public/ ./public/

# Create data directory
RUN mkdir -p data

# Create a simple .env file if it doesn't exist
RUN echo "PORT=3000" > .env.default

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Start the application
CMD ["node", "simple-app.js"]
