#!/bin/bash

echo "🧪 Testing Enhanced PR Review Tool Features"
echo "=========================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to test API endpoint
test_api() {
    local endpoint=$1
    local expected_status=$2
    local description=$3
    
    echo -n "Testing $description... "
    
    response=$(curl -s -w "%{http_code}" http://localhost:3000$endpoint)
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL (Expected: $expected_status, Got: $status_code)${NC}"
        ((TESTS_FAILED++))
    fi
}

# Function to test API with JSON response
test_api_json() {
    local endpoint=$1
    local expected_field=$2
    local description=$3
    
    echo -n "Testing $description... "
    
    response=$(curl -s http://localhost:3000$endpoint)
    
    if echo "$response" | jq -e ".$expected_field" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL (Field '$expected_field' not found)${NC}"
        echo "Response: $response"
        ((TESTS_FAILED++))
    fi
}

echo -e "${BLUE}1. Testing Basic Application Health${NC}"
test_api "/" "200" "Dashboard accessibility"
test_api "/analyze" "200" "Analyze page accessibility"
test_api "/checklists" "200" "Checklists page accessibility"

echo -e "\n${BLUE}2. Testing Repository API (Authentication Required)${NC}"
test_api "/api/repositories" "401" "Repository API without auth"

echo -e "\n${BLUE}3. Testing Checklist Management API${NC}"
test_api_json "/api/checklists" "success" "Checklists API response structure"
test_api_json "/api/checklists" "checklists" "Checklists data availability"

echo -e "\n${BLUE}4. Testing Individual Checklist API${NC}"
test_api_json "/api/checklists/default" "success" "Individual checklist API"
test_api "/api/checklists/nonexistent" "404" "Non-existent checklist handling"

echo -e "\n${BLUE}5. Testing Enhanced Checklist Structure${NC}"
echo -n "Testing unified rule structure... "
response=$(curl -s http://localhost:3000/api/checklists/default)
if echo "$response" | jq -e '.checklist.rules' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ PASS (Unified structure detected)${NC}"
    ((TESTS_PASSED++))
elif echo "$response" | jq -e '.checklist.checks' > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  LEGACY (Category structure detected)${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${RED}❌ FAIL (No valid structure found)${NC}"
    ((TESTS_FAILED++))
fi

echo -e "\n${BLUE}6. Testing Analysis Engine Compatibility${NC}"
echo -n "Testing PR analysis with enhanced structure... "
analysis_response=$(curl -s -X POST http://localhost:3000/api/analyze \
  -H "Content-Type: application/json" \
  -d '{"repoUrl":"https://github.com/saltaf07/assistila-web","prNumber":"1","analysisType":"public","checklistId":"default"}')

if echo "$analysis_response" | jq -e '.success' > /dev/null 2>&1; then
    review_id=$(echo "$analysis_response" | jq -r '.reviewId')
    echo -e "${GREEN}✅ PASS (Review ID: $review_id)${NC}"
    ((TESTS_PASSED++))
    
    # Test results page
    echo -n "Testing results page accessibility... "
    if curl -s http://localhost:3000/results/$review_id | grep -q "PR Analysis Results"; then
        echo -e "${GREEN}✅ PASS${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}"
        ((TESTS_FAILED++))
    fi
else
    echo -e "${RED}❌ FAIL${NC}"
    echo "Response: $analysis_response"
    ((TESTS_FAILED++))
fi

echo -e "\n${BLUE}7. Testing Navigation Enhancements${NC}"
echo -n "Testing back button functionality... "
if curl -s http://localhost:3000/results/test | grep -q "javascript:history.back()"; then
    echo -e "${GREEN}✅ PASS${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${RED}❌ FAIL${NC}"
    ((TESTS_FAILED++))
fi

echo -e "\n${BLUE}8. Testing Checklist CRUD Operations${NC}"
echo -n "Testing checklist creation API... "
create_response=$(curl -s -X POST http://localhost:3000/api/checklists \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Checklist",
    "description": "Test description",
    "rules": [
      {
        "id": "test_rule",
        "name": "Test Rule",
        "description": "Test rule description",
        "type": "title_check",
        "severity": "medium",
        "rule": "length <= 50",
        "passMessage": "Test pass",
        "failMessage": "Test fail"
      }
    ]
  }')

if echo "$create_response" | jq -e '.success' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ PASS${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${RED}❌ FAIL${NC}"
    echo "Response: $create_response"
    ((TESTS_FAILED++))
fi

echo -e "\n${YELLOW}📊 Test Summary${NC}"
echo "==============="
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! Enhanced features are working correctly.${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  Some tests failed. Please check the implementation.${NC}"
    exit 1
fi
