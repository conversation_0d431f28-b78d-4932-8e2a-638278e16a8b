<% 
const body = `
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
            <a href="/review/new" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>
                New Review
            </a>
        </div>

        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-clipboard-list fa-2x text-primary mb-2"></i>
                        <h5 class="card-title"><%= reviews.length %></h5>
                        <p class="card-text text-muted">Total Reviews</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h5 class="card-title">
                            <%= reviews.filter(r => r.analysis.percentage >= 80).length %>
                        </h5>
                        <p class="card-text text-muted">High Quality PRs</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <h5 class="card-title">
                            <%= reviews.filter(r => r.analysis.percentage < 60).length %>
                        </h5>
                        <p class="card-text text-muted">Needs Improvement</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                        <h5 class="card-title">
                            <%= reviews.length > 0 ? Math.round(reviews.reduce((sum, r) => sum + r.analysis.percentage, 0) / reviews.length) : 0 %>%
                        </h5>
                        <p class="card-text text-muted">Average Score</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Reviews</h5>
            </div>
            <div class="card-body">
                <% if (reviews.length === 0) { %>
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No reviews yet</h5>
                        <p class="text-muted">Start by creating your first PR review</p>
                        <a href="/review/new" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Create First Review
                        </a>
                    </div>
                <% } else { %>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>PR Title</th>
                                    <th>Repository</th>
                                    <th>Score</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% reviews.slice(0, 10).forEach(review => { %>
                                    <tr>
                                        <td>
                                            <a href="/review/<%= review.id %>" class="text-decoration-none">
                                                <%= review.prData.title %>
                                            </a>
                                            <br>
                                            <small class="text-muted">PR #<%= review.prNumber %></small>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <%= review.repoUrl.replace('https://github.com/', '') %>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<%= review.analysis.percentage >= 80 ? 'success' : review.analysis.percentage >= 60 ? 'warning' : 'danger' %>">
                                                <%= review.analysis.percentage %>%
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <%= new Date(review.createdAt).toLocaleDateString() %>
                                            </small>
                                        </td>
                                        <td>
                                            <a href="/review/<%= review.id %>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>
                                                View
                                            </a>
                                            <a href="<%= review.prData.html_url %>" target="_blank" class="btn btn-sm btn-outline-secondary">
                                                <i class="fab fa-github me-1"></i>
                                                GitHub
                                            </a>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>
                    </div>
                    
                    <% if (reviews.length > 10) { %>
                        <div class="text-center mt-3">
                            <p class="text-muted">Showing 10 of <%= reviews.length %> reviews</p>
                        </div>
                    <% } %>
                <% } %>
            </div>
        </div>
    </div>
</div>
`;
%>

<%- include('layout', { title, user, githubConfigured, body }) %>
