<% 
const body = `
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-clipboard-list me-2"></i>PR Review Details</h2>
            <div>
                <a href="<%= review.prData.html_url %>" target="_blank" class="btn btn-outline-primary">
                    <i class="fab fa-github me-2"></i>
                    View on GitHub
                </a>
                <a href="/dashboard" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-code-branch me-2"></i>
                            <%= review.prData.title %>
                        </h5>
                        <small class="text-muted">
                            PR #<%= review.prNumber %> • 
                            <%= review.repoUrl.replace('https://github.com/', '') %> • 
                            <%= new Date(review.createdAt).toLocaleDateString() %>
                        </small>
                    </div>
                    <div class="card-body">
                        <% if (review.prData.body) { %>
                            <h6>Description</h6>
                            <div class="bg-light p-3 rounded mb-3">
                                <%= review.prData.body.substring(0, 500) %><%= review.prData.body.length > 500 ? '...' : '' %>
                            </div>
                        <% } %>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>PR Information</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Author:</strong> <%= review.prData.user %></li>
                                    <li><strong>State:</strong> 
                                        <span class="badge bg-<%= review.prData.state === 'open' ? 'success' : 'secondary' %>">
                                            <%= review.prData.state %>
                                        </span>
                                    </li>
                                    <li><strong>Created:</strong> <%= new Date(review.prData.created_at).toLocaleDateString() %></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Analysis Score</h6>
                                <div class="text-center">
                                    <div class="pr-score text-<%= review.analysis.percentage >= 80 ? 'success' : review.analysis.percentage >= 60 ? 'warning' : 'danger' %>">
                                        <%= review.analysis.percentage %>%
                                    </div>
                                    <p class="text-muted">
                                        <%= review.analysis.score %>/<%= review.analysis.totalChecks %> checks passed
                                    </p>
                                    <div class="progress">
                                        <div class="progress-bar bg-<%= review.analysis.percentage >= 80 ? 'success' : review.analysis.percentage >= 60 ? 'warning' : 'danger' %>" 
                                             style="width: <%= review.analysis.percentage %>%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list-check me-2"></i>
                            Detailed Analysis
                        </h5>
                    </div>
                    <div class="card-body">
                        <% review.analysis.checks.forEach(check => { %>
                            <div class="check-item <%= check.passed ? 'check-passed' : 'check-failed' %>">
                                <div class="d-flex align-items-center">
                                    <i class="<%= check.passed ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger' %> me-2"></i>
                                    <strong><%= check.name %></strong>
                                </div>
                                <% if (check.reason) { %>
                                    <small class="text-muted ms-4"><%= check.reason %></small>
                                <% } %>
                            </div>
                        <% }); %>
                    </div>
                </div>

                <% if (review.analysis.suggestions.length > 0) { %>
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>
                                Suggestions for Improvement
                            </h5>
                        </div>
                        <div class="card-body">
                            <% review.analysis.suggestions.forEach(suggestion => { %>
                                <div class="alert alert-warning">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <%= suggestion.title %>
                                    </h6>
                                    <p class="mb-0"><%= suggestion.reason %></p>
                                </div>
                            <% }); %>
                        </div>
                    </div>
                <% } %>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            Quick Stats
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-success"><%= review.analysis.score %></h4>
                                    <small class="text-muted">Passed</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-danger"><%= review.analysis.totalChecks - review.analysis.score %></h4>
                                <small class="text-muted">Failed</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            Actions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<%= review.prData.html_url %>" target="_blank" class="btn btn-primary">
                                <i class="fab fa-github me-2"></i>
                                Open in GitHub
                            </a>
                            <button class="btn btn-outline-secondary" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>
                                Print Report
                            </button>
                            <button class="btn btn-outline-info" onclick="copyReportToClipboard()">
                                <i class="fas fa-copy me-2"></i>
                                Copy Summary
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Review Info
                        </h6>
                    </div>
                    <div class="card-body">
                        <small class="text-muted">
                            <strong>Review ID:</strong> <%= review.id %><br>
                            <strong>Analyzed:</strong> <%= new Date(review.createdAt).toLocaleString() %><br>
                            <strong>Repository:</strong> <%= review.repoUrl.replace('https://github.com/', '') %><br>
                            <strong>PR Number:</strong> #<%= review.prNumber %>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyReportToClipboard() {
    const summary = \`PR Review Summary
Repository: <%= review.repoUrl.replace('https://github.com/', '') %>
PR #<%= review.prNumber %>: <%= review.prData.title %>
Score: <%= review.analysis.percentage %>% (<%= review.analysis.score %>/<%= review.analysis.totalChecks %> checks passed)

Checks:
<% review.analysis.checks.forEach(check => { %>- <%= check.passed ? '✅' : '❌' %> <%= check.name %><% if (check.reason) { %> - <%= check.reason %><% } %>
<% }); %>
<% if (review.analysis.suggestions.length > 0) { %>
Suggestions:
<% review.analysis.suggestions.forEach(suggestion => { %>- <%= suggestion.title %>: <%= suggestion.reason %>
<% }); %><% } %>\`;

    navigator.clipboard.writeText(summary).then(() => {
        showNotification('Report summary copied to clipboard!', 'success');
    }).catch(() => {
        showNotification('Failed to copy to clipboard', 'danger');
    });
}
</script>
`;
%>

<%- include('layout', { title, user, githubConfigured, body }) %>
