<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fab fa-github me-2"></i>
                PR Review Assistant
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/dashboard">Dashboard</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>
                            Tools
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showBulkAnalysis()">Bulk Analysis</a></li>
                            <li><a class="dropdown-item" href="#" onclick="showHealthCheck()">Health Check</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/api/pr/checklist/template" target="_blank">API Docs</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- Status Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fab fa-github fa-2x text-primary me-2"></i>
                            <div>
                                <h5 class="card-title mb-0" id="githubStatus">Checking...</h5>
                                <small class="text-muted">GitHub API</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-robot fa-2x text-success me-2"></i>
                            <div>
                                <h5 class="card-title mb-0" id="aiStatus">Checking...</h5>
                                <small class="text-muted">AI Service</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-database fa-2x text-info me-2"></i>
                            <div>
                                <h5 class="card-title mb-0" id="dbStatus">Healthy</h5>
                                <small class="text-muted">Database</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="fas fa-chart-line fa-2x text-warning me-2"></i>
                            <div>
                                <h5 class="card-title mb-0" id="totalAnalyses">0</h5>
                                <small class="text-muted">Total Analyses</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h6>Analyze Single PR</h6>
                                <form id="singlePRForm" class="d-flex gap-2">
                                    <input type="text" class="form-control" placeholder="owner/repo" id="repoInput" required>
                                    <input type="number" class="form-control" placeholder="PR #" id="prInput" required>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-6 mb-3">
                                <h6>Repository PRs</h6>
                                <div class="d-flex gap-2">
                                    <select class="form-select" id="repoSelect">
                                        <option value="">Loading repositories...</option>
                                    </select>
                                    <button class="btn btn-outline-primary" onclick="loadRepoPRs()">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Repositories and PRs -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-code-branch me-2"></i>
                            Your Repositories
                        </h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshRepositories()">
                            <i class="fas fa-refresh"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="repositoriesList">
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-code-pull-request me-2"></i>
                            Recent Pull Requests
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary active" onclick="filterPRs('open')">Open</button>
                            <button class="btn btn-outline-secondary" onclick="filterPRs('closed')">Closed</button>
                            <button class="btn btn-outline-secondary" onclick="filterPRs('all')">All</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="pullRequestsList">
                            <div class="text-center text-muted">
                                <i class="fas fa-arrow-left me-2"></i>
                                Select a repository to view PRs
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Analysis Modal -->
        <div class="modal fade" id="bulkAnalysisModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-tasks me-2"></i>
                            Bulk PR Analysis
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Enter PR URLs or owner/repo#number format (one per line):</label>
                            <textarea class="form-control" rows="6" id="bulkPRInput" 
                                placeholder="Examples:&#10;owner/repo#123&#10;owner/repo#456&#10;https://github.com/owner/repo/pull/789"></textarea>
                        </div>
                        <div id="bulkAnalysisResults"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="runBulkAnalysis()">
                            <i class="fas fa-play me-2"></i>
                            Run Analysis
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Health Check Modal -->
        <div class="modal fade" id="healthCheckModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-heartbeat me-2"></i>
                            System Health Check
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="healthCheckResults">
                            <div class="d-flex justify-content-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Checking...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="runHealthCheck()">
                            <i class="fas fa-refresh me-2"></i>
                            Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="/js/app.js"></script>
    <script src="/js/dashboard.js"></script>
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });
    </script>
</body>
</html>
