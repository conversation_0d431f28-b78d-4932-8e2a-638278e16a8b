<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
            <div>
                <a href="/review/new" class="btn btn-success me-2">
                    <i class="fas fa-plus me-2"></i>
                    New Review
                </a>
                <a href="/review/results" class="btn btn-primary">
                    <i class="fas fa-chart-line me-2"></i>
                    View Results
                </a>
            </div>
        </div>

        <!-- Quick Navigation Cards -->
        <div class="row g-3 mb-4">
            <div class="col-md-4">
                <div class="card h-100 border-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-globe fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Analyze Public Repository</h5>
                        <p class="card-text text-muted">Analyze any public GitHub repository without login</p>
                        <a href="/review/new" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            Start Analysis
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                        <h5 class="card-title">View Analysis Results</h5>
                        <p class="card-text text-muted">View detailed results from your latest PR analysis</p>
                        <a href="/review/results" class="btn btn-success">
                            <i class="fas fa-eye me-2"></i>
                            View Results
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-info">
                    <div class="card-body text-center">
                        <i class="fas fa-history fa-3x text-info mb-3"></i>
                        <h5 class="card-title">Review History</h5>
                        <p class="card-text text-muted">Browse your complete PR review history</p>
                        <a href="#reviewHistory" class="btn btn-info" onclick="scrollToHistory()">
                            <i class="fas fa-list me-2"></i>
                            View History
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-clipboard-list fa-2x text-primary mb-2"></i>
                        <h5 class="card-title"><%= reviews.length %></h5>
                        <p class="card-text text-muted">Total Reviews</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h5 class="card-title">
                            <%= reviews.filter(r => r.analysis.percentage >= 80).length %>
                        </h5>
                        <p class="card-text text-muted">High Quality PRs</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <h5 class="card-title">
                            <%= reviews.filter(r => r.analysis.percentage < 60).length %>
                        </h5>
                        <p class="card-text text-muted">Needs Improvement</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                        <h5 class="card-title">
                            <%= reviews.length > 0 ? Math.round(reviews.reduce((sum, r) => sum + r.analysis.percentage, 0) / reviews.length) : 0 %>%
                        </h5>
                        <p class="card-text text-muted">Average Score</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card" id="reviewHistory">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Reviews</h5>
                <div>
                    <span class="badge bg-secondary me-2"><%= reviews.length %> Total</span>
                    <a href="/review/new" class="btn btn-sm btn-success">
                        <i class="fas fa-plus me-1"></i>
                        New Review
                    </a>
                </div>
            </div>
            <div class="card-body">
                <% if (reviews.length === 0) { %>
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No reviews yet</h5>
                        <p class="text-muted">Start by creating your first PR review</p>
                        <a href="/review/new" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Create First Review
                        </a>
                    </div>
                <% } else { %>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>PR Title</th>
                                    <th>Repository</th>
                                    <th>Score</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% reviews.slice(0, 10).forEach(review => { %>
                                    <tr>
                                        <td>
                                            <a href="/review/<%= review.id %>" class="text-decoration-none">
                                                <%= review.prData.title %>
                                            </a>
                                            <br>
                                            <small class="text-muted">PR #<%= review.prNumber %></small>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <%= review.repoUrl.replace('https://github.com/', '') %>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<%= review.analysis.percentage >= 80 ? 'success' : review.analysis.percentage >= 60 ? 'warning' : 'danger' %>">
                                                <%= review.analysis.percentage %>%
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <%= new Date(review.createdAt).toLocaleDateString() %>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-primary" onclick="viewResults('<%= JSON.stringify(review.analysis).replace(/'/g, '&apos;') %>')">
                                                    <i class="fas fa-chart-line me-1"></i>
                                                    Results
                                                </button>
                                                <a href="/review/<%= review.id %>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye me-1"></i>
                                                    Details
                                                </a>
                                                <a href="<%= review.prData.html_url %>" target="_blank" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fab fa-github me-1"></i>
                                                    GitHub
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>
                    </div>
                    
                    <% if (reviews.length > 10) { %>
                        <div class="text-center mt-3">
                            <p class="text-muted">Showing 10 of <%= reviews.length %> reviews</p>
                        </div>
                    <% } %>
                <% } %>
            </div>
        </div>
    </div>
</div>

<script>
// Scroll to review history section
function scrollToHistory() {
    document.getElementById('reviewHistory').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

// View results function
function viewResults(analysisData) {
    try {
        // Store analysis data in localStorage
        localStorage.setItem('lastAnalysis', JSON.stringify(analysisData));

        // Open results page in new tab
        window.open('/review/results', '_blank');
    } catch (error) {
        console.error('Error viewing results:', error);
        alert('Error loading results. Please try again.');
    }
}

// Add some dashboard interactivity
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to stat cards
    const statCards = document.querySelectorAll('.card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.2s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add tooltips to score badges
    const scoreBadges = document.querySelectorAll('.badge');
    scoreBadges.forEach(badge => {
        const score = parseInt(badge.textContent);
        let tooltip = '';
        if (score >= 80) {
            tooltip = 'Excellent PR quality';
        } else if (score >= 60) {
            tooltip = 'Good PR, minor improvements needed';
        } else {
            tooltip = 'PR needs significant improvements';
        }
        badge.setAttribute('title', tooltip);
        badge.setAttribute('data-bs-toggle', 'tooltip');
    });

    // Initialize Bootstrap tooltips
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
});
</script>
