<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .pr-score {
            font-size: 2rem;
            font-weight: bold;
        }
        .check-item {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 0.375rem;
        }
        .check-passed {
            background-color: #d1e7dd;
            border: 1px solid #badbcc;
        }
        .check-failed {
            background-color: #f8d7da;
            border: 1px solid #f5c2c7;
        }
        .footer {
            margin-top: 3rem;
            padding: 2rem 0;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fab fa-github me-2"></i>
                Simple PR Review
            </a>
            
            <div class="navbar-nav ms-auto">
                <% if (user) { %>
                    <a class="nav-link" href="/dashboard">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        Dashboard
                    </a>
                    <a class="nav-link" href="/review/new">
                        <i class="fas fa-plus me-1"></i>
                        New Review
                    </a>
                    <a class="nav-link" href="/review/results">
                        <i class="fas fa-chart-line me-1"></i>
                        View Results
                    </a>
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <img src="<%= user.avatar_url %>" alt="Avatar" width="24" height="24" class="rounded-circle me-1">
                            <%= user.login %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/logout">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                Logout
                            </a></li>
                        </ul>
                    </div>
                <% } else { %>
                    <a class="nav-link" href="/review/new">
                        <i class="fas fa-plus me-1"></i>
                        Analyze PR
                    </a>
                    <a class="nav-link" href="/review/results">
                        <i class="fas fa-chart-line me-1"></i>
                        View Results
                    </a>
                    <% if (githubConfigured) { %>
                        <a class="nav-link" href="/auth/github">
                            <i class="fab fa-github me-1"></i>
                            Login with GitHub
                        </a>
                    <% } else { %>
                        <span class="nav-link text-muted">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            GitHub OAuth not configured
                        </span>
                    <% } %>
                <% } %>
            </div>
        </div>
    </nav>

    <main class="container my-4">
        <%- include(page) %>
    </main>

    <footer class="footer">
        <div class="container text-center">
            <div class="row">
                <div class="col-md-12">
                    <p class="text-muted mb-0">
                        <i class="fab fa-github me-1"></i>
                        Simple PR Review Tool - Lightweight Docker-based PR analysis
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simple notification system
        function showNotification(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // Check for URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('error') === 'oauth_failed') {
            showNotification('GitHub login failed. Please try again.', 'danger');
        }
    </script>
</body>
</html>
