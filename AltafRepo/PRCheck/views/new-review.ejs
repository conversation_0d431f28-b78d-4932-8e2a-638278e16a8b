<% 
const body = `
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    New PR Review
                </h4>
            </div>
            <div class="card-body">
                <form id="reviewForm">
                    <div class="mb-3">
                        <label for="repoUrl" class="form-label">
                            <i class="fab fa-github me-1"></i>
                            GitHub Repository URL
                        </label>
                        <input 
                            type="url" 
                            class="form-control" 
                            id="repoUrl" 
                            name="repoUrl" 
                            placeholder="https://github.com/owner/repository"
                            required
                        >
                        <div class="form-text">
                            Enter the full GitHub repository URL (e.g., https://github.com/facebook/react)
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="prNumber" class="form-label">
                            <i class="fas fa-code-branch me-1"></i>
                            Pull Request Number
                        </label>
                        <input 
                            type="number" 
                            class="form-control" 
                            id="prNumber" 
                            name="prNumber" 
                            placeholder="123"
                            min="1"
                            required
                        >
                        <div class="form-text">
                            Enter the PR number (found in the PR URL: /pull/123)
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="/dashboard" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Dashboard
                        </a>
                        <button type="submit" class="btn btn-primary" id="analyzeBtn">
                            <i class="fas fa-search me-2"></i>
                            Analyze PR
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div id="analysisResult" class="mt-4" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Analysis Results
                    </h5>
                </div>
                <div class="card-body" id="resultContent">
                    <!-- Results will be loaded here -->
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    How it works
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle text-success me-2"></i>What we analyze:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Title descriptiveness</li>
                            <li><i class="fas fa-check text-success me-2"></i>Description quality</li>
                            <li><i class="fas fa-check text-success me-2"></i>Change scope</li>
                            <li><i class="fas fa-check text-success me-2"></i>PR size</li>
                            <li><i class="fas fa-check text-success me-2"></i>Branch naming</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-shield-alt text-info me-2"></i>Privacy & Security:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-info me-2"></i>Uses your GitHub token</li>
                            <li><i class="fas fa-check text-info me-2"></i>No data sent to external services</li>
                            <li><i class="fas fa-check text-info me-2"></i>Local storage only</li>
                            <li><i class="fas fa-check text-info me-2"></i>Works with private repos</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('reviewForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const analyzeBtn = document.getElementById('analyzeBtn');
    const resultDiv = document.getElementById('analysisResult');
    const resultContent = document.getElementById('resultContent');
    
    // Show loading state
    analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
    analyzeBtn.disabled = true;
    
    try {
        const formData = new FormData(this);
        const response = await fetch('/review/analyze', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Show results
            resultDiv.style.display = 'block';
            resultContent.innerHTML = generateResultHTML(result.analysis);
            
            // Scroll to results
            resultDiv.scrollIntoView({ behavior: 'smooth' });
            
            showNotification('PR analysis completed successfully!', 'success');
        } else {
            throw new Error(result.message || 'Analysis failed');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('Error analyzing PR: ' + error.message, 'danger');
    } finally {
        // Reset button
        analyzeBtn.innerHTML = '<i class="fas fa-search me-2"></i>Analyze PR';
        analyzeBtn.disabled = false;
    }
});

function generateResultHTML(analysis) {
    const scoreColor = analysis.percentage >= 80 ? 'success' : analysis.percentage >= 60 ? 'warning' : 'danger';
    
    let html = \`
        <div class="row mb-4">
            <div class="col-md-4 text-center">
                <div class="pr-score text-\${scoreColor}">
                    \${analysis.percentage}%
                </div>
                <p class="text-muted">Overall Score</p>
            </div>
            <div class="col-md-8">
                <h6>Analysis Summary</h6>
                <p>Passed \${analysis.score} out of \${analysis.totalChecks} checks</p>
                <div class="progress">
                    <div class="progress-bar bg-\${scoreColor}" style="width: \${analysis.percentage}%"></div>
                </div>
            </div>
        </div>
        
        <h6>Detailed Checks</h6>
    \`;
    
    analysis.checks.forEach(check => {
        const checkClass = check.passed ? 'check-passed' : 'check-failed';
        const icon = check.passed ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger';
        
        html += \`
            <div class="check-item \${checkClass}">
                <div class="d-flex align-items-center">
                    <i class="\${icon} me-2"></i>
                    <strong>\${check.name}</strong>
                </div>
                \${check.reason ? \`<small class="text-muted ms-4">\${check.reason}</small>\` : ''}
            </div>
        \`;
    });
    
    if (analysis.suggestions.length > 0) {
        html += \`
            <h6 class="mt-4">Suggestions for Improvement</h6>
            <ul class="list-group list-group-flush">
        \`;
        
        analysis.suggestions.forEach(suggestion => {
            html += \`
                <li class="list-group-item">
                    <strong>\${suggestion.title}</strong><br>
                    <small class="text-muted">\${suggestion.reason}</small>
                </li>
            \`;
        });
        
        html += '</ul>';
    }
    
    return html;
}
</script>
`;
%>

<%- include('layout', { title, user, githubConfigured, body }) %>
