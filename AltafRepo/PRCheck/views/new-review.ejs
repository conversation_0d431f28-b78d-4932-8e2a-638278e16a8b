
<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    New PR Review
                </h4>
            </div>
            <div class="card-body">
                <!-- Repository Selection Tabs -->
                <ul class="nav nav-tabs mb-4" id="repoTabs" role="tablist">
                    <!-- Public Repository Analysis (Always First) -->
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="manual-tab" data-bs-toggle="tab" data-bs-target="#manual-pane" type="button" role="tab">
                            <i class="fas fa-globe me-2"></i>
                            Analyze Public Repository
                        </button>
                    </li>

                    <% if (user) { %>
                    <!-- Repository Dropdown (For Authenticated Users) -->
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="dropdown-tab" data-bs-toggle="tab" data-bs-target="#dropdown-pane" type="button" role="tab">
                            <i class="fas fa-list me-2"></i>
                            Your Repositories
                        </button>
                    </li>
                    <% } else { %>
                    <!-- Login Option (For Non-Authenticated Users) -->
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="login-tab" data-bs-toggle="tab" data-bs-target="#login-pane" type="button" role="tab">
                            <i class="fab fa-github me-2"></i>
                            Login for Private Repos
                        </button>
                    </li>
                    <% } %>
                </ul>

                <div class="tab-content" id="repoTabContent">
                    <!-- Public Repository Analysis Tab (Always First and Active) -->
                    <div class="tab-pane fade show active" id="manual-pane" role="tabpanel">
                        <div class="alert alert-info mb-4">
                            <h6><i class="fas fa-info-circle me-2"></i>Public Repository Analysis</h6>
                            <p class="mb-0">
                                <strong>✅ No login required!</strong> Analyze any public GitHub repository instantly.
                                <% if (!user) { %>For private repositories, please login with GitHub.<% } %>
                            </p>
                        </div>

                        <form id="manualReviewForm">
                            <div class="mb-3">
                                <label for="repoUrl" class="form-label">
                                    <i class="fab fa-github me-1"></i>
                                    GitHub Repository URL
                                </label>
                                <input
                                    type="url"
                                    class="form-control"
                                    id="repoUrl"
                                    name="repoUrl"
                                    placeholder="https://github.com/hashicorp/terraform"
                                    value="https://github.com/hashicorp/terraform"
                                    required
                                >
                                <div class="form-text">
                                    Enter the full GitHub repository URL (e.g., https://github.com/facebook/react)
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="prNumber" class="form-label">
                                    <i class="fas fa-code-branch me-1"></i>
                                    Pull Request Number
                                </label>
                                <input
                                    type="number"
                                    class="form-control"
                                    id="prNumber"
                                    name="prNumber"
                                    placeholder="37258"
                                    value="37258"
                                    min="1"
                                    required
                                >
                                <div class="form-text">
                                    Enter the PR number (found in the PR URL: /pull/37258)
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="<% if (user) { %>/dashboard<% } else { %>/<% } %>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    <% if (user) { %>Back to Dashboard<% } else { %>Back to Home<% } %>
                                </a>
                                <button type="button" class="btn btn-info me-2" onclick="testResults()">
                                    <i class="fas fa-test-tube me-2"></i>
                                    Test Results
                                </button>
                                <button type="submit" class="btn btn-success btn-lg" id="analyzeManualBtn">
                                    <i class="fas fa-search me-2"></i>
                                    Analyze PR Now
                                </button>
                            </div>
                        </form>
                    </div>

                    <% if (user) { %>
                    <!-- Repository Dropdown Tab (Authenticated Users) -->
                    <div class="tab-pane fade" id="dropdown-pane" role="tabpanel">
                        <div class="alert alert-success mb-4">
                            <h6><i class="fas fa-check-circle me-2"></i>Your GitHub Repositories</h6>
                            <p class="mb-0">Select from your accessible repositories (public & private) and pull requests.</p>
                        </div>

                        <form id="dropdownReviewForm">
                            <div class="mb-3">
                                <label for="repoSelect" class="form-label">
                                    <i class="fab fa-github me-1"></i>
                                    Select Repository
                                </label>
                                <select class="form-select" id="repoSelect" name="repoSelect" required>
                                    <option value="">Loading your repositories...</option>
                                </select>
                                <div class="form-text">
                                    Choose from your accessible GitHub repositories (public & private)
                                </div>
                            </div>

                            <div class="mb-3" id="prSelectContainer" style="display: none;">
                                <label for="prSelect" class="form-label">
                                    <i class="fas fa-code-branch me-1"></i>
                                    Select Pull Request
                                </label>
                                <select class="form-select" id="prSelect" name="prSelect" required>
                                    <option value="">Select a repository first</option>
                                </select>
                                <div class="form-text">
                                    Choose from open pull requests in the selected repository
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="/dashboard" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    Back to Dashboard
                                </a>
                                <button type="submit" class="btn btn-primary" id="analyzeDropdownBtn" disabled>
                                    <i class="fas fa-search me-2"></i>
                                    Analyze Selected PR
                                </button>
                            </div>
                        </form>
                    </div>
                    <% } %>



                    <% if (!user) { %>
                    <!-- Login Tab (Non-authenticated Users) -->
                    <div class="tab-pane fade" id="login-pane" role="tabpanel">
                        <div class="text-center py-5">
                            <i class="fab fa-github fa-4x text-primary mb-4"></i>
                            <h4>Access Private Repositories</h4>
                            <p class="text-muted mb-4">Login with GitHub to access your private repositories and save your review history.</p>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-check text-success me-2"></i>With GitHub Login:</h6>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="fas fa-check text-success me-2"></i>Access private repositories</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Repository dropdown selection</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Save review history</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Personal dashboard</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-info text-info me-2"></i>Without Login:</h6>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="fas fa-check text-info me-2"></i>Analyze public repositories</li>
                                        <li><i class="fas fa-check text-info me-2"></i>Manual URL entry</li>
                                        <li><i class="fas fa-times text-muted me-2"></i>No saved history</li>
                                        <li><i class="fas fa-times text-muted me-2"></i>No private repo access</li>
                                    </ul>
                                </div>
                            </div>

                            <% if (githubConfigured) { %>
                            <a href="/auth/github" class="btn btn-primary btn-lg mt-4">
                                <i class="fab fa-github me-2"></i>
                                Login with GitHub
                            </a>
                            <% } else { %>
                            <div class="alert alert-warning mt-4">
                                <strong>GitHub OAuth not configured.</strong><br>
                                Please configure GitHub OAuth to enable login functionality.
                            </div>
                            <% } %>
                        </div>
                    </div>
                    <% } %>
                </div>

            </div>
        </div>

        <div id="analysisResult" class="mt-4" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Analysis Results
                    </h5>
                </div>
                <div class="card-body" id="resultContent">
                    <!-- Results will be loaded here -->
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    How it works
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle text-success me-2"></i>What we analyze:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Title descriptiveness</li>
                            <li><i class="fas fa-check text-success me-2"></i>Description quality</li>
                            <li><i class="fas fa-check text-success me-2"></i>Change scope</li>
                            <li><i class="fas fa-check text-success me-2"></i>PR size</li>
                            <li><i class="fas fa-check text-success me-2"></i>Branch naming</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-shield-alt text-info me-2"></i>Privacy & Security:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-info me-2"></i>Uses your GitHub token</li>
                            <li><i class="fas fa-check text-info me-2"></i>No data sent to external services</li>
                            <li><i class="fas fa-check text-info me-2"></i>Local storage only</li>
                            <li><i class="fas fa-check text-info me-2"></i>Works with private repos</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analysis Results Section -->
        <div class="card mt-4" id="resultsCard" style="display: none;">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    PR Analysis Results
                </h5>
            </div>
            <div class="card-body" id="analysisResults">
                <!-- Results will be populated here -->
            </div>
        </div>

        <!-- Loading Indicator -->
        <div class="card mt-4" id="loadingCard" style="display: none;">
            <div class="card-body text-center py-5">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Analyzing Pull Request...</h5>
                <p class="text-muted">Please wait while we analyze the PR against 10 comprehensive checks</p>
            </div>
        </div>

        <!-- Error Display -->
        <div class="card mt-4 border-danger" id="errorCard" style="display: none;">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Analysis Error
                </h5>
            </div>
            <div class="card-body" id="errorMessage">
                <!-- Error message will be populated here -->
            </div>
        </div>
    </div>
</div>

<script>
// Load user repositories on page load (only if user is logged in)
document.addEventListener('DOMContentLoaded', async function() {
    // Check if user is logged in and repository dropdown exists
    const repoSelect = document.getElementById('repoSelect');
    if (repoSelect) {
        await loadUserRepositories();
    }
});

// Load user repositories
async function loadUserRepositories() {
    const repoSelect = document.getElementById('repoSelect');
    if (!repoSelect) return;

    try {
        const response = await fetch('/api/repositories');

        if (!response.ok) {
            throw new Error('Failed to fetch repositories');
        }

        const repos = await response.json();

        // Clear loading option
        repoSelect.innerHTML = '<option value="">Select a repository...</option>';

        // Add repositories to dropdown
        repos.forEach(repo => {
            const option = document.createElement('option');
            option.value = repo.full_name;
            option.textContent = `${repo.full_name} ${repo.private ? '(Private)' : '(Public)'}`;
            option.dataset.repoData = JSON.stringify(repo);
            repoSelect.appendChild(option);
        });

    } catch (error) {
        console.error('Error loading repositories:', error);
        repoSelect.innerHTML = '<option value="">Error loading repositories</option>';
        showNotification('Failed to load repositories: ' + error.message, 'warning');
    }
}

// Handle repository selection
document.getElementById('repoSelect').addEventListener('change', async function() {
    const selectedRepo = this.value;
    const prSelectContainer = document.getElementById('prSelectContainer');
    const prSelect = document.getElementById('prSelect');
    const analyzeBtn = document.getElementById('analyzeDropdownBtn');

    if (!selectedRepo) {
        prSelectContainer.style.display = 'none';
        analyzeBtn.disabled = true;
        return;
    }

    // Show PR selection and load PRs
    prSelectContainer.style.display = 'block';
    prSelect.innerHTML = '<option value="">Loading pull requests...</option>';

    try {
        const [owner, repo] = selectedRepo.split('/');
        const response = await fetch(`/api/repositories/${owner}/${repo}/pulls`);

        if (!response.ok) {
            throw new Error('Failed to fetch pull requests');
        }

        const pulls = await response.json();

        // Clear loading option
        prSelect.innerHTML = '<option value="">Select a pull request...</option>';

        if (pulls.length === 0) {
            prSelect.innerHTML = '<option value="">No open pull requests found</option>';
        } else {
            // Add PRs to dropdown
            pulls.forEach(pr => {
                const option = document.createElement('option');
                option.value = pr.number;
                option.textContent = `#${pr.number}: ${pr.title}`;
                option.dataset.prData = JSON.stringify(pr);
                prSelect.appendChild(option);
            });
        }

    } catch (error) {
        console.error('Error loading pull requests:', error);
        prSelect.innerHTML = '<option value="">Error loading pull requests</option>';
        showNotification('Failed to load pull requests: ' + error.message, 'warning');
    }
});

// Handle PR selection
document.getElementById('prSelect').addEventListener('change', function() {
    const analyzeBtn = document.getElementById('analyzeDropdownBtn');
    analyzeBtn.disabled = !this.value;
});

// Handle dropdown form submission (only if form exists)
const dropdownForm = document.getElementById('dropdownReviewForm');
if (dropdownForm) {
    dropdownForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const repoSelect = document.getElementById('repoSelect');
        const prSelect = document.getElementById('prSelect');
        const analyzeBtn = document.getElementById('analyzeDropdownBtn');

        if (!repoSelect.value || !prSelect.value) {
            showNotification('Please select both repository and pull request', 'warning');
            return;
        }

        // Show loading state
        analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
        analyzeBtn.disabled = true;

        try {
            // Create form data for analysis
            const formData = new FormData();
            formData.append('repoUrl', `https://github.com/${repoSelect.value}`);
            formData.append('prNumber', prSelect.value);

            const response = await fetch('/review/analyze', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                console.log('Dropdown analysis completed successfully...', result.analysis);

                // Store analysis in localStorage for results page
                try {
                    localStorage.setItem('lastAnalysis', JSON.stringify(result.analysis));
                    console.log('Analysis stored in localStorage');
                } catch (error) {
                    console.error('Error storing analysis:', error);
                }

                // Show success notification
                showNotification('Analysis completed! Redirecting to results page...', 'success');

                // Auto-redirect after delay
                setTimeout(() => {
                    console.log('Auto-redirecting to results page...');
                    window.location.href = '/review/results';
                }, 2000);
            } else {
                throw new Error(result.message || 'Analysis failed');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('Error analyzing PR: ' + error.message, 'danger');
        } finally {
            // Reset button
            analyzeBtn.innerHTML = '<i class="fas fa-search me-2"></i>Analyze Selected PR';
            analyzeBtn.disabled = false;
        }
    });
}

// Handle manual form submission
document.getElementById('manualReviewForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const analyzeBtn = document.getElementById('analyzeManualBtn');

    // Hide all result cards
    hideAllResultCards();

    // Show loading indicator
    showLoadingCard();

    // Show loading state on button
    analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
    analyzeBtn.disabled = true;

    try {
        const formData = new FormData(this);
        console.log('Sending analysis request...', {
            repoUrl: formData.get('repoUrl'),
            prNumber: formData.get('prNumber')
        });

        const response = await fetch('/review/analyze', {
            method: 'POST',
            body: formData
        });

        console.log('Response status:', response.status);

        const result = await response.json();
        console.log('Analysis result:', result);

        // Hide loading
        hideLoadingCard();

        if (result.success) {
            console.log('Analysis completed successfully...', result.analysis);

            // Store analysis in localStorage for results page
            try {
                localStorage.setItem('lastAnalysis', JSON.stringify(result.analysis));
                console.log('Analysis stored in localStorage');
            } catch (error) {
                console.error('Error storing analysis:', error);
            }

            // Show success notification
            showNotification('Analysis completed! Redirecting to results page...', 'success');

            // Also show results on current page as backup
            showAnalysisResults(result.analysis);

            // Add redirect button
            setTimeout(() => {
                const redirectBtn = document.createElement('button');
                redirectBtn.className = 'btn btn-primary btn-lg mt-3';
                redirectBtn.innerHTML = '<i class="fas fa-external-link-alt me-2"></i>View Full Results Page';
                redirectBtn.onclick = () => {
                    window.open('/review/results', '_blank');
                };

                const resultsCard = document.getElementById('resultsCard');
                if (resultsCard) {
                    resultsCard.appendChild(redirectBtn);
                }
            }, 1000);

            // Auto-redirect after longer delay
            setTimeout(() => {
                console.log('Auto-redirecting to results page...');
                window.location.href = '/review/results';
            }, 3000);
        } else {
            console.error('Analysis failed:', result);
            showErrorCard(result.message || result.error || 'Analysis failed');
        }
    } catch (error) {
        console.error('Error analyzing PR:', error);

        // Hide loading
        hideLoadingCard();

        // Handle different types of errors
        let errorMessage = 'Error analyzing PR: ';
        if (error.message.includes('404')) {
            errorMessage += 'Repository or PR not found. Please check the URL and PR number.';
        } else if (error.message.includes('403')) {
            errorMessage += 'Access denied. This repository may be private - please login with GitHub.';
        } else if (error.message.includes('401')) {
            errorMessage += 'Authentication required. Please login with GitHub for private repositories.';
        } else {
            errorMessage += error.message;
        }

        showErrorCard(errorMessage);
        showNotification(errorMessage, 'danger');
    } finally {
        // Reset button
        analyzeBtn.innerHTML = '<i class="fas fa-search me-2"></i>Analyze PR Now';
        analyzeBtn.disabled = false;
    }
});

// Helper functions for result cards
function hideAllResultCards() {
    document.getElementById('resultsCard').style.display = 'none';
    document.getElementById('loadingCard').style.display = 'none';
    document.getElementById('errorCard').style.display = 'none';
}

function showLoadingCard() {
    hideAllResultCards();
    const loadingCard = document.getElementById('loadingCard');
    loadingCard.style.display = 'block';
    loadingCard.scrollIntoView({ behavior: 'smooth' });
}

function hideLoadingCard() {
    document.getElementById('loadingCard').style.display = 'none';
}

function showErrorCard(message) {
    hideAllResultCards();
    const errorCard = document.getElementById('errorCard');
    const errorMessage = document.getElementById('errorMessage');

    errorMessage.innerHTML = `
        <div class="alert alert-danger mb-0">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Analysis Failed</h6>
            <p class="mb-0">${message}</p>
        </div>
        <div class="mt-3">
            <h6>Troubleshooting Tips:</h6>
            <ul class="mb-0">
                <li>Verify the repository URL is correct and accessible</li>
                <li>Check that the PR number exists</li>
                <li>For private repositories, please login with GitHub</li>
                <li>Try again in a few moments</li>
            </ul>
        </div>
    `;

    errorCard.style.display = 'block';
    errorCard.scrollIntoView({ behavior: 'smooth' });
}

// Show analysis results
function showAnalysisResults(analysis) {
    console.log('showAnalysisResults called with:', analysis);

    hideAllResultCards();

    // Use the existing results card
    const resultsCard = document.getElementById('resultsCard');
    const resultsDiv = document.getElementById('analysisResults');

    console.log('Results elements found:', { resultsCard: !!resultsCard, resultsDiv: !!resultsDiv });

    if (!resultsCard || !resultsDiv) {
        console.error('Results elements not found', { resultsCard, resultsDiv });
        showErrorCard('Unable to display results. Please refresh the page and try again.');
        return;
    }

    const resultHTML = generateSimpleResultHTML(analysis);
    console.log('Generated HTML length:', resultHTML.length);

    resultsDiv.innerHTML = resultHTML;
    resultsCard.style.display = 'block';
    resultsCard.scrollIntoView({ behavior: 'smooth' });

    console.log('Results displayed successfully');
}

// Simple results generation function
function generateSimpleResultHTML(analysis) {
    console.log('generateSimpleResultHTML called with:', analysis);

    const scoreColor = analysis.percentage >= 80 ? 'success' : analysis.percentage >= 60 ? 'warning' : 'danger';

    let html = `
        <div class="row mb-4">
            <div class="col-md-4 text-center">
                <div class="display-4 text-${scoreColor} mb-2">
                    <i class="fas fa-chart-pie me-2"></i>${analysis.percentage}%
                </div>
                <h5>Overall Score</h5>
                <p class="text-muted">${analysis.score} out of ${analysis.totalChecks} checks passed</p>
            </div>
            <div class="col-md-8">
                <h5><i class="fas fa-list-check me-2"></i>Analysis Results</h5>
                <div class="row">
    `;

    // Display checks
    analysis.checks.forEach(check => {
        const checkColor = check.passed ? 'success' : 'danger';
        const checkIcon = check.passed ? 'check-circle' : 'times-circle';

        html += `
            <div class="col-md-6 mb-2">
                <div class="card border-${checkColor}">
                    <div class="card-body py-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-${checkIcon} text-${checkColor} me-2"></i>
                            <div>
                                <strong>${check.name}</strong>
                                <br><small class="text-muted">${check.reason}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    // Add suggestions if any
    if (analysis.suggestions && analysis.suggestions.length > 0) {
        html += `
            <div class="mt-4">
                <h5><i class="fas fa-lightbulb me-2"></i>Improvement Suggestions</h5>
        `;

        analysis.suggestions.forEach((suggestion, index) => {
            html += `
                <div class="alert alert-warning">
                    <strong>${index + 1}. ${suggestion.title}</strong><br>
                    ${suggestion.reason}
                </div>
            `;
        });

        html += `</div>`;
    }

    // Add reference links
    html += `
        <div class="mt-4 p-3 bg-light rounded">
            <h6><i class="fas fa-info-circle me-2"></i>Analysis Based On:</h6>
            <ul class="list-unstyled mb-0">
                <li><i class="fas fa-check text-success me-2"></i><a href="https://cbea.ms/git-commit/" target="_blank">Git Commit Message Guidelines</a></li>
                <li><i class="fas fa-check text-success me-2"></i>PR Best Practices from prchecklist.txt</li>
                <li><i class="fas fa-check text-success me-2"></i>Industry Standard Code Review Guidelines</li>
            </ul>
        </div>
    `;

    console.log('Simple HTML generated successfully');
    return html;
}

function generateResultHTML(analysis) {
    console.log('generateResultHTML called with:', analysis);

    const scoreColor = analysis.percentage >= 80 ? 'success' : analysis.percentage >= 60 ? 'warning' : 'danger';
    const scoreIcon = analysis.percentage >= 80 ? 'fas fa-check-circle' : analysis.percentage >= 60 ? 'fas fa-exclamation-triangle' : 'fas fa-times-circle';

    console.log('Score details:', { percentage: analysis.percentage, scoreColor, scoreIcon });

    let html = \`
        <div class="row mb-4">
            <div class="col-md-4 text-center">
                <div class="pr-score-container">
                    <div class="pr-score text-\${scoreColor}">
                        <i class="\${scoreIcon} me-2"></i>
                        \${analysis.percentage}%
                    </div>
                    <p class="text-muted">Overall Score</p>
                    <small class="text-muted">Based on Git Commit Guidelines & PR Best Practices</small>
                </div>
            </div>
            <div class="col-md-8">
                <h6><i class="fas fa-chart-bar me-2"></i>Analysis Summary</h6>
                <p>Passed <strong>\${analysis.score}</strong> out of <strong>\${analysis.totalChecks}</strong> comprehensive checks</p>
                <div class="progress mb-3" style="height: 10px;">
                    <div class="progress-bar bg-\${scoreColor}" style="width: \${analysis.percentage}%"></div>
                </div>

                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-success mb-0">\${analysis.score}</h5>
                            <small class="text-muted">Passed</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-danger mb-0">\${analysis.totalChecks - analysis.score}</h5>
                            <small class="text-muted">Failed</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="text-info mb-0">\${analysis.suggestions.length}</h5>
                        <small class="text-muted">Suggestions</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <h6><i class="fas fa-list-check me-2"></i>Detailed Analysis</h6>
                <div class="checks-container">
    \`;

    // Group checks by category
    const gitCommitChecks = analysis.checks.slice(0, 4); // Title length, capitalization, period, imperative
    const prQualityChecks = analysis.checks.slice(4); // Description, branch naming, scope, etc.

    html += \`
                    <div class="check-category mb-4">
                        <h6 class="text-primary"><i class="fas fa-git-alt me-2"></i>Git Commit Guidelines</h6>
    \`;

    gitCommitChecks.forEach(check => {
        const checkClass = check.passed ? 'check-passed' : 'check-failed';
        const icon = check.passed ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger';

        html += \`
                        <div class="check-item \${checkClass} mb-2">
                            <div class="d-flex align-items-start">
                                <i class="\${icon} me-2 mt-1"></i>
                                <div>
                                    <strong>\${check.name}</strong>
                                    \${check.reason ? \`<br><small class="text-muted">\${check.reason}</small>\` : ''}
                                </div>
                            </div>
                        </div>
        \`;
    });

    html += \`
                    </div>
                    <div class="check-category">
                        <h6 class="text-info"><i class="fas fa-code-branch me-2"></i>PR Quality Checks</h6>
    \`;

    prQualityChecks.forEach(check => {
        const checkClass = check.passed ? 'check-passed' : 'check-failed';
        const icon = check.passed ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger';

        html += \`
                        <div class="check-item \${checkClass} mb-2">
                            <div class="d-flex align-items-start">
                                <i class="\${icon} me-2 mt-1"></i>
                                <div>
                                    <strong>\${check.name}</strong>
                                    \${check.reason ? \`<br><small class="text-muted">\${check.reason}</small>\` : ''}
                                </div>
                            </div>
                        </div>
        \`;
    });

    html += \`
                    </div>
                </div>
            </div>
            <div class="col-md-4">
    \`;

    if (analysis.suggestions.length > 0) {
        html += \`
                <h6><i class="fas fa-lightbulb me-2"></i>Improvement Suggestions</h6>
                <div class="suggestions-container">
        \`;

        analysis.suggestions.forEach((suggestion, index) => {
            const priority = index < 3 ? 'high' : 'medium';
            const badgeColor = priority === 'high' ? 'danger' : 'warning';

            html += \`
                    <div class="alert alert-\${badgeColor} alert-sm mb-2">
                        <div class="d-flex align-items-start">
                            <span class="badge bg-\${badgeColor} me-2">\${index + 1}</span>
                            <div>
                                <strong>\${suggestion.title}</strong><br>
                                <small>\${suggestion.reason}</small>
                            </div>
                        </div>
                    </div>
            \`;
        });

        html += \`
                </div>
        \`;
    } else {
        html += \`
                <div class="text-center py-4">
                    <i class="fas fa-trophy fa-3x text-success mb-3"></i>
                    <h6 class="text-success">Excellent Work!</h6>
                    <p class="text-muted">All checks passed. This PR follows best practices.</p>
                </div>
        \`;
    }

    html += \`
            </div>
        </div>

        <div class="mt-4 p-3 bg-light rounded">
            <h6><i class="fas fa-info-circle me-2"></i>Analysis Based On:</h6>
            <ul class="list-unstyled mb-0">
                <li><i class="fas fa-check text-success me-2"></i><a href="https://cbea.ms/git-commit/" target="_blank">Git Commit Message Guidelines</a></li>
                <li><i class="fas fa-check text-success me-2"></i>PR Best Practices from prchecklist.txt</li>
                <li><i class="fas fa-check text-success me-2"></i>Industry Standard Code Review Guidelines</li>
            </ul>
        </div>
    \`;

    return html;
}

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification-toast');
    existingNotifications.forEach(n => n.remove());

    // Create notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        animation: slideInRight 0.3s ease-out;
    `;

    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Test function to verify results display
function testResults() {
    console.log('Testing results display...');

    // Show loading first
    showLoadingCard();

    // Simulate loading delay
    setTimeout(() => {
        const testAnalysis = {
            score: 9,
            totalChecks: 10,
            percentage: 90,
            checks: [
                { name: "Title Length (≤50 chars)", passed: true, reason: "Title follows 50-character guideline" },
                { name: "Capitalized Title", passed: true, reason: "Title is properly capitalized" },
                { name: "No Period in Title", passed: true, reason: "Title correctly omits trailing period" },
                { name: "Imperative Mood", passed: true, reason: "Title uses imperative mood" },
                { name: "Meaningful Description", passed: true, reason: "PR has detailed description explaining context" },
                { name: "Branch Naming Convention", passed: false, reason: "Branch should follow format: TICKET-123_description" },
                { name: "Reasonable Scope", passed: true, reason: "PR has reasonable scope" },
                { name: "Manageable Size", passed: true, reason: "PR has manageable size" },
                { name: "Targets Main/Master", passed: true, reason: "Correctly targets main/master branch" },
                { name: "Testing Information", passed: true, reason: "Includes testing information" }
            ],
            suggestions: [
                { title: "Branch Naming Convention", reason: "Branch should follow format: TICKET-123_description" }
            ]
        };

        showAnalysisResults(testAnalysis);
        showNotification('Test results displayed successfully! This shows what a real analysis looks like.', 'success');
    }, 1500); // 1.5 second delay to simulate API call
}
</script>

