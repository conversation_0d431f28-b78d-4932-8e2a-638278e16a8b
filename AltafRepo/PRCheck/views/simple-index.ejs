<% 
const body = `
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="text-center mb-5">
            <h1 class="display-4 mb-3">
                <i class="fab fa-github text-primary me-3"></i>
                Simple PR Review Tool
            </h1>
            <p class="lead text-muted">
                Lightweight Docker-based tool for analyzing GitHub Pull Requests
            </p>
        </div>

        <div class="row g-4 mb-5">
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-code-branch fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Manual PR Input</h5>
                        <p class="card-text">Simply paste your GitHub repo URL and PR number to get started</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                        <h5 class="card-title">GitHub OAuth</h5>
                        <p class="card-text">Secure login with GitHub - access both public and private repos</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="fas fa-database fa-3x text-info mb-3"></i>
                        <h5 class="card-title">Local Storage</h5>
                        <p class="card-text">No external databases - all data stored locally in Docker</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <% if (user) { %>
                    <h3 class="card-title">Welcome back, <%= user.name || user.login %>!</h3>
                    <p class="card-text">Ready to review some pull requests?</p>
                    <div class="d-grid gap-2 d-md-block">
                        <a href="/dashboard" class="btn btn-primary btn-lg">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Go to Dashboard
                        </a>
                        <a href="/review/new" class="btn btn-success btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            New Review
                        </a>
                    </div>
                <% } else { %>
                    <h3 class="card-title">Get Started</h3>
                    <% if (githubConfigured) { %>
                        <p class="card-text">Login with your GitHub account to start reviewing pull requests</p>
                        <a href="/auth/github" class="btn btn-primary btn-lg">
                            <i class="fab fa-github me-2"></i>
                            Login with GitHub
                        </a>
                    <% } else { %>
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>GitHub OAuth Not Configured</h5>
                            <p class="mb-2">To enable GitHub login, set these environment variables:</p>
                            <ul class="list-unstyled">
                                <li><code>GITHUB_CLIENT_ID</code> - Your GitHub OAuth App Client ID</li>
                                <li><code>GITHUB_CLIENT_SECRET</code> - Your GitHub OAuth App Client Secret</li>
                                <li><code>GITHUB_CALLBACK_URL</code> - OAuth callback URL (optional)</li>
                            </ul>
                            <hr>
                            <p class="mb-0">
                                <strong>How to create a GitHub OAuth App:</strong><br>
                                1. Go to GitHub Settings → Developer settings → OAuth Apps<br>
                                2. Click "New OAuth App"<br>
                                3. Set Authorization callback URL to: <code>http://localhost:3000/auth/github/callback</code>
                            </p>
                        </div>
                    <% } %>
                <% } %>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-md-6">
                <h4><i class="fas fa-check-circle text-success me-2"></i>Features</h4>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Rule-based PR analysis</li>
                    <li><i class="fas fa-check text-success me-2"></i>GitHub OAuth integration</li>
                    <li><i class="fas fa-check text-success me-2"></i>Local data storage</li>
                    <li><i class="fas fa-check text-success me-2"></i>Docker deployment</li>
                    <li><i class="fas fa-check text-success me-2"></i>Private & public repo support</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h4><i class="fas fa-info-circle text-info me-2"></i>Analysis Checks</h4>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-info me-2"></i>Descriptive title</li>
                    <li><i class="fas fa-check text-info me-2"></i>Meaningful description</li>
                    <li><i class="fas fa-check text-info me-2"></i>Reasonable scope</li>
                    <li><i class="fas fa-check text-info me-2"></i>Manageable size</li>
                    <li><i class="fas fa-check text-info me-2"></i>Feature branch usage</li>
                </ul>
            </div>
        </div>
    </div>
</div>
`;
%>

<%- include('layout', { title, user, githubConfigured, body }) %>
