<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        PR Analysis Results
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Results will be populated here -->
                    <div id="resultsContainer">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>Loading Analysis Results...</h5>
                            <p class="text-muted">Please wait while we load your PR analysis</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4 text-center">
                <a href="/review/new" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Analyze Another PR
                </a>
                <% if (user) { %>
                <a href="/dashboard" class="btn btn-secondary ms-2">
                    <i class="fas fa-dashboard me-2"></i>
                    Back to Dashboard
                </a>
                <% } else { %>
                <a href="/" class="btn btn-secondary ms-2">
                    <i class="fas fa-home me-2"></i>
                    Back to Home
                </a>
                <% } %>
            </div>
        </div>
    </div>
</div>

<script>
// Get analysis data from URL parameters or localStorage
function loadAnalysisResults() {
    const urlParams = new URLSearchParams(window.location.search);
    const analysisData = urlParams.get('data');
    
    if (analysisData) {
        try {
            const analysis = JSON.parse(decodeURIComponent(analysisData));
            displayResults(analysis);
        } catch (error) {
            console.error('Error parsing analysis data:', error);
            showError('Invalid analysis data');
        }
    } else {
        // Try to get from localStorage
        const storedAnalysis = localStorage.getItem('lastAnalysis');
        if (storedAnalysis) {
            try {
                const analysis = JSON.parse(storedAnalysis);
                displayResults(analysis);
            } catch (error) {
                console.error('Error parsing stored analysis:', error);
                showError('No analysis data found');
            }
        } else {
            showError('No analysis data found');
        }
    }
}

function displayResults(analysis) {
    const container = document.getElementById('resultsContainer');
    
    const scoreColor = analysis.percentage >= 80 ? 'success' : analysis.percentage >= 60 ? 'warning' : 'danger';
    
    let html = `
        <div class="row mb-4">
            <div class="col-md-4 text-center">
                <div class="display-3 text-${scoreColor} mb-3">
                    <i class="fas fa-chart-pie me-2"></i>${analysis.percentage}%
                </div>
                <h4>Overall Score</h4>
                <p class="text-muted fs-5">${analysis.score} out of ${analysis.totalChecks} checks passed</p>
                
                <div class="progress mb-3" style="height: 20px;">
                    <div class="progress-bar bg-${scoreColor}" role="progressbar" 
                         style="width: ${analysis.percentage}%" 
                         aria-valuenow="${analysis.percentage}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                        ${analysis.percentage}%
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <h4><i class="fas fa-list-check me-2"></i>Detailed Analysis</h4>
                
                <!-- Git Commit Guidelines -->
                <h5 class="mt-4"><i class="fab fa-git-alt me-2"></i>Git Commit Guidelines</h5>
                <div class="row">
    `;
    
    // Filter and display Git checks
    const gitChecks = analysis.checks.filter(check => 
        check.name.includes('Title') || check.name.includes('Capitalized') || 
        check.name.includes('Period') || check.name.includes('Imperative')
    );
    
    gitChecks.forEach(check => {
        const checkColor = check.passed ? 'success' : 'danger';
        const checkIcon = check.passed ? 'check-circle' : 'times-circle';
        
        html += `
            <div class="col-md-6 mb-3">
                <div class="card border-${checkColor}">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-${checkIcon} text-${checkColor} me-3 mt-1"></i>
                            <div>
                                <h6 class="card-title mb-1">${check.name}</h6>
                                <p class="card-text small text-muted mb-0">${check.reason}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += `
                </div>
                
                <!-- PR Quality Checks -->
                <h5 class="mt-4"><i class="fas fa-code-branch me-2"></i>PR Quality Checks</h5>
                <div class="row">
    `;
    
    // Filter and display PR checks
    const prChecks = analysis.checks.filter(check => !gitChecks.includes(check));
    
    prChecks.forEach(check => {
        const checkColor = check.passed ? 'success' : 'danger';
        const checkIcon = check.passed ? 'check-circle' : 'times-circle';
        
        html += `
            <div class="col-md-6 mb-3">
                <div class="card border-${checkColor}">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-${checkIcon} text-${checkColor} me-3 mt-1"></i>
                            <div>
                                <h6 class="card-title mb-1">${check.name}</h6>
                                <p class="card-text small text-muted mb-0">${check.reason}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += `
                </div>
            </div>
        </div>
    `;
    
    // Add suggestions if any
    if (analysis.suggestions && analysis.suggestions.length > 0) {
        html += `
            <div class="mt-5">
                <h4><i class="fas fa-lightbulb me-2"></i>Improvement Suggestions</h4>
                <div class="row">
        `;
        
        analysis.suggestions.forEach((suggestion, index) => {
            html += `
                <div class="col-md-6 mb-3">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>${index + 1}. ${suggestion.title}</h6>
                        <p class="mb-0">${suggestion.reason}</p>
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
    }
    
    // Add reference links
    html += `
        <div class="mt-5 p-4 bg-light rounded">
            <h5><i class="fas fa-info-circle me-2"></i>Analysis Based On:</h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <a href="https://cbea.ms/git-commit/" target="_blank" class="text-decoration-none">
                            Git Commit Guidelines
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <span>PR Best Practices</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <span>Industry Standards</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

function showError(message) {
    const container = document.getElementById('resultsContainer');
    container.innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle text-warning fa-4x mb-3"></i>
            <h4>No Results Found</h4>
            <p class="text-muted">${message}</p>
            <a href="/review/new" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Start New Analysis
            </a>
        </div>
    `;
}

// Load results when page loads
document.addEventListener('DOMContentLoaded', loadAnalysisResults);
</script>
