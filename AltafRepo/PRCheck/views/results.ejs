<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        PR Analysis Results
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Results will be populated here -->
                    <div id="resultsContainer">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>Loading Analysis Results...</h5>
                            <p class="text-muted">Please wait while we load your PR analysis</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4 text-center">
                <a href="/review/new" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Analyze Another PR
                </a>
                <% if (user) { %>
                <a href="/dashboard" class="btn btn-secondary ms-2">
                    <i class="fas fa-dashboard me-2"></i>
                    Back to Dashboard
                </a>
                <% } else { %>
                <a href="/" class="btn btn-secondary ms-2">
                    <i class="fas fa-home me-2"></i>
                    Back to Home
                </a>
                <% } %>
            </div>
        </div>
    </div>
</div>

<script>
// Get analysis data from URL parameters or localStorage
function loadAnalysisResults() {
    console.log('Loading analysis results...');

    const urlParams = new URLSearchParams(window.location.search);
    const analysisData = urlParams.get('data');

    console.log('URL params data:', analysisData);

    if (analysisData) {
        try {
            const analysis = JSON.parse(decodeURIComponent(analysisData));
            console.log('Parsed URL analysis:', analysis);
            displayResults(analysis);
            return;
        } catch (error) {
            console.error('Error parsing URL analysis data:', error);
        }
    }

    // Try to get from localStorage
    const storedAnalysis = localStorage.getItem('lastAnalysis');
    console.log('Stored analysis:', storedAnalysis);

    if (storedAnalysis) {
        try {
            const analysis = JSON.parse(storedAnalysis);
            console.log('Parsed stored analysis:', analysis);
            displayResults(analysis);
            return;
        } catch (error) {
            console.error('Error parsing stored analysis:', error);
        }
    }

    // If no data found, show sample data for demonstration
    console.log('No analysis data found, showing sample data');
    showSampleResults();
}

function displayResults(analysis) {
    const container = document.getElementById('resultsContainer');
    
    const scoreColor = analysis.percentage >= 80 ? 'success' : analysis.percentage >= 60 ? 'warning' : 'danger';
    
    let html = `
        <div class="row mb-4">
            <div class="col-md-4 text-center">
                <div class="display-3 text-${scoreColor} mb-3">
                    <i class="fas fa-chart-pie me-2"></i>${analysis.percentage}%
                </div>
                <h4>Overall Score</h4>
                <p class="text-muted fs-5">${analysis.score} out of ${analysis.totalChecks} checks passed</p>
                
                <div class="progress mb-3" style="height: 20px;">
                    <div class="progress-bar bg-${scoreColor}" role="progressbar" 
                         style="width: ${analysis.percentage}%" 
                         aria-valuenow="${analysis.percentage}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                        ${analysis.percentage}%
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <h4><i class="fas fa-list-check me-2"></i>Detailed Analysis</h4>
                
                <!-- Git Commit Guidelines -->
                <h5 class="mt-4"><i class="fab fa-git-alt me-2"></i>Git Commit Guidelines</h5>
                <div class="row">
    `;
    
    // Filter and display Git checks
    const gitChecks = analysis.checks.filter(check => 
        check.name.includes('Title') || check.name.includes('Capitalized') || 
        check.name.includes('Period') || check.name.includes('Imperative')
    );
    
    gitChecks.forEach(check => {
        const checkColor = check.passed ? 'success' : 'danger';
        const checkIcon = check.passed ? 'check-circle' : 'times-circle';
        
        html += `
            <div class="col-md-6 mb-3">
                <div class="card border-${checkColor}">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-${checkIcon} text-${checkColor} me-3 mt-1"></i>
                            <div>
                                <h6 class="card-title mb-1">${check.name}</h6>
                                <p class="card-text small text-muted mb-0">${check.reason}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += `
                </div>
                
                <!-- PR Quality Checks -->
                <h5 class="mt-4"><i class="fas fa-code-branch me-2"></i>PR Quality Checks</h5>
                <div class="row">
    `;
    
    // Filter and display PR checks
    const prChecks = analysis.checks.filter(check => !gitChecks.includes(check));
    
    prChecks.forEach(check => {
        const checkColor = check.passed ? 'success' : 'danger';
        const checkIcon = check.passed ? 'check-circle' : 'times-circle';
        
        html += `
            <div class="col-md-6 mb-3">
                <div class="card border-${checkColor}">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-${checkIcon} text-${checkColor} me-3 mt-1"></i>
                            <div>
                                <h6 class="card-title mb-1">${check.name}</h6>
                                <p class="card-text small text-muted mb-0">${check.reason}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += `
                </div>
            </div>
        </div>
    `;

    // Add detailed file analysis if available
    if (analysis.file_analysis && analysis.file_analysis.files.length > 0) {
        html += `
            <div class="mt-5">
                <h4><i class="fas fa-file-code me-2"></i>Detailed File Analysis</h4>
                <div class="row mb-3">
                    <div class="col-md-3 text-center">
                        <div class="card border-info">
                            <div class="card-body">
                                <h5 class="text-info">${analysis.file_analysis.files.length}</h5>
                                <small>Files Changed</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="card border-primary">
                            <div class="card-body">
                                <h5 class="text-primary">${analysis.file_analysis.total_comments}</h5>
                                <small>Review Comments</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="card border-warning">
                            <div class="card-body">
                                <h5 class="text-warning">${analysis.file_analysis.suggestions_count}</h5>
                                <small>Suggestions</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="card border-danger">
                            <div class="card-body">
                                <h5 class="text-danger">${analysis.file_analysis.security_issues}</h5>
                                <small>Security Issues</small>
                            </div>
                        </div>
                    </div>
                </div>
        `;

        // Display each file's analysis
        analysis.file_analysis.files.forEach((file, index) => {
            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-file me-2"></i>
                                ${file.filename}
                                <span class="badge bg-${file.status === 'added' ? 'success' : file.status === 'modified' ? 'primary' : 'danger'} ms-2">
                                    ${file.status}
                                </span>
                            </h6>
                            <small class="text-muted">
                                +${file.additions} -${file.deletions}
                            </small>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
            `;

            // Security concerns
            if (file.security_concerns && file.security_concerns.length > 0) {
                html += `
                    <div class="col-12 mb-3">
                        <h6 class="text-danger"><i class="fas fa-shield-alt me-2"></i>Security Concerns</h6>
                `;
                file.security_concerns.forEach(concern => {
                    html += `
                        <div class="alert alert-danger">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
                                <div>
                                    <strong>Line ${concern.line}:</strong> ${concern.message}
                                    <br><code class="small">${concern.code}</code>
                                    ${concern.suggestion ? `<br><small class="text-muted">${concern.suggestion}</small>` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += `</div>`;
            }

            // Issues
            if (file.issues && file.issues.length > 0) {
                html += `
                    <div class="col-md-6 mb-3">
                        <h6 class="text-warning"><i class="fas fa-exclamation-circle me-2"></i>Issues Found</h6>
                `;
                file.issues.forEach(issue => {
                    html += `
                        <div class="alert alert-warning alert-sm">
                            <strong>Line ${issue.line}:</strong> ${issue.message}
                            <br><code class="small">${issue.code}</code>
                        </div>
                    `;
                });
                html += `</div>`;
            }

            // Suggestions
            if (file.suggestions && file.suggestions.length > 0) {
                html += `
                    <div class="col-md-6 mb-3">
                        <h6 class="text-info"><i class="fas fa-lightbulb me-2"></i>Suggestions</h6>
                `;
                file.suggestions.forEach(suggestion => {
                    html += `
                        <div class="alert alert-info alert-sm">
                            <strong>Line ${suggestion.line}:</strong> ${suggestion.message}
                            <br><code class="small">${suggestion.code}</code>
                        </div>
                    `;
                });
                html += `</div>`;
            }

            // Review comments
            if (file.comments && file.comments.length > 0) {
                html += `
                    <div class="col-12 mb-3">
                        <h6 class="text-primary"><i class="fas fa-comments me-2"></i>Review Comments</h6>
                `;
                file.comments.forEach(comment => {
                    html += `
                        <div class="alert alert-light border-primary">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-comment me-2 mt-1 text-primary"></i>
                                <div>
                                    <strong>Line ${comment.line}:</strong> ${comment.message}
                                    <br><code class="small">${comment.code}</code>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += `</div>`;
            }

            // File type recommendations
            if (file.file_type_recommendations && file.file_type_recommendations.length > 0) {
                html += `
                    <div class="col-12">
                        <h6 class="text-secondary"><i class="fas fa-file-alt me-2"></i>File Type Recommendations</h6>
                        <ul class="list-unstyled">
                `;
                file.file_type_recommendations.forEach(rec => {
                    html += `<li><i class="fas fa-check text-success me-2"></i>${rec}</li>`;
                });
                html += `
                        </ul>
                    </div>
                `;
            }

            html += `
                        </div>
                    </div>
                </div>
            `;
        });

        html += `</div>`;
    }

    // Add suggestions if any
    if (analysis.suggestions && analysis.suggestions.length > 0) {
        html += `
            <div class="mt-5">
                <h4><i class="fas fa-lightbulb me-2"></i>Improvement Suggestions</h4>
                <div class="row">
        `;
        
        analysis.suggestions.forEach((suggestion, index) => {
            html += `
                <div class="col-md-6 mb-3">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>${index + 1}. ${suggestion.title}</h6>
                        <p class="mb-0">${suggestion.reason}</p>
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
        `;
    }
    
    // Add reference links
    html += `
        <div class="mt-5 p-4 bg-light rounded">
            <h5><i class="fas fa-info-circle me-2"></i>Analysis Based On:</h5>
            <div class="row">
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <a href="https://cbea.ms/git-commit/" target="_blank" class="text-decoration-none">
                            Git Commit Guidelines
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <span>PR Best Practices</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <span>Industry Standards</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

function showSampleResults() {
    console.log('Showing sample results...');
    const sampleAnalysis = {
        score: 9,
        totalChecks: 10,
        percentage: 90,
        checks: [
            { name: "Title Length (≤50 chars)", passed: true, reason: "Title follows 50-character guideline" },
            { name: "Capitalized Title", passed: true, reason: "Title is properly capitalized" },
            { name: "No Period in Title", passed: true, reason: "Title correctly omits trailing period" },
            { name: "Imperative Mood", passed: true, reason: "Title uses imperative mood" },
            { name: "Meaningful Description", passed: true, reason: "PR has detailed description explaining context" },
            { name: "Branch Naming Convention", passed: false, reason: "Branch should follow format: TICKET-123_description" },
            { name: "Reasonable Scope", passed: true, reason: "PR has reasonable scope" },
            { name: "Manageable Size", passed: true, reason: "PR has manageable size" },
            { name: "Targets Main/Master", passed: true, reason: "Correctly targets main/master branch" },
            { name: "Testing Information", passed: true, reason: "Includes testing information" }
        ],
        suggestions: [
            { title: "Branch Naming Convention", reason: "Branch should follow format: TICKET-123_description" }
        ]
    };

    const container = document.getElementById('resultsContainer');
    container.innerHTML = `
        <div class="alert alert-info mb-4">
            <h6><i class="fas fa-info-circle me-2"></i>Sample Analysis Results</h6>
            <p class="mb-0">This is a sample analysis result. To see your own results, analyze a PR from the <a href="/review/new">New Review</a> page.</p>
        </div>
    `;

    displayResults(sampleAnalysis);
}

function showError(message) {
    const container = document.getElementById('resultsContainer');
    container.innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle text-warning fa-4x mb-3"></i>
            <h4>No Results Found</h4>
            <p class="text-muted">${message}</p>
            <div class="mt-4">
                <a href="/review/new" class="btn btn-primary me-2">
                    <i class="fas fa-plus me-2"></i>
                    Start New Analysis
                </a>
                <button type="button" class="btn btn-outline-primary" onclick="showSampleResults()">
                    <i class="fas fa-eye me-2"></i>
                    View Sample Results
                </button>
            </div>
        </div>
    `;
}

// Load results when page loads
document.addEventListener('DOMContentLoaded', loadAnalysisResults);
</script>
