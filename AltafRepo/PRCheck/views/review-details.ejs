<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-file-alt me-2"></i>Review Details</h2>
                    <p class="text-muted mb-0">Detailed analysis for PR #<%= review.prNumber %></p>
                </div>
                <div>
                    <a href="/dashboard" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Dashboard
                    </a>
                    <button type="button" class="btn btn-primary" onclick="viewFullResults()">
                        <i class="fas fa-chart-line me-2"></i>
                        View Full Results
                    </button>
                </div>
            </div>

            <!-- PR Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fab fa-github me-2"></i>Pull Request Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6><%= review.prData.title %></h6>
                            <p class="text-muted mb-2">
                                <strong>Repository:</strong> <%= review.repoUrl.replace('https://github.com/', '') %>
                            </p>
                            <p class="text-muted mb-2">
                                <strong>PR Number:</strong> #<%= review.prNumber %>
                            </p>
                            <p class="text-muted mb-2">
                                <strong>Author:</strong> <%= review.prData.user %>
                            </p>
                            <p class="text-muted mb-2">
                                <strong>Created:</strong> <%= new Date(review.prData.created_at).toLocaleDateString() %>
                            </p>
                            <p class="text-muted mb-0">
                                <strong>State:</strong> 
                                <span class="badge bg-<%= review.prData.state === 'open' ? 'success' : 'secondary' %>">
                                    <%= review.prData.state.toUpperCase() %>
                                </span>
                            </p>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="display-4 text-<%= review.analysis.percentage >= 80 ? 'success' : review.analysis.percentage >= 60 ? 'warning' : 'danger' %> mb-2">
                                <%= review.analysis.percentage %>%
                            </div>
                            <h6>Overall Score</h6>
                            <p class="text-muted"><%= review.analysis.score %>/<%= review.analysis.totalChecks %> checks passed</p>
                            <a href="<%= review.prData.html_url %>" target="_blank" class="btn btn-outline-primary">
                                <i class="fab fa-github me-2"></i>
                                View on GitHub
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Analysis Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="h4 text-success"><%= review.analysis.checks.filter(c => c.passed).length %></div>
                            <p class="text-muted">Passed Checks</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h4 text-danger"><%= review.analysis.checks.filter(c => !c.passed).length %></div>
                            <p class="text-muted">Failed Checks</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h4 text-info"><%= review.prData.changed_files || 0 %></div>
                            <p class="text-muted">Files Changed</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h4 text-warning"><%= review.prData.additions || 0 %></div>
                            <p class="text-muted">Lines Added</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Analysis Results -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list-check me-2"></i>Quick Analysis Results</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <% review.analysis.checks.forEach(check => { %>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-<%= check.passed ? 'check-circle text-success' : 'times-circle text-danger' %> me-3 mt-1"></i>
                                <div>
                                    <h6 class="mb-1"><%= check.name %></h6>
                                    <p class="text-muted small mb-0"><%= check.reason %></p>
                                </div>
                            </div>
                        </div>
                        <% }); %>
                    </div>
                </div>
            </div>

            <!-- Improvement Suggestions -->
            <% if (review.analysis.suggestions && review.analysis.suggestions.length > 0) { %>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Improvement Suggestions</h5>
                </div>
                <div class="card-body">
                    <% review.analysis.suggestions.forEach((suggestion, index) => { %>
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i><%= index + 1 %>. <%= suggestion.title %></h6>
                        <p class="mb-0"><%= suggestion.reason %></p>
                    </div>
                    <% }); %>
                </div>
            </div>
            <% } %>

            <!-- PR Description -->
            <% if (review.prData.body && review.prData.body.trim()) { %>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-file-text me-2"></i>PR Description</h5>
                </div>
                <div class="card-body">
                    <div class="bg-light p-3 rounded">
                        <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit;"><%= review.prData.body %></pre>
                    </div>
                </div>
            </div>
            <% } %>

            <!-- Analysis Metadata -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Analysis Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2"><strong>Analysis Date:</strong> <%= new Date(review.createdAt).toLocaleString() %></p>
                            <p class="mb-2"><strong>Review ID:</strong> <%= review.id %></p>
                            <p class="mb-0"><strong>Analysis Version:</strong> Enhanced 10-Point Analysis</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Analysis Based On:</h6>
                            <ul class="list-unstyled mb-0">
                                <li><i class="fas fa-check text-success me-2"></i><a href="https://cbea.ms/git-commit/" target="_blank">Git Commit Guidelines</a></li>
                                <li><i class="fas fa-check text-success me-2"></i>PR Best Practices</li>
                                <li><i class="fas fa-check text-success me-2"></i>Industry Standards</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="mt-4 text-center">
                <a href="/review/new" class="btn btn-success me-2">
                    <i class="fas fa-plus me-2"></i>
                    Analyze Another PR
                </a>
                <button type="button" class="btn btn-primary me-2" onclick="viewFullResults()">
                    <i class="fas fa-chart-line me-2"></i>
                    View Full Results Page
                </button>
                <a href="<%= review.prData.html_url %>" target="_blank" class="btn btn-outline-primary">
                    <i class="fab fa-github me-2"></i>
                    Open in GitHub
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function viewFullResults() {
    // Store analysis data in localStorage
    localStorage.setItem('lastAnalysis', JSON.stringify(<%= JSON.stringify(review.analysis) %>));
    
    // Open results page
    window.open('/review/results', '_blank');
}
</script>
