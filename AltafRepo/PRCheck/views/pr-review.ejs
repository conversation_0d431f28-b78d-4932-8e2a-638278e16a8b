<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fab fa-github me-2"></i>
                PR Review Assistant
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- PR Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">
                                <span class="badge bg-<%= pr.state === 'open' ? 'success' : pr.state === 'closed' ? 'danger' : 'secondary' %>">
                                    <%= pr.state.toUpperCase() %>
                                </span>
                                <%= pr.title %>
                                <small class="text-muted">#<%= pr.number %></small>
                            </h4>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshAnalysis()">
                                <i class="fas fa-refresh me-1"></i>
                                Refresh Analysis
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-magic me-1"></i>
                                    AI Actions
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="improvePR('title')">Improve Title</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="improvePR('description')">Improve Description</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="generateReview()">Generate Review</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="autoApplyImprovements()">Auto-Apply All</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="pr-description">
                                    <% if (pr.body) { %>
                                        <div class="markdown-content">
                                            <%- pr.body.replace(/\n/g, '<br>') %>
                                        </div>
                                    <% } else { %>
                                        <em class="text-muted">No description provided</em>
                                    <% } %>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="pr-stats">
                                    <div class="stat-item">
                                        <i class="fas fa-file-code text-primary"></i>
                                        <span><%= pr.changed_files %> files changed</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-plus text-success"></i>
                                        <span><%= pr.additions %> additions</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-minus text-danger"></i>
                                        <span><%= pr.deletions %> deletions</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-git-alt text-info"></i>
                                        <span><%= pr.commits.length %> commits</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Checklist Results -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list-check me-2"></i>
                            Checklist Analysis
                        </h5>
                        <div class="score-badge">
                            <span class="badge bg-<%= checklistResults.weightedScore >= 80 ? 'success' : checklistResults.weightedScore >= 60 ? 'warning' : 'danger' %> fs-6">
                                Score: <%= checklistResults.weightedScore %>%
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3">
                            <div class="progress-bar bg-<%= checklistResults.weightedScore >= 80 ? 'success' : checklistResults.weightedScore >= 60 ? 'warning' : 'danger' %>" 
                                 style="width: <%= checklistResults.weightedScore %>%">
                                <%= checklistResults.score %>/<%= checklistResults.totalChecks %> checks passed
                            </div>
                        </div>
                        
                        <div class="checklist-items">
                            <% checklistResults.checks.forEach(check => { %>
                                <div class="checklist-item <%= check.passed ? 'passed' : 'failed' %>">
                                    <div class="d-flex align-items-start">
                                        <div class="check-icon me-3">
                                            <i class="fas fa-<%= check.passed ? 'check-circle text-success' : 'times-circle text-danger' %>"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><%= check.name %></h6>
                                            <p class="text-muted mb-1"><%= check.description %></p>
                                            <% if (!check.passed && check.reason) { %>
                                                <div class="alert alert-warning alert-sm mb-0">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    <%= check.reason %>
                                                </div>
                                            <% } %>
                                        </div>
                                        <div class="check-weight">
                                            <span class="badge bg-secondary">Weight: <%= check.weight %></span>
                                        </div>
                                    </div>
                                </div>
                            <% }); %>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Suggestions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-robot me-2"></i>
                            AI-Powered Suggestions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <% checklistResults.suggestions.forEach(suggestion => { %>
                                <div class="col-md-4 mb-3">
                                    <div class="suggestion-card priority-<%= suggestion.priority %>">
                                        <h6 class="suggestion-title">
                                            <i class="fas fa-<%= suggestion.priority === 'high' ? 'exclamation-circle' : suggestion.priority === 'medium' ? 'exclamation-triangle' : 'info-circle' %>"></i>
                                            <%= suggestion.title %>
                                        </h6>
                                        <ul class="suggestion-list">
                                            <% suggestion.items.forEach(item => { %>
                                                <li>
                                                    <strong><%= item.name %>:</strong>
                                                    <span class="text-muted"><%= item.reason %></span>
                                                    <div class="action-text">
                                                        <i class="fas fa-arrow-right me-1"></i>
                                                        <%= item.action %>
                                                    </div>
                                                </li>
                                            <% }); %>
                                        </ul>
                                    </div>
                                </div>
                            <% }); %>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Improvement Actions -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            Quick Improvements
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="improvePR('title', false)">
                                <i class="fas fa-heading me-2"></i>
                                Suggest Better Title
                            </button>
                            <button class="btn btn-outline-primary" onclick="improvePR('description', false)">
                                <i class="fas fa-file-text me-2"></i>
                                Improve Description
                            </button>
                            <button class="btn btn-outline-success" onclick="generateReview('COMMENT')">
                                <i class="fas fa-comment me-2"></i>
                                Generate Review Comment
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Auto-Apply Options
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="improvePR('description', true)">
                                <i class="fas fa-magic me-2"></i>
                                Auto-Update Description
                            </button>
                            <button class="btn btn-success" onclick="generateReview('COMMENT', true)">
                                <i class="fas fa-robot me-2"></i>
                                Auto-Post AI Review
                            </button>
                            <button class="btn btn-warning" onclick="autoApplyImprovements()">
                                <i class="fas fa-wand-magic-sparkles me-2"></i>
                                Apply All Improvements
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Display -->
        <div id="improvementResults" class="row" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-magic me-2"></i>
                            AI Improvement Results
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="improvementContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    <script src="/js/app.js"></script>
    <script src="/js/pr-review.js"></script>
    <script>
        // Initialize PR review page
        const prData = {
            owner: '<%= pr.head.repo.owner.login %>',
            repo: '<%= pr.head.repo.name %>',
            number: <%= pr.number %>
        };
        
        initializePRReview(prData);
    </script>
</body>
</html>
