[{"id": "252a4da2717b78dec196dad4e2dbd8c5", "userId": null, "repoUrl": "https://github.com/hashicorp/terraform-guides", "prNumber": 332, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 4, "totalChecks": 10, "percentage": 40, "status": "poor"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 2, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": false, "message": "Title should start with a capital letter", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": false, "message": "Title should use imperative mood (e.g., \"Fix bug\" not \"Fixed bug\")", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 2, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": false, "message": "Too many files changed. Consider breaking into smaller PRs", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": false, "message": "Too many additions. Consider breaking into smaller PRs", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": false, "message": "Should include information about how changes were tested", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 30, "filesAnalyzed": 30, "files": [{"filename": "governance/first-generation/aws/aws-vpcs-must-have-tags-and-enable-dns-hostnames.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-ami-owners.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-mandatory-tags.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-tag-from-data-source.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/must_have_remote_exec_provisioner.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/openshift-aws-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/require-private-acl-and-kms-for-s3-buckets.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/require-vpc-and-kms-for-lambda-functions.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-availability-zones.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-cidr-blocks.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-instance-type.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-region.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-iam-policy-statement.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-iam-policy-statement2.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/sentinel.hcl", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/acs-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/aks-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/block-allow-all-cidr.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/enforce-mandatory-tags.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-current-azure-vms.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-image-id.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-publisher.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-size.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/cloud-agnostic/destroy-limit.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account.sh", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account.tf", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account_balance.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/block-allow-all-cidr.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/enforce-mandatory-labels.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/gke-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 90, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["📁 Consider breaking large changes into smaller, focused PRs"], "metadata": {"repository": "hashicorp/terraform-guides", "prNumber": "332", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T19:55:35.537Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T19:55:35.537Z"}, {"id": "384cd9696c2c80c770c9b9706591f4aa", "userId": null, "repoUrl": "https://github.com/hashicorp/terraform-guides", "prNumber": 332, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 4, "totalChecks": 10, "percentage": 40, "status": "poor"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 2, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": false, "message": "Title should start with a capital letter", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": false, "message": "Title should use imperative mood (e.g., \"Fix bug\" not \"Fixed bug\")", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 2, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": false, "message": "Too many files changed. Consider breaking into smaller PRs", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": false, "message": "Too many additions. Consider breaking into smaller PRs", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": false, "message": "Should include information about how changes were tested", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 30, "filesAnalyzed": 30, "files": [{"filename": "governance/first-generation/aws/aws-vpcs-must-have-tags-and-enable-dns-hostnames.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-ami-owners.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-mandatory-tags.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-tag-from-data-source.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/must_have_remote_exec_provisioner.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/openshift-aws-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/require-private-acl-and-kms-for-s3-buckets.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/require-vpc-and-kms-for-lambda-functions.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-availability-zones.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-cidr-blocks.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-instance-type.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-region.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-iam-policy-statement.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-iam-policy-statement2.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/sentinel.hcl", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/acs-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/aks-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/block-allow-all-cidr.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/enforce-mandatory-tags.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-current-azure-vms.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-image-id.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-publisher.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-size.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/cloud-agnostic/destroy-limit.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account.sh", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account.tf", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account_balance.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/block-allow-all-cidr.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/enforce-mandatory-labels.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/gke-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 90, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["📁 Consider breaking large changes into smaller, focused PRs"], "metadata": {"repository": "hashicorp/terraform-guides", "prNumber": "332", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T19:55:00.607Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T19:55:00.607Z"}, {"id": "6e0e78c0cf0e0553a72e455891dcaa4c", "userId": null, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T19:52:50.073Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T19:52:50.074Z"}]