[{"id": "28496ac4999b1014fdcd533adfdd1a31", "userId": 148588427, "repoUrl": "https://github.com/v-altaf-syed_sflyinc/aws-path-exercise", "prNumber": 6, "analysisType": "private", "checklistId": "code_quality", "analysis": {"summary": {"score": 0, "totalChecks": 3, "percentage": 0, "status": "poor"}, "categories": {"code_standards": {"id": "code_standards", "name": "Code Standards", "description": "Code quality and maintainability checks", "totalChecks": 3, "passedChecks": 0, "checks": [{"id": "no_debug_code", "name": "No Debug Code", "description": "Code should not contain console.log or debugger statements", "passed": false, "message": "Remove console.log and debugger statements before merging", "severity": "medium", "details": {}}, {"id": "proper_error_handling", "name": "Proper <PERSON><PERSON><PERSON>", "description": "Code should have appropriate error handling", "passed": false, "message": "Add proper error handling for robustness", "severity": "high", "details": {}}, {"id": "code_documentation", "name": "Code Documentation", "description": "Functions and classes should be documented", "passed": false, "message": "Add documentation for functions and classes", "severity": "medium", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test", "status": "added", "additions": 1, "deletions": 0, "changes": 1, "totalLines": 1, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 1, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": [], "metadata": {"repository": "v-altaf-syed_sflyinc/aws-path-exercise", "prNumber": "6", "analysisType": "private", "checklistId": "code_quality", "analyzedAt": "2025-06-26T08:49:28.004Z", "analyzedBy": "v-altaf-syed_sflyinc"}}, "createdAt": "2025-06-26T08:49:28.005Z"}, {"id": "3185fcd33d8fb4f4efd76cad00c49116", "userId": 148588427, "repoUrl": "https://github.com/v-altaf-syed_sflyinc/aws-path-exercise", "prNumber": 6, "analysisType": "private", "checklistId": "default", "analysis": {"summary": {"score": 7, "totalChecks": 10, "percentage": 70, "status": "needs_improvement"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 3, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": false, "message": "PR should include detailed description explaining WHY (>50 characters)", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": false, "message": "Should include information about how changes were tested", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test", "status": "added", "additions": 1, "deletions": 0, "changes": 1, "totalLines": 1, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 1, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": [], "metadata": {"repository": "v-altaf-syed_sflyinc/aws-path-exercise", "prNumber": "6", "analysisType": "private", "checklistId": "default", "analyzedAt": "2025-06-26T08:37:53.873Z", "analyzedBy": "v-altaf-syed_sflyinc"}}, "createdAt": "2025-06-26T08:37:53.874Z"}, {"id": "d181e51ff2016ef0bffd7bcadd5bb3ab", "userId": 148588427, "repoUrl": "https://github.com/facebook/react", "prNumber": 456, "analysisType": "private", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "facebook/react", "prNumber": "456", "analysisType": "private", "checklistId": "default", "analyzedAt": "2025-06-26T08:22:01.543Z", "analyzedBy": "v-altaf-syed_sflyinc", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-26T08:22:01.543Z"}, {"id": "508b0e4d58c42ab15e105a7d34cd0250", "userId": 148588427, "repoUrl": "https://github.com/sflyinc-shutterfly/tf_marketing_platform", "prNumber": 275, "analysisType": "private", "checklistId": "code_quality", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "sflyinc-shutterfly/tf_marketing_platform", "prNumber": "275", "analysisType": "private", "checklistId": "code_quality", "analyzedAt": "2025-06-26T08:21:26.082Z", "analyzedBy": "v-altaf-syed_sflyinc", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-26T08:21:26.082Z"}, {"id": "b2a3d33ffa3a551b00144d976ceedd17", "userId": 148588427, "repoUrl": "https://github.com/sflyinc-shutterfly/tf_marketing_platform", "prNumber": 275, "analysisType": "private", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "sflyinc-shutterfly/tf_marketing_platform", "prNumber": "275", "analysisType": "private", "checklistId": "default", "analyzedAt": "2025-06-26T08:19:55.198Z", "analyzedBy": "v-altaf-syed_sflyinc", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-26T08:19:55.198Z"}, {"id": "6ba8df0ead11ca2f41a5f34ba45872f1", "userId": 148588427, "repoUrl": "https://github.com/sflyinc-shutterfly/tf_live_infra_sfly-aws-marketing-preprod", "prNumber": 663, "analysisType": "private", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "sflyinc-shutterfly/tf_live_infra_sfly-aws-marketing-preprod", "prNumber": "663", "analysisType": "private", "checklistId": "default", "analyzedAt": "2025-06-25T21:11:00.738Z", "analyzedBy": "v-altaf-syed_sflyinc", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T21:11:00.739Z"}, {"id": "d5d3136f2bd001435153b2a778f60e78", "userId": 148588427, "repoUrl": "https://github.com/v-altaf-syed_sflyinc/aws-path-exercise", "prNumber": 123, "analysisType": "private", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "v-altaf-syed_sflyinc/aws-path-exercise", "prNumber": "123", "analysisType": "private", "checklistId": "default", "analyzedAt": "2025-06-25T21:07:32.866Z", "analyzedBy": "v-altaf-syed_sflyinc", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T21:07:32.866Z"}, {"id": "90b5f706b8c8d655d40cce31a0ee2830", "userId": null, "repoUrl": "https://github.com/microsoft/vscode", "prNumber": 123, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "microsoft/vscode", "prNumber": "123", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:48:29.014Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:48:29.014Z"}, {"id": "870fe39522dc99b50a9a71d13f02b8e3", "userId": null, "repoUrl": "https://github.com/sflyinc-shutterfly/tf_live_infra_sfly-aws-marketing-preprod", "prNumber": 663, "analysisType": "manual", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "sflyinc-shutterfly/tf_live_infra_sfly-aws-marketing-preprod", "prNumber": "663", "analysisType": "manual", "checklistId": "default", "analyzedAt": "2025-06-25T20:43:52.012Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:43:52.012Z"}, {"id": "e7d33d1e987967ba34db88d7a6e28224", "userId": 87066855, "repoUrl": "https://github.com/facebook/react", "prNumber": 1234, "analysisType": "manual", "checklistId": "default", "analysis": {"summary": {"score": 5, "totalChecks": 10, "percentage": 50, "status": "poor"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 2, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": false, "message": "Title should not end with a period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": false, "message": "Title should use imperative mood (e.g., \"Fix bug\" not \"Fixed bug\")", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 3, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": false, "message": "PR should include detailed description explaining WHY (>50 characters)", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": false, "message": "Should include information about how changes were tested", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 6, "filesAnalyzed": 6, "files": [{"filename": "Gruntfile.js", "status": "modified", "additions": 18, "deletions": 18, "changes": 36, "totalLines": 18, "linesWithIssues": 1, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [{"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 5, "file": "Gruntfile.js", "code": "var bundleTask = require('./grunt/tasks/bundle');"}], "suggestions": []}, "codeQuality": {"complexity": "medium", "maintainability": "good", "security": "safe"}, "recommendations": ["<PERSON><PERSON> comments", "Consider using TypeScript for better type safety"]}, {"filename": "grunt/config/bundle.js", "status": "renamed", "additions": 58, "deletions": 48, "changes": 106, "totalLines": 58, "linesWithIssues": 5, "lineComments": [{"type": "documentation", "message": "Consider adding documentation for this function", "suggestion": "Add JSDoc comments or docstrings to explain the function's purpose, parameters, and return value", "line": 58, "file": "grunt/config/bundle.js", "code": "function override(obj1, obj2) {"}], "issues": {"critical": [], "major": [], "minor": [{"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 6, "file": "grunt/config/bundle.js", "code": "var deamdify = require('deamdify');"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 77, "file": "grunt/config/bundle.js", "code": "var min = override(basic, {"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 109, "file": "grunt/config/bundle.js", "code": "var addonsMin = override(addons, {"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 118, "file": "grunt/config/bundle.js", "code": "var withCodeCoverageLogging = override(basic, {"}], "suggestions": []}, "codeQuality": {"complexity": "medium", "maintainability": "good", "security": "safe"}, "recommendations": ["<PERSON><PERSON> comments", "Consider using TypeScript for better type safety"]}, {"filename": "grunt/tasks/browserify.js", "status": "removed", "additions": 0, "deletions": 68, "changes": 68, "totalLines": 0, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "grunt/tasks/bundle.js", "status": "added", "additions": 38, "deletions": 0, "changes": 38, "totalLines": 38, "linesWithIssues": 6, "lineComments": [{"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 13, "file": "grunt/tasks/bundle.js", "code": "var done = this.async();"}], "issues": {"critical": [], "major": [], "minor": [{"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 3, "file": "grunt/tasks/bundle.js", "code": "var cjs = require('pure-cjs');"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 4, "file": "grunt/tasks/bundle.js", "code": "var grunt = require('grunt');"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 7, "file": "grunt/tasks/bundle.js", "code": "var config = this.options({"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 13, "file": "grunt/tasks/bundle.js", "code": "var done = this.async();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 16, "file": "grunt/tasks/bundle.js", "code": "var options = {"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 26, "file": "grunt/tasks/bundle.js", "code": "var _this = this;"}], "suggestions": []}, "codeQuality": {"complexity": "medium", "maintainability": "fair", "security": "safe"}, "recommendations": ["<PERSON><PERSON> comments", "Consider using TypeScript for better type safety"]}, {"filename": "package.json", "status": "modified", "additions": 2, "deletions": 1, "changes": 3, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "vendor/browser-transforms.js", "status": "modified", "additions": 1, "deletions": 2, "changes": 3, "totalLines": 1, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "medium", "maintainability": "good", "security": "safe"}, "recommendations": ["<PERSON><PERSON> comments", "Consider using TypeScript for better type safety"]}]}, "lineByLineReview": {"totalLines": 117, "linesWithIssues": 12, "comments": [{"type": "documentation", "message": "Consider adding documentation for this function", "suggestion": "Add JSDoc comments or docstrings to explain the function's purpose, parameters, and return value", "line": 58, "file": "grunt/config/bundle.js", "code": "function override(obj1, obj2) {"}, {"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 13, "file": "grunt/tasks/bundle.js", "code": "var done = this.async();"}]}, "issues": {"critical": [], "major": [], "minor": [{"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 5, "file": "Gruntfile.js", "code": "var bundleTask = require('./grunt/tasks/bundle');"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 6, "file": "grunt/config/bundle.js", "code": "var deamdify = require('deamdify');"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 77, "file": "grunt/config/bundle.js", "code": "var min = override(basic, {"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 109, "file": "grunt/config/bundle.js", "code": "var addonsMin = override(addons, {"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 118, "file": "grunt/config/bundle.js", "code": "var withCodeCoverageLogging = override(basic, {"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 3, "file": "grunt/tasks/bundle.js", "code": "var cjs = require('pure-cjs');"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 4, "file": "grunt/tasks/bundle.js", "code": "var grunt = require('grunt');"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 7, "file": "grunt/tasks/bundle.js", "code": "var config = this.options({"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 13, "file": "grunt/tasks/bundle.js", "code": "var done = this.async();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 16, "file": "grunt/tasks/bundle.js", "code": "var options = {"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 26, "file": "grunt/tasks/bundle.js", "code": "var _this = this;"}], "suggestions": []}, "recommendations": ["🧹 Multiple minor issues found - consider code cleanup"], "metadata": {"repository": "facebook/react", "prNumber": "1234", "analysisType": "manual", "checklistId": "default", "analyzedAt": "2025-06-25T20:37:18.167Z", "analyzedBy": "saltaf07"}}, "createdAt": "2025-06-25T20:37:18.167Z"}, {"id": "fc591bc465d4599f03d992401e352f68", "userId": 87066855, "repoUrl": "https://github.com/hashicorp/terraform", "prNumber": 123, "analysisType": "private", "checklistId": "254f58475325f5b8", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "hashicorp/terraform", "prNumber": "123", "analysisType": "private", "checklistId": "254f58475325f5b8", "analyzedAt": "2025-06-25T20:36:59.833Z", "analyzedBy": "saltaf07", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:36:59.834Z"}, {"id": "fdf6b84c16877c583952fb607943ee7f", "userId": 87066855, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "private", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "private", "checklistId": "default", "analyzedAt": "2025-06-25T20:36:03.461Z", "analyzedBy": "saltaf07"}}, "createdAt": "2025-06-25T20:36:03.462Z"}, {"id": "cedceaba54a49d81b4ba270415c09801", "userId": null, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:33:37.011Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T20:33:37.011Z"}, {"id": "8c95f0ffc70695ab0994466b64df4e48", "userId": null, "repoUrl": "https://github.com/facebook/react", "prNumber": 123, "analysisType": "private", "checklistId": "default", "analysis": {"summary": {"score": 7, "totalChecks": 10, "percentage": 70, "status": "needs_improvement"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 3, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": false, "message": "PR should include detailed description explaining WHY (>50 characters)", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": false, "message": "Should include information about how changes were tested", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "docs/docs/getting-started.md", "status": "modified", "additions": 4, "deletions": 0, "changes": 4, "totalLines": 4, "linesWithIssues": 1, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [{"type": "formatting", "severity": "minor", "message": "Line too long (346 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 93, "file": "docs/docs/getting-started.md", "code": "> The comment parser is very strict right now, in order for it to pick up the `@jsx` modifier, two conditions are required. The `@jsx` comment block must be the first comment on the file. The comment must start with `/**` (`/*` and `//` will not work). If the parser can't find the `@jsx` comment, it will output the file without transforming it."}], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 4, "linesWithIssues": 1, "comments": []}, "issues": {"critical": [], "major": [], "minor": [{"type": "formatting", "severity": "minor", "message": "Line too long (346 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 93, "file": "docs/docs/getting-started.md", "code": "> The comment parser is very strict right now, in order for it to pick up the `@jsx` modifier, two conditions are required. The `@jsx` comment block must be the first comment on the file. The comment must start with `/**` (`/*` and `//` will not work). If the parser can't find the `@jsx` comment, it will output the file without transforming it."}], "suggestions": []}, "recommendations": [], "metadata": {"repository": "facebook/react", "prNumber": "123", "analysisType": "private", "checklistId": "default", "analyzedAt": "2025-06-25T20:15:52.030Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T20:15:52.030Z"}, {"id": "5242aa46701afae1bf1bb4984893ddd8", "userId": 87066855, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "private", "checklistId": "security_focused", "analysis": {"summary": {"score": 0, "totalChecks": 3, "percentage": 0, "status": "poor"}, "categories": {"security_checks": {"id": "security_checks", "name": "Security Validation", "description": "Security-focused code review", "totalChecks": 3, "passedChecks": 0, "checks": [{"id": "no_hardcoded_secrets", "name": "No Hardcoded Secrets", "description": "Code should not contain hardcoded passwords, API keys, or secrets", "passed": false, "message": "Hardcoded secrets detected. Use environment variables or secure storage", "severity": "critical", "details": {}}, {"id": "input_validation", "name": "Input Validation", "description": "User inputs should be properly validated", "passed": false, "message": "Ensure all user inputs are properly validated and sanitized", "severity": "high", "details": {}}, {"id": "sql_injection_prevention", "name": "SQL Injection Prevention", "description": "Database queries should use parameterized statements", "passed": false, "message": "Potential SQL injection vulnerability. Use parameterized queries", "severity": "critical", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": [], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "private", "checklistId": "security_focused", "analyzedAt": "2025-06-25T20:14:18.144Z", "analyzedBy": "saltaf07"}}, "createdAt": "2025-06-25T20:14:18.145Z"}, {"id": "c6a742053608908fcfd92be8759668c7", "userId": 87066855, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "public", "checklistId": "code_quality", "analysis": {"summary": {"score": 0, "totalChecks": 3, "percentage": 0, "status": "poor"}, "categories": {"code_standards": {"id": "code_standards", "name": "Code Standards", "description": "Code quality and maintainability checks", "totalChecks": 3, "passedChecks": 0, "checks": [{"id": "no_debug_code", "name": "No Debug Code", "description": "Code should not contain console.log or debugger statements", "passed": false, "message": "Remove console.log and debugger statements before merging", "severity": "medium", "details": {}}, {"id": "proper_error_handling", "name": "Proper <PERSON><PERSON><PERSON>", "description": "Code should have appropriate error handling", "passed": false, "message": "Add proper error handling for robustness", "severity": "high", "details": {}}, {"id": "code_documentation", "name": "Code Documentation", "description": "Functions and classes should be documented", "passed": false, "message": "Add documentation for functions and classes", "severity": "medium", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": [], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "public", "checklistId": "code_quality", "analyzedAt": "2025-06-25T20:13:35.317Z", "analyzedBy": "saltaf07"}}, "createdAt": "2025-06-25T20:13:35.317Z"}, {"id": "14735fb7deac180bdbca418e0c22176e", "userId": 87066855, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "private", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "private", "checklistId": "default", "analyzedAt": "2025-06-25T20:11:38.352Z", "analyzedBy": "saltaf07"}}, "createdAt": "2025-06-25T20:11:38.353Z"}, {"id": "d6b1ef0ca3d1ddd9e88c7f11afeb0655", "userId": null, "repoUrl": "https://github.com/github/KustoSchemaTools", "prNumber": 106, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 3, "totalChecks": 10, "percentage": 30, "status": "poor"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 2, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": false, "message": "Title should start with a capital letter", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": false, "message": "Title should use imperative mood (e.g., \"Fix bug\" not \"Fixed bug\")", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 1, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": false, "message": "PR should include detailed description explaining WHY (>50 characters)", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": false, "message": "Too many files changed. Consider breaking into smaller PRs", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": false, "message": "Too many additions. Consider breaking into smaller PRs", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": false, "message": "Should include information about how changes were tested", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 18, "filesAnalyzed": 18, "files": [{"filename": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "status": "added", "additions": 156, "deletions": 0, "changes": 156, "totalLines": 156, "linesWithIssues": 26, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [{"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 24, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "var oldCluster = CreateClusterWithPolicy(0.2, 1, 2, 3);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 25, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "var newCluster = CreateClusterWithPolicy(0.2, 1, 2, 3);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 28, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "var changes = ClusterChanges.GenerateChanges(oldCluster, newCluster, _loggerMock.Object);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 39, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var oldCluster = CreateClusterWithPolicy(totalCapacity: 1000, coreUtilization: 0.75);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 40, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var newCluster = CreateClusterWithPolicy(totalCapacity: 1200, coreUtilization: 0.75);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 43, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var changes = ClusterChanges.GenerateChanges(oldCluster, newCluster, _loggerMock.Object);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 49, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var policyChange = Assert.Single(changes.PolicyChanges);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 52, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var capacityChange = policyChange.Value;"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 53, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var propertyChange = Assert.Single(capacityChange.PropertyChanges);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 59, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var expectedJson = JsonConvert.SerializeObject(newCluster.CapacityPolicy, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });"}, {"type": "formatting", "severity": "minor", "message": "Line too long (158 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 59, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var expectedJson = JsonConvert.SerializeObject(newCluster.CapacityPolicy, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 60, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var expectedScript = $\".alter-merge cluster policy capacity @'{expectedJson}'\";"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 68, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var oldCluster = CreateClusterWithPolicy(totalCapacity: 1000, coreUtilization: 0.75);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 69, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var newCluster = CreateClusterWithPolicy(totalCapacity: 1200, coreUtilization: 0.90);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 72, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var changes = ClusterChanges.GenerateChanges(oldCluster, newCluster, _loggerMock.Object);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 75, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var policyChange = Assert.Single(changes.PolicyChanges);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 76, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var capacityChange = policyChange.Value;"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 79, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var capacityPropChange = capacityChange.PropertyChanges.Single(p => p.PropertyName == \"TotalCapacity\");"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 79, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var capacityPropChange = capacityChange.PropertyChanges.Single(p => p.PropertyName == \"TotalCapacity\");"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 80, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var coreUtilPropChange = capacityChange.PropertyChanges.Single(p => p.PropertyName == \"CoreUtilizationCoefficient\");"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 80, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var coreUtilPropChange = capacityChange.PropertyChanges.Single(p => p.PropertyName == \"CoreUtilizationCoefficient\");"}, {"type": "formatting", "severity": "minor", "message": "Line too long (123 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 80, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var coreUtilPropChange = capacityChange.PropertyChanges.Single(p => p.PropertyName == \"CoreUtilizationCoefficient\");"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 93, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var oldCluster = CreateClusterWithPolicy(totalCapacity: 1000);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 94, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var newCluster = new Cluster(); // No capacity policy defined"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 97, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var changes = ClusterChanges.GenerateChanges(oldCluster, newCluster, _loggerMock.Object);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 109, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var oldCluster = new Cluster { CapacityPolicy = new CapacityPolicy() };"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 111, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var newCluster = CreateClusterWithPolicy(coreUtilization: 0.95);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 114, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var changes = ClusterChanges.GenerateChanges(oldCluster, newCluster, _loggerMock.Object);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 117, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var policyChange = Assert.Single(changes.PolicyChanges).Value;"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 118, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var propertyChange = Assert.Single(policyChange.PropertyChanges);"}], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "poor", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools.Tests/DemoData/ClusterScopedChanges/ComprehensiveCapacityPolicy/clusters.yml", "status": "added", "additions": 37, "deletions": 0, "changes": 37, "totalLines": 37, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools.Tests/DemoData/ClusterScopedChanges/MultipleClusters/clusters.yml", "status": "added", "additions": 16, "deletions": 0, "changes": 16, "totalLines": 16, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools.Tests/DemoData/ClusterScopedChanges/PartialCapacityPolicy/clusters.yml", "status": "added", "additions": 13, "deletions": 0, "changes": 13, "totalLines": 13, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools.Tests/Parser/YamlDatabaseHandlerTests.cs", "status": "renamed", "additions": 0, "deletions": 0, "changes": 0, "totalLines": 0, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/Changes/ClusterChange.cs", "status": "added", "additions": 9, "deletions": 0, "changes": 9, "totalLines": 9, "linesWithIssues": 1, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [{"type": "quality", "severity": "minor", "message": "TODO/FIXME comment found", "suggestion": "Address TODO items or create proper issue tracking", "line": 7, "file": "KustoSchemaTools/Changes/ClusterChange.cs", "code": "// TODO: Future extensibility for workload groups can be added here"}], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/Changes/ClusterChanges.cs", "status": "added", "additions": 62, "deletions": 0, "changes": 62, "totalLines": 62, "linesWithIssues": 8, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [{"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 16, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var clusterName = oldCluster.Name;"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 17, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var clusterChange = new ClusterChange"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 26, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var capacityPolicyChange = new PolicyChange();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 27, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var newPolicyProps = newCluster.CapacityPolicy.GetType().GetProperties()"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 30, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "foreach (var prop in newPolicyProps)"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 32, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var newValue = prop.GetValue(newCluster.CapacityPolicy);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 33, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var oldValue = prop.GetValue(oldCluster.CapacityPolicy);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 49, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var newPolicyJson = JsonConvert.SerializeObject(newCluster.CapacityPolicy, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });"}, {"type": "formatting", "severity": "minor", "message": "Line too long (152 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 49, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var newPolicyJson = JsonConvert.SerializeObject(newCluster.CapacityPolicy, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });"}], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "fair", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/Changes/PolicyChange.cs", "status": "added", "additions": 10, "deletions": 0, "changes": 10, "totalLines": 10, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/Changes/PropertyChange.cs", "status": "added", "additions": 9, "deletions": 0, "changes": 9, "totalLines": 9, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/Helpers/Serialization.cs", "status": "modified", "additions": 1, "deletions": 0, "changes": 1, "totalLines": 1, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/KustoClusterOrchestrator.cs", "status": "added", "additions": 57, "deletions": 0, "changes": 57, "totalLines": 57, "linesWithIssues": 11, "lineComments": [{"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 31, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "public async Task<List<ClusterChange>> GenerateChangesAsync(string path)"}, {"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 43, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var yamlCluster = await yamlHandler.LoadAsync();"}, {"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 47, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var kustoCluster = await kustoHandler.LoadAsync();"}], "issues": {"critical": [], "major": [], "minor": [{"type": "formatting", "severity": "minor", "message": "Line too long (181 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 14, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "public KustoClusterOrchestrator(ILogger<KustoClusterOrchestrator> logger, YamlClusterHandlerFactory yamlClusterHandlerFactory, KustoClusterHandlerFactory kustoClusterHandlerFactory)"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 33, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var clustersFile = File.ReadAllText(Path.Combine(path, \"clusters.yml\"));"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 34, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var clusters = Serialization.YamlPascalCaseDeserializer.Deserialize<Clusters>(clustersFile);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 35, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var allChanges = new List<ClusterChange>();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 37, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "foreach (var clusterConnection in clusters.Connections)"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 42, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var yamlHandler = YamlClusterHandlerFactory.Create(path);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 43, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var yamlCluster = await yamlHandler.LoadAsync();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 46, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var kustoHandler = KustoClusterHandlerFactory.Create(clusterConnection.Url);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 47, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var kustoCluster = await kustoHandler.LoadAsync();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 50, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var change = ClusterChanges.GenerateChanges(kustoCluster, yamlCluster, Log);"}], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "fair", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/KustoSchemaHandler.cs", "status": "modified", "additions": 1, "deletions": 1, "changes": 2, "totalLines": 1, "linesWithIssues": 1, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [{"type": "formatting", "severity": "minor", "message": "Line too long (132 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 44, "file": "KustoSchemaTools/KustoSchemaHandler.cs", "code": "Log.LogInformation($\"Generating database-scoped diff markdown for {Path.Combine(path, databaseName)} => {cluster}/{escapedDbName}\");"}], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/Model/Cluster.cs", "status": "modified", "additions": 1, "deletions": 0, "changes": 1, "totalLines": 1, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "status": "added", "additions": 247, "deletions": 0, "changes": 247, "totalLines": 247, "linesWithIssues": 32, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [{"type": "formatting", "severity": "minor", "message": "Line too long (128 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 31, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<ExtentsPurgeRebuildCapacity?>.Default.Equals(ExtentsPurgeRebuildCapacity, other.ExtentsPurgeRebuildCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (122 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 34, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<MaterializedViewsCapacity?>.Default.Equals(MaterializedViewsCapacity, other.MaterializedViewsCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (125 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 35, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<StoredQueryResultsCapacity?>.Default.Equals(StoredQueryResultsCapacity, other.StoredQueryResultsCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (167 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 36, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<StreamingIngestionPostProcessingCapacity?>.Default.Equals(StreamingIngestionPostProcessingCapacity, other.StreamingIngestionPostProcessingCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (155 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 37, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<PurgeStorageArtifactsCleanupCapacity?>.Default.Equals(PurgeStorageArtifactsCleanupCapacity, other.PurgeStorageArtifactsCleanupCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (164 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 38, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<PeriodicStorageArtifactsCleanupCapacity?>.Default.Equals(PeriodicStorageArtifactsCleanupCapacity, other.PeriodicStorageArtifactsCleanupCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (122 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 39, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<QueryAccelerationCapacity?>.Default.Equals(QueryAccelerationCapacity, other.QueryAccelerationCapacity) &&"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 46, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "var hc = new HashCode();"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 71, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 72, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "CoreUtilizationCoefficient == other.CoreUtilizationCoefficient;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 86, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MinimumConcurrentOperationsPerNode == other.MinimumConcurrentOperationsPerNode &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 87, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "MaximumConcurrentOperationsPerNode == other.MaximumConcurrentOperationsPerNode;"}, {"type": "formatting", "severity": "minor", "message": "Line too long (126 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 90, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "public override int GetHashCode() => HashCode.Combine(MinimumConcurrentOperationsPerNode, MaximumConcurrentOperationsPerNode);"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 100, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MaximumConcurrentOperationsPerNode == other.MaximumConcurrentOperationsPerNode;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 114, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 115, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "CoreUtilizationCoefficient == other.CoreUtilizationCoefficient;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 129, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMinimumConcurrentOperations == other.ClusterMinimumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 130, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations;"}, {"type": "formatting", "severity": "minor", "message": "Line too long (126 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 133, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "public override int GetHashCode() => HashCode.Combine(ClusterMinimumConcurrentOperations, ClusterMaximumConcurrentOperations);"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 144, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 159, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 160, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "MaximumConcurrentOperationsPerNode == other.MaximumConcurrentOperationsPerNode;"}, {"type": "formatting", "severity": "minor", "message": "Line too long (126 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 163, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "public override int GetHashCode() => HashCode.Combine(ClusterMaximumConcurrentOperations, MaximumConcurrentOperationsPerNode);"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 174, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MaximumConcurrentOperationsPerDbAdmin == other.MaximumConcurrentOperationsPerDbAdmin &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 175, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "CoreUtilizationCoefficient == other.CoreUtilizationCoefficient;"}, {"type": "formatting", "severity": "minor", "message": "Line too long (121 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 178, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "public override int GetHashCode() => HashCode.Combine(MaximumConcurrentOperationsPerDbAdmin, CoreUtilizationCoefficient);"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 188, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MaximumConcurrentOperationsPerNode == other.MaximumConcurrentOperationsPerNode;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 201, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MaximumConcurrentOperationsPerCluster == other.MaximumConcurrentOperationsPerCluster;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 214, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MaximumConcurrentOperationsPerCluster == other.MaximumConcurrentOperationsPerCluster;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 228, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 229, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "CoreUtilizationCoefficient == other.CoreUtilizationCoefficient;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 242, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations;"}], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "poor", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "status": "added", "additions": 50, "deletions": 0, "changes": 50, "totalLines": 50, "linesWithIssues": 5, "lineComments": [{"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 22, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "public async Task<Cluster> LoadAsync()"}, {"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 29, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "using (var reader = await _client.AdminClient.ExecuteControlCommandAsync(\"\", \".show cluster policy capacity\", new ClientRequestProperties()))"}], "issues": {"critical": [], "major": [], "minor": [{"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 24, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "var cluster = new Cluster();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 29, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "using (var reader = await _client.AdminClient.ExecuteControlCommandAsync(\"\", \".show cluster policy capacity\", new ClientRequestProperties()))"}, {"type": "formatting", "severity": "minor", "message": "Line too long (141 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 29, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "using (var reader = await _client.AdminClient.ExecuteControlCommandAsync(\"\", \".show cluster policy capacity\", new ClientRequestProperties()))"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 33, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "var policyJson = reader[\"Policy\"]?.ToString();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 36, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "var policy = JsonConvert.DeserializeObject<ClusterCapacityPolicy>(policyJson);"}], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/Parser/KustoClusterHandlerFactory.cs", "status": "added", "additions": 22, "deletions": 0, "changes": 22, "totalLines": 22, "linesWithIssues": 2, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [{"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 17, "file": "KustoSchemaTools/Parser/KustoClusterHandlerFactory.cs", "code": "var client = new KustoClient(clusterUrl);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 18, "file": "KustoSchemaTools/Parser/KustoClusterHandlerFactory.cs", "code": "var logger = _loggerFactory.CreateLogger<KustoClusterHandler>();"}], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/Parser/YamlClusterHandler.cs", "status": "added", "additions": 31, "deletions": 0, "changes": 31, "totalLines": 31, "linesWithIssues": 4, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [{"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 19, "file": "KustoSchemaTools/Parser/YamlClusterHandler.cs", "code": "var clustersFile = File.ReadAllText(Path.Combine(_path, \"clusters.yml\"));"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 20, "file": "KustoSchemaTools/Parser/YamlClusterHandler.cs", "code": "var clusters = Serialization.YamlPascalCaseDeserializer.Deserialize<Clusters>(clustersFile);"}, {"type": "quality", "severity": "minor", "message": "TODO/FIXME comment found", "suggestion": "Address TODO items or create proper issue tracking", "line": 22, "file": "KustoSchemaTools/Parser/YamlClusterHandler.cs", "code": "// TODO -- handle case with multiple clusters"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 23, "file": "KustoSchemaTools/Parser/YamlClusterHandler.cs", "code": "var cluster = new Cluster"}], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "KustoSchemaTools/Parser/YamlClusterHandlerFactory.cs", "status": "added", "additions": 10, "deletions": 0, "changes": 10, "totalLines": 10, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 732, "linesWithIssues": 90, "comments": [{"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 31, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "public async Task<List<ClusterChange>> GenerateChangesAsync(string path)"}, {"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 43, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var yamlCluster = await yamlHandler.LoadAsync();"}, {"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 47, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var kustoCluster = await kustoHandler.LoadAsync();"}, {"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 22, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "public async Task<Cluster> LoadAsync()"}, {"type": "error_handling", "message": "Ensure proper error handling for async operations", "suggestion": "Add try-catch blocks or proper error handling for asynchronous code", "line": 29, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "using (var reader = await _client.AdminClient.ExecuteControlCommandAsync(\"\", \".show cluster policy capacity\", new ClientRequestProperties()))"}]}, "issues": {"critical": [], "major": [], "minor": [{"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 24, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "var oldCluster = CreateClusterWithPolicy(0.2, 1, 2, 3);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 25, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "var newCluster = CreateClusterWithPolicy(0.2, 1, 2, 3);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 28, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "var changes = ClusterChanges.GenerateChanges(oldCluster, newCluster, _loggerMock.Object);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 39, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var oldCluster = CreateClusterWithPolicy(totalCapacity: 1000, coreUtilization: 0.75);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 40, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var newCluster = CreateClusterWithPolicy(totalCapacity: 1200, coreUtilization: 0.75);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 43, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var changes = ClusterChanges.GenerateChanges(oldCluster, newCluster, _loggerMock.Object);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 49, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var policyChange = Assert.Single(changes.PolicyChanges);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 52, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var capacityChange = policyChange.Value;"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 53, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var propertyChange = Assert.Single(capacityChange.PropertyChanges);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 59, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var expectedJson = JsonConvert.SerializeObject(newCluster.CapacityPolicy, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });"}, {"type": "formatting", "severity": "minor", "message": "Line too long (158 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 59, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var expectedJson = JsonConvert.SerializeObject(newCluster.CapacityPolicy, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 60, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var expectedScript = $\".alter-merge cluster policy capacity @'{expectedJson}'\";"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 68, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var oldCluster = CreateClusterWithPolicy(totalCapacity: 1000, coreUtilization: 0.75);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 69, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var newCluster = CreateClusterWithPolicy(totalCapacity: 1200, coreUtilization: 0.90);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 72, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var changes = ClusterChanges.GenerateChanges(oldCluster, newCluster, _loggerMock.Object);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 75, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var policyChange = Assert.Single(changes.PolicyChanges);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 76, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var capacityChange = policyChange.Value;"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 79, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var capacityPropChange = capacityChange.PropertyChanges.Single(p => p.PropertyName == \"TotalCapacity\");"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 79, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var capacityPropChange = capacityChange.PropertyChanges.Single(p => p.PropertyName == \"TotalCapacity\");"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 80, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var coreUtilPropChange = capacityChange.PropertyChanges.Single(p => p.PropertyName == \"CoreUtilizationCoefficient\");"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 80, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var coreUtilPropChange = capacityChange.PropertyChanges.Single(p => p.PropertyName == \"CoreUtilizationCoefficient\");"}, {"type": "formatting", "severity": "minor", "message": "Line too long (123 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 80, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var coreUtilPropChange = capacityChange.PropertyChanges.Single(p => p.PropertyName == \"CoreUtilizationCoefficient\");"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 93, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var oldCluster = CreateClusterWithPolicy(totalCapacity: 1000);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 94, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var newCluster = new Cluster(); // No capacity policy defined"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 97, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var changes = ClusterChanges.GenerateChanges(oldCluster, newCluster, _loggerMock.Object);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 109, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var oldCluster = new Cluster { CapacityPolicy = new CapacityPolicy() };"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 111, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var newCluster = CreateClusterWithPolicy(coreUtilization: 0.95);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 114, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var changes = ClusterChanges.GenerateChanges(oldCluster, newCluster, _loggerMock.Object);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 117, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var policyChange = Assert.Single(changes.PolicyChanges).Value;"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 118, "file": "KustoSchemaTools.Tests/Changes/ClusterChangesTest.cs", "code": "//     var propertyChange = Assert.Single(policyChange.PropertyChanges);"}, {"type": "quality", "severity": "minor", "message": "TODO/FIXME comment found", "suggestion": "Address TODO items or create proper issue tracking", "line": 7, "file": "KustoSchemaTools/Changes/ClusterChange.cs", "code": "// TODO: Future extensibility for workload groups can be added here"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 16, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var clusterName = oldCluster.Name;"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 17, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var clusterChange = new ClusterChange"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 26, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var capacityPolicyChange = new PolicyChange();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 27, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var newPolicyProps = newCluster.CapacityPolicy.GetType().GetProperties()"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 30, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "foreach (var prop in newPolicyProps)"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 32, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var newValue = prop.GetValue(newCluster.CapacityPolicy);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 33, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var oldValue = prop.GetValue(oldCluster.CapacityPolicy);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 49, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var newPolicyJson = JsonConvert.SerializeObject(newCluster.CapacityPolicy, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });"}, {"type": "formatting", "severity": "minor", "message": "Line too long (152 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 49, "file": "KustoSchemaTools/Changes/ClusterChanges.cs", "code": "var newPolicyJson = JsonConvert.SerializeObject(newCluster.CapacityPolicy, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });"}, {"type": "formatting", "severity": "minor", "message": "Line too long (181 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 14, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "public KustoClusterOrchestrator(ILogger<KustoClusterOrchestrator> logger, YamlClusterHandlerFactory yamlClusterHandlerFactory, KustoClusterHandlerFactory kustoClusterHandlerFactory)"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 33, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var clustersFile = File.ReadAllText(Path.Combine(path, \"clusters.yml\"));"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 34, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var clusters = Serialization.YamlPascalCaseDeserializer.Deserialize<Clusters>(clustersFile);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 35, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var allChanges = new List<ClusterChange>();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 37, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "foreach (var clusterConnection in clusters.Connections)"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 42, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var yamlHandler = YamlClusterHandlerFactory.Create(path);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 43, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var yamlCluster = await yamlHandler.LoadAsync();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 46, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var kustoHandler = KustoClusterHandlerFactory.Create(clusterConnection.Url);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 47, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var kustoCluster = await kustoHandler.LoadAsync();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 50, "file": "KustoSchemaTools/KustoClusterOrchestrator.cs", "code": "var change = ClusterChanges.GenerateChanges(kustoCluster, yamlCluster, Log);"}, {"type": "formatting", "severity": "minor", "message": "Line too long (132 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 44, "file": "KustoSchemaTools/KustoSchemaHandler.cs", "code": "Log.LogInformation($\"Generating database-scoped diff markdown for {Path.Combine(path, databaseName)} => {cluster}/{escapedDbName}\");"}, {"type": "formatting", "severity": "minor", "message": "Line too long (128 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 31, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<ExtentsPurgeRebuildCapacity?>.Default.Equals(ExtentsPurgeRebuildCapacity, other.ExtentsPurgeRebuildCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (122 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 34, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<MaterializedViewsCapacity?>.Default.Equals(MaterializedViewsCapacity, other.MaterializedViewsCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (125 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 35, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<StoredQueryResultsCapacity?>.Default.Equals(StoredQueryResultsCapacity, other.StoredQueryResultsCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (167 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 36, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<StreamingIngestionPostProcessingCapacity?>.Default.Equals(StreamingIngestionPostProcessingCapacity, other.StreamingIngestionPostProcessingCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (155 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 37, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<PurgeStorageArtifactsCleanupCapacity?>.Default.Equals(PurgeStorageArtifactsCleanupCapacity, other.PurgeStorageArtifactsCleanupCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (164 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 38, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<PeriodicStorageArtifactsCleanupCapacity?>.Default.Equals(PeriodicStorageArtifactsCleanupCapacity, other.PeriodicStorageArtifactsCleanupCapacity) &&"}, {"type": "formatting", "severity": "minor", "message": "Line too long (122 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 39, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "EqualityComparer<QueryAccelerationCapacity?>.Default.Equals(QueryAccelerationCapacity, other.QueryAccelerationCapacity) &&"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 46, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "var hc = new HashCode();"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 71, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 72, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "CoreUtilizationCoefficient == other.CoreUtilizationCoefficient;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 86, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MinimumConcurrentOperationsPerNode == other.MinimumConcurrentOperationsPerNode &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 87, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "MaximumConcurrentOperationsPerNode == other.MaximumConcurrentOperationsPerNode;"}, {"type": "formatting", "severity": "minor", "message": "Line too long (126 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 90, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "public override int GetHashCode() => HashCode.Combine(MinimumConcurrentOperationsPerNode, MaximumConcurrentOperationsPerNode);"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 100, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MaximumConcurrentOperationsPerNode == other.MaximumConcurrentOperationsPerNode;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 114, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 115, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "CoreUtilizationCoefficient == other.CoreUtilizationCoefficient;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 129, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMinimumConcurrentOperations == other.ClusterMinimumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 130, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations;"}, {"type": "formatting", "severity": "minor", "message": "Line too long (126 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 133, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "public override int GetHashCode() => HashCode.Combine(ClusterMinimumConcurrentOperations, ClusterMaximumConcurrentOperations);"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 144, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 159, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 160, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "MaximumConcurrentOperationsPerNode == other.MaximumConcurrentOperationsPerNode;"}, {"type": "formatting", "severity": "minor", "message": "Line too long (126 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 163, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "public override int GetHashCode() => HashCode.Combine(ClusterMaximumConcurrentOperations, MaximumConcurrentOperationsPerNode);"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 174, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MaximumConcurrentOperationsPerDbAdmin == other.MaximumConcurrentOperationsPerDbAdmin &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 175, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "CoreUtilizationCoefficient == other.CoreUtilizationCoefficient;"}, {"type": "formatting", "severity": "minor", "message": "Line too long (121 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 178, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "public override int GetHashCode() => HashCode.Combine(MaximumConcurrentOperationsPerDbAdmin, CoreUtilizationCoefficient);"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 188, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MaximumConcurrentOperationsPerNode == other.MaximumConcurrentOperationsPerNode;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 201, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MaximumConcurrentOperationsPerCluster == other.MaximumConcurrentOperationsPerCluster;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 214, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return MaximumConcurrentOperationsPerCluster == other.MaximumConcurrentOperationsPerCluster;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 228, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations &&"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 229, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "CoreUtilizationCoefficient == other.CoreUtilizationCoefficient;"}, {"type": "quality", "severity": "minor", "message": "Loose equality operator detected", "suggestion": "Use strict equality (===) instead of loose equality (==)", "line": 242, "file": "KustoSchemaTools/Model/ClusterCapacityPolicy.cs", "code": "return ClusterMaximumConcurrentOperations == other.ClusterMaximumConcurrentOperations;"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 24, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "var cluster = new Cluster();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 29, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "using (var reader = await _client.AdminClient.ExecuteControlCommandAsync(\"\", \".show cluster policy capacity\", new ClientRequestProperties()))"}, {"type": "formatting", "severity": "minor", "message": "Line too long (141 characters)", "suggestion": "Break long lines for better readability (recommended max: 120 characters)", "line": 29, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "using (var reader = await _client.AdminClient.ExecuteControlCommandAsync(\"\", \".show cluster policy capacity\", new ClientRequestProperties()))"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 33, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "var policyJson = reader[\"Policy\"]?.ToString();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 36, "file": "KustoSchemaTools/Parser/KustoClusterHandler.cs", "code": "var policy = JsonConvert.DeserializeObject<ClusterCapacityPolicy>(policyJson);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 17, "file": "KustoSchemaTools/Parser/KustoClusterHandlerFactory.cs", "code": "var client = new KustoClient(clusterUrl);"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 18, "file": "KustoSchemaTools/Parser/KustoClusterHandlerFactory.cs", "code": "var logger = _loggerFactory.CreateLogger<KustoClusterHandler>();"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 19, "file": "KustoSchemaTools/Parser/YamlClusterHandler.cs", "code": "var clustersFile = File.ReadAllText(Path.Combine(_path, \"clusters.yml\"));"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 20, "file": "KustoSchemaTools/Parser/YamlClusterHandler.cs", "code": "var clusters = Serialization.YamlPascalCaseDeserializer.Deserialize<Clusters>(clustersFile);"}, {"type": "quality", "severity": "minor", "message": "TODO/FIXME comment found", "suggestion": "Address TODO items or create proper issue tracking", "line": 22, "file": "KustoSchemaTools/Parser/YamlClusterHandler.cs", "code": "// TODO -- handle case with multiple clusters"}, {"type": "quality", "severity": "minor", "message": "Use of 'var' detected", "suggestion": "Consider using 'let' or 'const' instead of 'var' for better scoping", "line": 23, "file": "KustoSchemaTools/Parser/YamlClusterHandler.cs", "code": "var cluster = new Cluster"}], "suggestions": []}, "recommendations": ["📁 Consider breaking large changes into smaller, focused PRs", "📏 Large PR detected - ensure adequate testing and review time", "🧹 Multiple minor issues found - consider code cleanup"], "metadata": {"repository": "github/KustoSchemaTools", "prNumber": "106", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:10:08.608Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T20:10:08.608Z"}, {"id": "003c87bed679e39afdd0685228779094", "userId": null, "repoUrl": "https://github.com/facebook/react", "prNumber": 456, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "facebook/react", "prNumber": "456", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:08:57.144Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:08:57.144Z"}, {"id": "aa8f60bf4f1c43c83a7bc0f7718c6a8d", "userId": null, "repoUrl": "https://github.com/facebook/react", "prNumber": 456, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "facebook/react", "prNumber": "456", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:08:00.389Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:08:00.389Z"}, {"id": "04aa696a296c82a3922f2221c7753e8c", "userId": null, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:07:55.801Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T20:07:55.801Z"}, {"id": "37fe686e015fbda08c14ba053290fefb", "userId": null, "repoUrl": "https://github.com/facebook/react", "prNumber": 456, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "facebook/react", "prNumber": "456", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:07:51.168Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:07:51.168Z"}, {"id": "633a31564fbd460b9a4248abd68cac08", "userId": null, "repoUrl": "https://github.com/nonexistent/repo", "prNumber": 999, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "nonexistent/repo", "prNumber": "999", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:07:36.607Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:07:36.607Z"}, {"id": "78d1bed3c4f3f95af1799b8d60d878db", "userId": null, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:07:35.513Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T20:07:35.514Z"}, {"id": "4e19cca9b999f6bd5baa8d5e56fe31a7", "userId": null, "repoUrl": "https://github.com/nonexistent/repo", "prNumber": 999, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "nonexistent/repo", "prNumber": "999", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:04:54.601Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:04:54.601Z"}, {"id": "0cf53746280801e7622545d7662f1a93", "userId": null, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:04:53.530Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T20:04:53.530Z"}, {"id": "5f940dc7f47d7a098c0b259b94e8de9c", "userId": null, "repoUrl": "https://github.com/facebook/react", "prNumber": 456, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "facebook/react", "prNumber": "456", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:04:20.810Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:04:20.810Z"}, {"id": "ae4219186ee4d952c12db05c8846b35c", "userId": null, "repoUrl": "https://github.com/facebook/react", "prNumber": 456, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "facebook/react", "prNumber": "456", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:03:46.106Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:03:46.106Z"}, {"id": "40c939490d2b63d6646684a84cdac80d", "userId": null, "repoUrl": "https://github.com/microsoft/vscode", "prNumber": 123, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "microsoft/vscode", "prNumber": "123", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:03:37.501Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:03:37.501Z"}, {"id": "b92b29e5bf45ded51b2ccf1c74f6024f", "userId": null, "repoUrl": "https://github.com/facebook/react", "prNumber": 456, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "facebook/react", "prNumber": "456", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:02:35.209Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:02:35.209Z"}, {"id": "3eebcc013099a88ff6ba1a62373c0a8b", "userId": null, "repoUrl": "https://github.com/nonexistent/repo", "prNumber": 999, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "nonexistent/repo", "prNumber": "999", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:02:10.099Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:02:10.099Z"}, {"id": "4edc436f722b1e862be3aa946b1ffe89", "userId": null, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:02:09.020Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T20:02:09.020Z"}, {"id": "905b1413ea620c6f426354a4aae68d14", "userId": null, "repoUrl": "https://github.com/facebook/react", "prNumber": 456, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 0, "totalChecks": 0, "percentage": 0, "status": "error"}, "categories": {}, "fileAnalysis": {"totalFiles": 0, "filesAnalyzed": 0, "files": []}, "lineByLineReview": {"totalLines": 0, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["Analysis failed: Failed to fetch PR data: PR not found"], "metadata": {"repository": "facebook/react", "prNumber": "456", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:01:46.223Z", "analyzedBy": "anonymous", "error": "Failed to fetch PR data: PR not found"}}, "createdAt": "2025-06-25T20:01:46.223Z"}, {"id": "298d45cba9187073ecdc3e4778fde333", "userId": null, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:00:50.729Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T20:00:50.729Z"}, {"id": "cfa017686f446938aa5e7f1eb3b64e30", "userId": null, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T20:00:11.389Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T20:00:11.390Z"}, {"id": "af93aae85d90c50735e116b40a4b16f3", "userId": 87066855, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "private", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "private", "checklistId": "default", "analyzedAt": "2025-06-25T19:58:04.613Z", "analyzedBy": "saltaf07"}}, "createdAt": "2025-06-25T19:58:04.614Z"}, {"id": "252a4da2717b78dec196dad4e2dbd8c5", "userId": null, "repoUrl": "https://github.com/hashicorp/terraform-guides", "prNumber": 332, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 4, "totalChecks": 10, "percentage": 40, "status": "poor"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 2, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": false, "message": "Title should start with a capital letter", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": false, "message": "Title should use imperative mood (e.g., \"Fix bug\" not \"Fixed bug\")", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 2, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": false, "message": "Too many files changed. Consider breaking into smaller PRs", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": false, "message": "Too many additions. Consider breaking into smaller PRs", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": false, "message": "Should include information about how changes were tested", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 30, "filesAnalyzed": 30, "files": [{"filename": "governance/first-generation/aws/aws-vpcs-must-have-tags-and-enable-dns-hostnames.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-ami-owners.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-mandatory-tags.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-tag-from-data-source.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/must_have_remote_exec_provisioner.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/openshift-aws-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/require-private-acl-and-kms-for-s3-buckets.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/require-vpc-and-kms-for-lambda-functions.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-availability-zones.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-cidr-blocks.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-instance-type.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-region.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-iam-policy-statement.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-iam-policy-statement2.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/sentinel.hcl", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/acs-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/aks-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/block-allow-all-cidr.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/enforce-mandatory-tags.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-current-azure-vms.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-image-id.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-publisher.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-size.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/cloud-agnostic/destroy-limit.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account.sh", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account.tf", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account_balance.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/block-allow-all-cidr.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/enforce-mandatory-labels.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/gke-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 90, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["📁 Consider breaking large changes into smaller, focused PRs"], "metadata": {"repository": "hashicorp/terraform-guides", "prNumber": "332", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T19:55:35.537Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T19:55:35.537Z"}, {"id": "384cd9696c2c80c770c9b9706591f4aa", "userId": null, "repoUrl": "https://github.com/hashicorp/terraform-guides", "prNumber": 332, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 4, "totalChecks": 10, "percentage": 40, "status": "poor"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 2, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": false, "message": "Title should start with a capital letter", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": false, "message": "Title should use imperative mood (e.g., \"Fix bug\" not \"Fixed bug\")", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 2, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": false, "message": "Too many files changed. Consider breaking into smaller PRs", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": false, "message": "Too many additions. Consider breaking into smaller PRs", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": false, "message": "Should include information about how changes were tested", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 30, "filesAnalyzed": 30, "files": [{"filename": "governance/first-generation/aws/aws-vpcs-must-have-tags-and-enable-dns-hostnames.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-ami-owners.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-mandatory-tags.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/enforce-tag-from-data-source.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/must_have_remote_exec_provisioner.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/openshift-aws-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/require-private-acl-and-kms-for-s3-buckets.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/require-vpc-and-kms-for-lambda-functions.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-availability-zones.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-cidr-blocks.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-instance-type.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-aws-region.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-iam-policy-statement.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/restrict-iam-policy-statement2.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/aws/sentinel.hcl", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/acs-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/aks-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/block-allow-all-cidr.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/enforce-mandatory-tags.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-current-azure-vms.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-image-id.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-publisher.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/azure/restrict-vm-size.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/cloud-agnostic/destroy-limit.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account.sh", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account.tf", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/external/check_account_balance.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/block-allow-all-cidr.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/enforce-mandatory-labels.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}, {"filename": "governance/first-generation/gcp/gke-cluster-policy.sentinel", "status": "modified", "additions": 3, "deletions": 0, "changes": 3, "totalLines": 3, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 90, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["📁 Consider breaking large changes into smaller, focused PRs"], "metadata": {"repository": "hashicorp/terraform-guides", "prNumber": "332", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T19:55:00.607Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T19:55:00.607Z"}, {"id": "6e0e78c0cf0e0553a72e455891dcaa4c", "userId": null, "repoUrl": "https://github.com/saltaf07/assistila-web", "prNumber": 1, "analysisType": "public", "checklistId": "default", "analysis": {"summary": {"score": 9, "totalChecks": 10, "percentage": 90, "status": "excellent"}, "categories": {"git_commit_guidelines": {"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "totalChecks": 4, "passedChecks": 4, "checks": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "passed": true, "message": "Title follows 50-character guideline", "severity": "medium", "details": {}}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "passed": true, "message": "Title is properly capitalized", "severity": "low", "details": {}}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "passed": true, "message": "Title correctly omits trailing period", "severity": "low", "details": {}}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "passed": true, "message": "Title uses imperative mood", "severity": "medium", "details": {}}]}, "pr_quality": {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "totalChecks": 6, "passedChecks": 5, "checks": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "passed": true, "message": "PR has detailed description explaining context", "severity": "high", "details": {}}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "passed": false, "message": "Branch should follow format: feature/description or TICKET-123_description", "severity": "medium", "details": {}}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "passed": true, "message": "PR has reasonable scope", "severity": "medium", "details": {}}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "passed": true, "message": "PR has manageable size", "severity": "medium", "details": {}}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "passed": true, "message": "Correctly targets main/master branch", "severity": "high", "details": {}}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "passed": true, "message": "Includes testing information", "severity": "high", "details": {}}]}}, "fileAnalysis": {"totalFiles": 1, "filesAnalyzed": 1, "files": [{"filename": "test-pr", "status": "added", "additions": 2, "deletions": 0, "changes": 2, "totalLines": 2, "linesWithIssues": 0, "lineComments": [], "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "codeQuality": {"complexity": "low", "maintainability": "good", "security": "safe"}, "recommendations": []}]}, "lineByLineReview": {"totalLines": 2, "linesWithIssues": 0, "comments": []}, "issues": {"critical": [], "major": [], "minor": [], "suggestions": []}, "recommendations": ["✅ Excellent PR quality - ready for merge after final review"], "metadata": {"repository": "saltaf07/assistila-web", "prNumber": "1", "analysisType": "public", "checklistId": "default", "analyzedAt": "2025-06-25T19:52:50.073Z", "analyzedBy": "anonymous"}}, "createdAt": "2025-06-25T19:52:50.074Z"}]