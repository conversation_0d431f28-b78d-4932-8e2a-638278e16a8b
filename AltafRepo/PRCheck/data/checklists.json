{"default": {"id": "default", "name": "Comprehensive PR Review", "description": "Complete checklist covering all aspects of PR quality", "checks": [{"id": "git_commit_guidelines", "name": "Git Commit Guidelines", "description": "Based on https://cbea.ms/git-commit/", "rules": [{"id": "title_length", "name": "Title Length (≤50 chars)", "description": "Commit title should be 50 characters or less", "type": "title_check", "severity": "medium", "rule": "length <= 50", "passMessage": "Title follows 50-character guideline", "failMessage": "Title too long. Keep under 50 characters for better readability"}, {"id": "title_capitalized", "name": "Capitalized Title", "description": "Commit title should start with a capital letter", "type": "title_check", "severity": "low", "rule": "starts_with_capital", "passMessage": "Title is properly capitalized", "failMessage": "Title should start with a capital letter"}, {"id": "no_period_title", "name": "No Period in Title", "description": "Commit title should not end with a period", "type": "title_check", "severity": "low", "rule": "no_trailing_period", "passMessage": "Title correctly omits trailing period", "failMessage": "Title should not end with a period"}, {"id": "imperative_mood", "name": "Imperative Mood", "description": "Use imperative mood in the subject line", "type": "title_check", "severity": "medium", "rule": "imperative_mood", "passMessage": "Title uses imperative mood", "failMessage": "Title should use imperative mood (e.g., \"Fix bug\" not \"Fixed bug\")"}]}, {"id": "pr_quality", "name": "PR Quality Checks", "description": "Best practices for pull request quality", "rules": [{"id": "meaningful_description", "name": "Meaningful Description", "description": "PR should have detailed description explaining WHY", "type": "description_check", "severity": "high", "rule": "length > 50", "passMessage": "PR has detailed description explaining context", "failMessage": "PR should include detailed description explaining WHY (>50 characters)"}, {"id": "branch_naming", "name": "Branch Naming Convention", "description": "Branch should follow naming convention", "type": "branch_check", "severity": "medium", "rule": "follows_convention", "passMessage": "Branch follows naming convention", "failMessage": "Branch should follow format: feature/description or TICKET-123_description"}, {"id": "reasonable_scope", "name": "Reasonable Scope", "description": "PR should not change too many files", "type": "size_check", "severity": "medium", "rule": "files <= 10", "passMessage": "PR has reasonable scope", "failMessage": "Too many files changed. Consider breaking into smaller PRs"}, {"id": "manageable_size", "name": "Manageable Size", "description": "PR should not have too many additions", "type": "size_check", "severity": "medium", "rule": "additions <= 500", "passMessage": "PR has manageable size", "failMessage": "Too many additions. Consider breaking into smaller PRs"}, {"id": "target_branch", "name": "Targets Main/Master", "description": "PR should target main or master branch", "type": "branch_check", "severity": "high", "rule": "target_main_master", "passMessage": "Correctly targets main/master branch", "failMessage": "Should target main/master branch for integration"}, {"id": "testing_info", "name": "Testing Information", "description": "PR should include testing information", "type": "description_check", "severity": "high", "rule": "contains_testing_keywords", "passMessage": "Includes testing information", "failMessage": "Should include information about how changes were tested"}]}]}, "security_focused": {"id": "security_focused", "name": "Security-Focused Review", "description": "Checklist focused on security best practices", "checks": [{"id": "security_checks", "name": "Security Validation", "description": "Security-focused code review", "rules": [{"id": "no_hardcoded_secrets", "name": "No Hardcoded Secrets", "description": "Code should not contain hardcoded passwords, API keys, or secrets", "type": "security_check", "severity": "critical", "rule": "no_hardcoded_credentials", "passMessage": "No hardcoded secrets detected", "failMessage": "Hardcoded secrets detected. Use environment variables or secure storage"}, {"id": "input_validation", "name": "Input Validation", "description": "User inputs should be properly validated", "type": "security_check", "severity": "high", "rule": "validates_input", "passMessage": "Input validation appears adequate", "failMessage": "Ensure all user inputs are properly validated and sanitized"}, {"id": "sql_injection_prevention", "name": "SQL Injection Prevention", "description": "Database queries should use parameterized statements", "type": "security_check", "severity": "critical", "rule": "no_sql_injection", "passMessage": "No SQL injection vulnerabilities detected", "failMessage": "Potential SQL injection vulnerability. Use parameterized queries"}]}]}, "code_quality": {"id": "code_quality", "name": "Code Quality Focus", "description": "Checklist focused on code quality and maintainability", "checks": [{"id": "code_standards", "name": "Code Standards", "description": "Code quality and maintainability checks", "rules": [{"id": "no_debug_code", "name": "No Debug Code", "description": "Code should not contain console.log or debugger statements", "type": "quality_check", "severity": "medium", "rule": "no_debug_statements", "passMessage": "No debug code detected", "failMessage": "Remove console.log and debugger statements before merging"}, {"id": "proper_error_handling", "name": "Proper <PERSON><PERSON><PERSON>", "description": "Code should have appropriate error handling", "type": "quality_check", "severity": "high", "rule": "has_error_handling", "passMessage": "Error handling appears adequate", "failMessage": "Add proper error handling for robustness"}, {"id": "code_documentation", "name": "Code Documentation", "description": "Functions and classes should be documented", "type": "quality_check", "severity": "medium", "rule": "has_documentation", "passMessage": "Code documentation is adequate", "failMessage": "Add documentation for functions and classes"}]}]}, "minimal": {"id": "minimal", "name": "Minimal Review", "description": "Basic checklist for quick reviews", "checks": [{"id": "basic_checks", "name": "Basic Quality", "description": "Essential quality checks", "rules": [{"id": "has_description", "name": "Has Description", "description": "PR should have a description", "type": "description_check", "severity": "medium", "rule": "not_empty", "passMessage": "PR has description", "failMessage": "PR should have a meaningful description"}, {"id": "reasonable_size", "name": "Reasonable Size", "description": "PR should not be too large", "type": "size_check", "severity": "medium", "rule": "additions <= 1000", "passMessage": "PR size is reasonable", "failMessage": "PR is quite large. Consider breaking it down"}]}]}}