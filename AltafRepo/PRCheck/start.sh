#!/bin/bash

# PR Review Assistant Startup Script

echo "🚀 Starting PR Review Assistant with <PERSON><PERSON><PERSON> (smollm2:360m)"
echo "=================================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it from .env.example"
    echo "   cp .env.example .env"
    echo "   Then edit .env with your GitHub token"
    exit 1
fi

# Check if GitHub token is configured
if grep -q "****************************************" .env; then
    echo "⚠️  Please update your GitHub token in .env file"
    echo "   Current token appears to be a placeholder"
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker compose down

# Build and start services
echo "🔨 Building and starting services..."
docker compose up --build -d

# Wait for services to start
echo "⏳ Waiting for services to initialize..."
sleep 10

# Check service status
echo "📊 Service Status:"
echo "=================="

# Check main app
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ Main Application: Running (http://localhost:3000)"
else
    echo "❌ Main Application: Not responding"
fi

# Check Ollama
if curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "✅ Ollama Service: Running (http://localhost:11434)"
else
    echo "⏳ Ollama Service: Starting up..."
fi

# Check PostgreSQL
if docker compose exec -T postgres pg_isready -U pr_user > /dev/null 2>&1; then
    echo "✅ PostgreSQL: Running"
else
    echo "❌ PostgreSQL: Not ready"
fi

# Check Redis
if docker compose exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis: Running"
else
    echo "❌ Redis: Not ready"
fi

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo "📱 Access your application at: http://localhost:3000"
echo "🤖 Ollama API available at: http://localhost:11434"
echo ""
echo "📋 Next Steps:"
echo "1. Open http://localhost:3000 in your browser"
echo "2. Wait for Ollama to download smollm2:360m model (first time only)"
echo "3. Test with your private GitHub repositories"
echo ""
echo "📝 To view logs: docker compose logs -f"
echo "🛑 To stop: docker compose down"
