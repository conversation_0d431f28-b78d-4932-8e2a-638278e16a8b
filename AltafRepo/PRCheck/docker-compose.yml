version: '3.8'

services:
  pr-review-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      - OLLAMA_URL=http://ollama:11434
      - AI_MODEL=smollm2:360m
      - SESSION_SECRET=${SESSION_SECRET:-your-secret-key-here}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
      - ollama
    restart: unless-stopped
    networks:
      - pr-review-network

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped
    networks:
      - pr-review-network
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - pr-review-network

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=pr_review
      - POSTGRES_USER=pr_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-pr_password_123}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - pr-review-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - pr-review-app
    restart: unless-stopped
    networks:
      - pr-review-network

volumes:
  ollama_data:
  redis_data:
  postgres_data:

networks:
  pr-review-network:
    driver: bridge
