# CLI Authentication Guide for PR Review Tool

## 🎯 Overview

You can now authenticate with GitHub using CLI-friendly methods that **don't require client secrets or callback URLs**! This makes it perfect for CLI usage, Docker environments, and situations where traditional OAuth setup is complex.

## 🔐 Authentication Methods

### Method 1: GitHub Device Flow (Recommended)

**Perfect for CLI usage - no client secret needed!**

The Device Flow is GitHub's recommended method for CLI applications. Here's how it works:

1. **Start the flow**: Your application requests a device code
2. **User verification**: User visits GitHub and enters a code
3. **Automatic completion**: Your application automatically detects when authorization is complete

#### How to Use:

```bash
# 1. Start device flow
curl -X POST http://localhost:3000/auth/github/device \
  -H "Content-Type: application/json"

# Response will include:
# - user_code: Code for user to enter on GitHub
# - verification_uri: GitHub URL to visit
# - device_code: Code for polling
# - expires_in: How long the code is valid
# - interval: How often to poll

# 2. User visits verification_uri and enters user_code

# 3. Poll for completion
curl -X POST http://localhost:3000/auth/github/device/poll \
  -H "Content-Type: application/json" \
  -d '{"device_code": "YOUR_DEVICE_CODE"}'

# Keep polling until success or error
```

### Method 2: Personal Access Token

**Quick and direct authentication**

Use your GitHub Personal Access Token for immediate authentication.

#### How to Use:

```bash
# Authenticate with your token
curl -X POST http://localhost:3000/auth/github/token \
  -H "Content-Type: application/json" \
  -d '{"token": "ghp_your_personal_access_token_here"}'
```

#### Creating a Personal Access Token:

1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Click "Generate new token (classic)"
3. Select scopes: `repo` and `user:email`
4. Copy the generated token

## 🚀 CLI Demo Script

I've created a complete CLI demo script that shows both authentication methods:

```bash
# Make the script executable
chmod +x cli-auth-demo.js

# Run the demo
node cli-auth-demo.js
```

The demo script provides:
- Interactive menu for choosing authentication method
- Step-by-step guidance for Device Flow
- Token validation for Personal Access Token
- Demo PR analysis after successful authentication

## 🔧 Configuration Requirements

### Minimal Setup (Device Flow)
- **Required**: Only `GITHUB_CLIENT_ID` in your `.env` file
- **Not Required**: No `GITHUB_CLIENT_SECRET` needed!
- **Not Required**: No callback URL configuration

### For Personal Access Token
- **Required**: Nothing! Just your personal token
- Works completely independently

## 📋 Environment Setup

Create or update your `.env` file:

```bash
# Minimal configuration for Device Flow
GITHUB_CLIENT_ID=your_github_client_id

# Optional: For traditional OAuth (if you want both methods)
GITHUB_CLIENT_SECRET=your_github_client_secret

# Application settings
PORT=3000
SESSION_SECRET=your_session_secret
```

## 🌐 Web Interface Integration

The enhanced login page now includes:

1. **Device Flow Option**: Click "Device Flow (CLI-friendly)" for guided setup
2. **Personal Token Option**: Enter your token directly
3. **Traditional OAuth**: Still available if configured

## 🔄 API Endpoints

### Device Flow Endpoints

```bash
# Start device flow
POST /auth/github/device
Response: {
  "success": true,
  "user_code": "ABCD-1234",
  "verification_uri": "https://github.com/login/device",
  "device_code": "device_code_here",
  "expires_in": 900,
  "interval": 5
}

# Poll for completion
POST /auth/github/device/poll
Body: {"device_code": "device_code_here"}
Response: {
  "success": true,
  "user": {
    "id": 12345,
    "login": "username",
    "name": "User Name",
    "email": "<EMAIL>",
    "avatar_url": "https://..."
  }
}
```

### Personal Token Endpoint

```bash
# Authenticate with token
POST /auth/github/token
Body: {"token": "ghp_your_token_here"}
Response: {
  "success": true,
  "user": {
    "id": 12345,
    "login": "username",
    "name": "User Name",
    "email": "<EMAIL>",
    "avatar_url": "https://..."
  }
}
```

## 🎯 Benefits

### Device Flow Advantages:
- ✅ No client secret required
- ✅ Perfect for CLI applications
- ✅ Secure - user authorizes on GitHub directly
- ✅ Works in any environment (Docker, CI/CD, etc.)
- ✅ User-friendly verification process

### Personal Token Advantages:
- ✅ Instant authentication
- ✅ No additional setup required
- ✅ Works offline
- ✅ Full control over permissions
- ✅ Can be revoked anytime

## 🔒 Security Notes

1. **Device Flow**: User always authorizes on GitHub's official website
2. **Personal Tokens**: Store securely, never commit to version control
3. **Scopes**: Both methods require `repo` and `user:email` scopes
4. **Revocation**: Both methods can be revoked from GitHub settings

## 🚀 Getting Started

1. **Set up minimal environment**:
   ```bash
   echo "GITHUB_CLIENT_ID=your_client_id" > .env
   ```

2. **Start the application**:
   ```bash
   docker compose -f docker-compose.v2.yml up -d
   ```

3. **Choose your method**:
   - **CLI**: Use the demo script or API endpoints directly
   - **Web**: Visit `/login` and choose your preferred method

4. **Start analyzing**:
   ```bash
   # After authentication, analyze any repository
   curl -X POST http://localhost:3000/api/analyze \
     -H "Content-Type: application/json" \
     -d '{
       "repoUrl": "https://github.com/owner/repo",
       "prNumber": "123",
       "analysisType": "private",
       "checklistId": "default"
     }'
   ```

## 🎉 Success!

You now have CLI-friendly GitHub authentication without the complexity of traditional OAuth setup. Perfect for:

- CLI applications
- Docker environments
- CI/CD pipelines
- Development environments
- Any situation where callback URLs are problematic

The PR Review Tool is now truly CLI-ready! 🚀
