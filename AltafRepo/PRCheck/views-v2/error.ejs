<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .error-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .error-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <section class="error-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="card error-card">
                        <div class="card-body p-5 text-center">
                            <i class="fas fa-exclamation-triangle fa-4x text-warning mb-4"></i>
                            <h2 class="mb-3"><%= title %></h2>
                            <p class="lead text-muted mb-4"><%= message %></p>
                            
                            <% if (error) { %>
                            <div class="alert alert-danger text-start">
                                <strong>Error Details:</strong><br>
                                <%= error %>
                            </div>
                            <% } %>
                            
                            <div class="d-grid gap-2 d-md-block">
                                <a href="/" class="btn btn-primary btn-lg">
                                    <i class="fas fa-home me-2"></i>
                                    Back to Dashboard
                                </a>
                                <a href="/analyze" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-search me-2"></i>
                                    Try Analysis
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
