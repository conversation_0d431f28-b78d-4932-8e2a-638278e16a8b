<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - CodeReview Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
        }
        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .stats-card {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
        }
        .analysis-type-card {
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .analysis-type-card:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .analysis-type-card.selected {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-check me-2 text-primary"></i>
                <span class="fw-bold">CodeReview Pro</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analyze">Analyze PR</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/checklists">Checklists</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <% if (user) { %>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <img src="<%= user.avatar_url %>" alt="Avatar" class="rounded-circle me-2" width="24" height="24">
                                <%= user.login %>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/settings">
                                    <i class="fas fa-cog me-2"></i>Settings
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form action="/auth/logout" method="POST" class="d-inline">
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    <% } else { %>
                        <li class="nav-item">
                            <a class="nav-link" href="/login">
                                <i class="fab fa-github me-1"></i>
                                Login
                            </a>
                        </li>
                    <% } %>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">Enhanced PR Review Tool</h1>
                    <p class="lead mb-4">
                        Comprehensive line-by-line analysis with customizable checklists, 
                        security scanning, and detailed feedback for your pull requests.
                    </p>
                    <div class="d-flex gap-3">
                        <a href="/analyze" class="btn btn-light btn-lg">
                            <i class="fas fa-search me-2"></i>
                            Start Analysis
                        </a>
                        <a href="/checklists" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-list-check me-2"></i>
                            Manage Checklists
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-code-branch fa-10x opacity-75"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Stats Cards -->
        <% if (user) { %>
        <div class="row mb-5">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x mb-3"></i>
                        <h3><%= totalReviews %></h3>
                        <p class="mb-0">Total Reviews</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-list-check fa-2x mb-3"></i>
                        <h3><%= checklists %></h3>
                        <p class="mb-0">Checklists</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt fa-2x mb-3"></i>
                        <h3>100%</h3>
                        <p class="mb-0">Security Scan</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-code fa-2x mb-3"></i>
                        <h3>∞</h3>
                        <p class="mb-0">Line Analysis</p>
                    </div>
                </div>
            </div>
        </div>
        <% } %>

        <!-- Analysis Options -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">Choose Your Analysis Method</h2>
                <p class="text-center text-muted mb-5">
                    Select the type of repository analysis that best fits your needs
                </p>
            </div>
        </div>

        <div class="row g-4 mb-5">
            <!-- Public Repository Analysis -->
            <div class="col-lg-4">
                <div class="card analysis-type-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="mb-4">
                            <i class="fas fa-globe fa-4x text-success"></i>
                        </div>
                        <h4 class="card-title">Public Repository</h4>
                        <p class="card-text text-muted mb-4">
                            Analyze any public GitHub repository without authentication. 
                            Perfect for open-source projects and public contributions.
                        </p>
                        <ul class="list-unstyled text-start mb-4">
                            <li><i class="fas fa-check text-success me-2"></i>No login required</li>
                            <li><i class="fas fa-check text-success me-2"></i>Instant analysis</li>
                            <li><i class="fas fa-check text-success me-2"></i>Full line-by-line review</li>
                            <li><i class="fas fa-check text-success me-2"></i>Security scanning</li>
                        </ul>
                        <a href="/analyze?type=public" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-play me-2"></i>
                            Analyze Public Repo
                        </a>
                    </div>
                </div>
            </div>

            <!-- Private Repository Analysis -->
            <div class="col-lg-4">
                <div class="card analysis-type-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="mb-4">
                            <i class="fas fa-lock fa-4x text-primary"></i>
                        </div>
                        <h4 class="card-title">Private Repository</h4>
                        <p class="card-text text-muted mb-4">
                            Access your private repositories with GitHub OAuth. 
                            Secure authentication for enterprise and personal projects.
                        </p>
                        <ul class="list-unstyled text-start mb-4">
                            <li><i class="fas fa-check text-primary me-2"></i>GitHub OAuth login</li>
                            <li><i class="fas fa-check text-primary me-2"></i>Private repo access</li>
                            <li><i class="fas fa-check text-primary me-2"></i>Repository dropdown</li>
                            <li><i class="fas fa-check text-primary me-2"></i>Saved analysis history</li>
                        </ul>
                        <% if (user) { %>
                            <a href="/analyze?type=private" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-play me-2"></i>
                                Analyze Private Repo
                            </a>
                        <% } else { %>
                            <a href="/login" class="btn btn-outline-primary btn-lg w-100">
                                <i class="fab fa-github me-2"></i>
                                Login Required
                            </a>
                        <% } %>
                    </div>
                </div>
            </div>

            <!-- Manual Input -->
            <div class="col-lg-4">
                <div class="card analysis-type-card h-100">
                    <div class="card-body text-center p-4">
                        <div class="mb-4">
                            <i class="fas fa-keyboard fa-4x text-warning"></i>
                        </div>
                        <h4 class="card-title">Manual Input</h4>
                        <p class="card-text text-muted mb-4">
                            Direct URL and PR number entry for maximum flexibility. 
                            Works with any GitHub repository you have access to.
                        </p>
                        <ul class="list-unstyled text-start mb-4">
                            <li><i class="fas fa-check text-warning me-2"></i>Direct URL input</li>
                            <li><i class="fas fa-check text-warning me-2"></i>Custom PR numbers</li>
                            <li><i class="fas fa-check text-warning me-2"></i>Flexible access</li>
                            <li><i class="fas fa-check text-warning me-2"></i>Quick analysis</li>
                        </ul>
                        <a href="/analyze?type=manual" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-edit me-2"></i>
                            Manual Entry
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Reviews (if logged in) -->
        <% if (user && reviews.length > 0) { %>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            Recent Reviews
                        </h5>
                        <a href="/reviews" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Repository</th>
                                        <th>PR #</th>
                                        <th>Score</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% reviews.forEach(review => { %>
                                    <tr>
                                        <td>
                                            <i class="fab fa-github me-2"></i>
                                            <%= review.analysis.metadata.repository %>
                                        </td>
                                        <td>#<%= review.prNumber %></td>
                                        <td>
                                            <span class="badge bg-<%= review.analysis.summary.percentage >= 80 ? 'success' : review.analysis.summary.percentage >= 60 ? 'warning' : 'danger' %>">
                                                <%= review.analysis.summary.percentage %>%
                                            </span>
                                        </td>
                                        <td><%= new Date(review.createdAt).toLocaleDateString() %></td>
                                        <td>
                                            <a href="/results/<%= review.id %>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye me-1"></i>
                                                View Results
                                            </a>
                                        </td>
                                    </tr>
                                    <% }); %>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <% } %>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>Enhanced PR Review Tool v2.0</h6>
                    <p class="small mb-0">Comprehensive line-by-line analysis with customizable checklists</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="small mb-0">
                        <i class="fas fa-shield-alt me-1"></i>
                        Security Focused • 
                        <i class="fas fa-code me-1"></i>
                        Line-by-Line Analysis • 
                        <i class="fas fa-list-check me-1"></i>
                        Custom Checklists
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
