<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-check me-2 text-primary"></i>
                <span class="fw-bold">CodeReview Pro</span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="javascript:history.back()">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back
                </a>
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>
                    Dashboard
                </a>
                <% if (user) { %>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <img src="<%= user.avatar_url %>" alt="Avatar" class="rounded-circle me-2" width="24" height="24">
                        <%= user.login %>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/settings">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/auth/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a></li>
                    </ul>
                </div>
                <% } %>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h2">
                        <i class="fas fa-cog me-2"></i>
                        Settings
                    </h1>
                </div>

                <!-- User Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user me-2"></i>
                            User Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <img src="<%= user.avatar_url %>" alt="Avatar" class="rounded-circle img-fluid">
                            </div>
                            <div class="col-md-10">
                                <h6><%= user.name || user.login %></h6>
                                <p class="text-muted mb-1">@<%= user.login %></p>
                                <% if (user.email) { %>
                                <p class="text-muted mb-1">
                                    <i class="fas fa-envelope me-1"></i>
                                    <%= user.email %>
                                </p>
                                <% } %>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-clock me-1"></i>
                                    Last login: <%= new Date(user.loginTime).toLocaleString() %>
                                </p>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-key me-1"></i>
                                    Auth method: <%= user.authMethod %>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Token Management -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Personal Access Token Management
                        </h5>
                    </div>
                    <div class="card-body">
                        <% if (tokenInfo.hasToken) { %>
                        <!-- Token exists -->
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Token Stored Securely</h6>
                            <p class="mb-2">Your Personal Access Token is stored and encrypted locally.</p>
                            <div class="small">
                                <div><strong>Created:</strong> <%= new Date(tokenInfo.createdAt).toLocaleString() %></div>
                                <div><strong>Last Used:</strong> <%= new Date(tokenInfo.lastUsed).toLocaleString() %></div>
                                <div><strong>Auto-login:</strong> <%= tokenInfo.remember ? 'Enabled' : 'Disabled' %></div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-warning w-100 mb-2" onclick="showUpdateTokenModal()">
                                    <i class="fas fa-edit me-2"></i>
                                    Update Token
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-danger w-100 mb-2" onclick="removeToken()">
                                    <i class="fas fa-trash me-2"></i>
                                    Remove Token
                                </button>
                            </div>
                        </div>
                        <% } else { %>
                        <!-- No token stored -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>No Token Stored</h6>
                            <p class="mb-0">You can store your Personal Access Token for automatic authentication.</p>
                        </div>

                        <button type="button" class="btn btn-primary" onclick="showAddTokenModal()">
                            <i class="fas fa-plus me-2"></i>
                            Add Personal Access Token
                        </button>
                        <% } %>

                        <hr>
                        <div class="small text-muted">
                            <h6>Security Information:</h6>
                            <ul class="mb-0">
                                <li>Tokens are encrypted using base64 encoding before storage</li>
                                <li>Tokens are stored locally on the server, not in browser cookies</li>
                                <li>You can revoke access anytime from your GitHub settings</li>
                                <li>Removing the token here doesn't revoke it on GitHub</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Application Information -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Application Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Authentication Methods</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Personal Access Token</li>
                                    <li><i class="fas fa-check text-success me-2"></i>OAuth Web Flow</li>
                                    <li><i class="fas fa-info text-info me-2"></i>Device Flow (if enabled)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Features Available</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Private Repository Access</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Organization Repositories</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Custom Checklists</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Analysis History</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Token Modal -->
    <div class="modal fade" id="updateTokenModal" tabindex="-1" aria-labelledby="updateTokenModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateTokenModalLabel">
                        <i class="fas fa-edit me-2"></i>
                        Update Personal Access Token
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="updateTokenForm">
                        <div class="mb-3">
                            <label for="newToken" class="form-label">New GitHub Personal Access Token</label>
                            <input type="password" class="form-control" id="newToken" 
                                   placeholder="ghp_xxxxxxxxxxxxxxxxxxxx" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Token needs 'repo' and 'user:email' scopes
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="rememberNewToken" checked>
                                <label class="form-check-label" for="rememberNewToken">
                                    <i class="fas fa-save me-1"></i>
                                    Enable automatic login with this token
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Update Token
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Token Modal -->
    <div class="modal fade" id="addTokenModal" tabindex="-1" aria-labelledby="addTokenModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTokenModalLabel">
                        <i class="fas fa-plus me-2"></i>
                        Add Personal Access Token
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addTokenForm">
                        <div class="mb-3">
                            <label for="addToken" class="form-label">GitHub Personal Access Token</label>
                            <input type="password" class="form-control" id="addToken" 
                                   placeholder="ghp_xxxxxxxxxxxxxxxxxxxx" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Token needs 'repo' and 'user:email' scopes
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="rememberAddToken" checked>
                                <label class="form-check-label" for="rememberAddToken">
                                    <i class="fas fa-save me-1"></i>
                                    Enable automatic login with this token
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Save Token
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-3">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>How to create a token:</h6>
                            <ol class="mb-0 small">
                                <li>Go to GitHub Settings → Developer settings → Personal access tokens</li>
                                <li>Click "Generate new token (classic)"</li>
                                <li>Select scopes: <code>repo</code> and <code>user:email</code></li>
                                <li>Copy the generated token and paste it above</li>
                            </ol>
                            <a href="https://github.com/settings/tokens" target="_blank" class="btn btn-sm btn-outline-primary mt-2">
                                <i class="fas fa-external-link-alt me-1"></i>
                                Create Token
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Show update token modal
        function showUpdateTokenModal() {
            const modal = new bootstrap.Modal(document.getElementById('updateTokenModal'));
            modal.show();
        }

        // Show add token modal
        function showAddTokenModal() {
            const modal = new bootstrap.Modal(document.getElementById('addTokenModal'));
            modal.show();
        }

        // Remove token
        async function removeToken() {
            if (!confirm('Are you sure you want to remove the stored token? You will need to re-authenticate manually.')) {
                return;
            }

            try {
                const response = await fetch('/api/user/token', {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    location.reload();
                } else {
                    alert('Failed to remove token: ' + result.error);
                }
            } catch (error) {
                alert('Error removing token: ' + error.message);
            }
        }

        // Handle update token form
        document.getElementById('updateTokenForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const token = document.getElementById('newToken').value;
            const remember = document.getElementById('rememberNewToken').checked;
            
            try {
                const response = await fetch('/api/user/token/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token, remember })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    location.reload();
                } else {
                    alert('Failed to update token: ' + result.error);
                }
            } catch (error) {
                alert('Error updating token: ' + error.message);
            }
        });

        // Handle add token form
        document.getElementById('addTokenForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const token = document.getElementById('addToken').value;
            const remember = document.getElementById('rememberAddToken').checked;
            
            try {
                const response = await fetch('/api/user/token/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token, remember })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    location.reload();
                } else {
                    alert('Failed to save token: ' + result.error);
                }
            } catch (error) {
                alert('Error saving token: ' + error.message);
            }
        });
    </script>
</body>
</html>
