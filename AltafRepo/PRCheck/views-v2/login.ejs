<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .login-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        .access-method {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        .access-method:hover {
            border-color: #007bff;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <section class="login-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="card login-card">
                        <div class="card-body p-5">
                            <!-- Header -->
                            <div class="text-center mb-5">
                                <h1 class="display-5 fw-bold text-dark mb-3">
                                    <i class="fas fa-code-branch me-3"></i>
                                    PR Review Tool v2.0
                                </h1>
                                <p class="lead text-muted">
                                    Choose your access method to start analyzing pull requests
                                </p>
                            </div>

                            <!-- Error Message -->
                            <% if (error) { %>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <% if (error === 'github_not_configured') { %>
                                    GitHub OAuth is not configured. Please contact the administrator.
                                <% } else if (error === 'invalid_oauth_state') { %>
                                    Invalid OAuth state. Please try logging in again.
                                <% } else if (error === 'oauth_failed') { %>
                                    GitHub authentication failed. Please try again.
                                <% } else { %>
                                    <%= error %>
                                <% } %>
                            </div>
                            <% } %>

                            <!-- Access Methods -->
                            <div class="row g-4">
                                <!-- Public Repository Access -->
                                <div class="col-lg-4">
                                    <div class="card access-method h-100" onclick="window.location.href='/analyze?type=public'">
                                        <div class="card-body text-center p-4">
                                            <div class="feature-icon bg-success text-white">
                                                <i class="fas fa-globe fa-2x"></i>
                                            </div>
                                            <h4 class="card-title">Public Repositories</h4>
                                            <p class="card-text text-muted mb-4">
                                                Analyze any public GitHub repository without authentication
                                            </p>
                                            <ul class="list-unstyled text-start mb-4">
                                                <li><i class="fas fa-check text-success me-2"></i>No login required</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Instant access</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Full analysis features</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Line-by-line review</li>
                                            </ul>
                                            <button class="btn btn-success btn-lg w-100">
                                                <i class="fas fa-play me-2"></i>
                                                Start Public Analysis
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Private Repository Access -->
                                <div class="col-lg-4">
                                    <div class="card access-method h-100">
                                        <div class="card-body text-center p-4">
                                            <div class="feature-icon bg-primary text-white">
                                                <i class="fab fa-github fa-2x"></i>
                                            </div>
                                            <h4 class="card-title">Private Repositories</h4>
                                            <p class="card-text text-muted mb-4">
                                                Access your private repositories with multiple authentication options
                                            </p>
                                            <ul class="list-unstyled text-start mb-4">
                                                <li><i class="fas fa-check text-primary me-2"></i>Multiple auth methods</li>
                                                <li><i class="fas fa-check text-primary me-2"></i>Private repo access</li>
                                                <li><i class="fas fa-check text-primary me-2"></i>Analysis history</li>
                                                <li><i class="fas fa-check text-primary me-2"></i>Custom checklists</li>
                                            </ul>

                                            <!-- Authentication Options Dropdown -->
                                            <div class="dropdown">
                                                <button class="btn btn-primary btn-lg w-100 dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fab fa-github me-2"></i>
                                                    Choose Auth Method
                                                </button>
                                                <ul class="dropdown-menu w-100">
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="showDeviceFlow()">
                                                            <i class="fas fa-mobile-alt me-2"></i>
                                                            Device Flow (CLI-friendly)
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="showTokenAuth()">
                                                            <i class="fas fa-key me-2"></i>
                                                            Personal Access Token
                                                        </a>
                                                    </li>
                                                    <% if (githubConfigured) { %>
                                                    <li>
                                                        <a class="dropdown-item" href="/auth/github">
                                                            <i class="fas fa-globe me-2"></i>
                                                            OAuth Web Flow
                                                        </a>
                                                    </li>
                                                    <% } %>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Manual Input -->
                                <div class="col-lg-4">
                                    <div class="card access-method h-100" onclick="window.location.href='/analyze?type=manual'">
                                        <div class="card-body text-center p-4">
                                            <div class="feature-icon bg-warning text-white">
                                                <i class="fas fa-keyboard fa-2x"></i>
                                            </div>
                                            <h4 class="card-title">Manual Input</h4>
                                            <p class="card-text text-muted mb-4">
                                                Direct URL and PR number entry for maximum flexibility
                                            </p>
                                            <ul class="list-unstyled text-start mb-4">
                                                <li><i class="fas fa-check text-warning me-2"></i>Direct URL input</li>
                                                <li><i class="fas fa-check text-warning me-2"></i>Any repository</li>
                                                <li><i class="fas fa-check text-warning me-2"></i>Quick setup</li>
                                                <li><i class="fas fa-check text-warning me-2"></i>Flexible access</li>
                                            </ul>
                                            <button class="btn btn-warning btn-lg w-100">
                                                <i class="fas fa-edit me-2"></i>
                                                Manual Entry
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Features Section -->
                            <div class="mt-5 pt-5 border-top">
                                <h3 class="text-center mb-4">What You Get</h3>
                                <div class="row g-4">
                                    <div class="col-md-3 text-center">
                                        <i class="fas fa-code fa-3x text-primary mb-3"></i>
                                        <h6>Line-by-Line Analysis</h6>
                                        <p class="small text-muted">Detailed review of every changed line with specific feedback</p>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                                        <h6>Security Scanning</h6>
                                        <p class="small text-muted">Automatic detection of security vulnerabilities</p>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <i class="fas fa-list-check fa-3x text-warning mb-3"></i>
                                        <h6>Custom Checklists</h6>
                                        <p class="small text-muted">Configurable analysis rules for your team</p>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                                        <h6>Quality Metrics</h6>
                                        <p class="small text-muted">Comprehensive code quality assessment</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Start -->
                            <div class="mt-5 p-4 bg-light rounded">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="mb-2">
                                            <i class="fas fa-rocket me-2"></i>
                                            Quick Start
                                        </h5>
                                        <p class="mb-0 text-muted">
                                            New to PR reviews? Start with a public repository to see how our comprehensive analysis works.
                                        </p>
                                    </div>
                                    <div class="col-md-4 text-md-end">
                                        <a href="/analyze?type=public" class="btn btn-primary">
                                            <i class="fas fa-play me-2"></i>
                                            Try Public Analysis
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Footer -->
                            <div class="text-center mt-4">
                                <p class="small text-muted mb-0">
                                    Enhanced PR Review Tool v2.0 • 
                                    <a href="/" class="text-decoration-none">Dashboard</a> • 
                                    <a href="/checklists" class="text-decoration-none">Manage Checklists</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Device Flow Modal -->
    <div class="modal fade" id="deviceFlowModal" tabindex="-1" aria-labelledby="deviceFlowModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deviceFlowModalLabel">
                        <i class="fas fa-mobile-alt me-2"></i>
                        GitHub Device Flow Authentication
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="deviceFlowStep1">
                        <div class="text-center mb-4">
                            <p class="lead">Perfect for CLI usage - no client secret required!</p>
                        </div>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary btn-lg" onclick="startDeviceFlow()">
                                <i class="fas fa-play me-2"></i>
                                Start Device Authentication
                            </button>
                        </div>
                    </div>

                    <div id="deviceFlowStep2" class="d-none">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Follow these steps:</h6>
                            <ol class="mb-0">
                                <li>Copy the verification code below</li>
                                <li>Click "Open GitHub" to visit the verification page</li>
                                <li>Enter the code and authorize the application</li>
                                <li>Return here - we'll automatically detect completion</li>
                            </ol>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Verification Code:</label>
                                <div class="input-group">
                                    <input type="text" class="form-control text-center fw-bold fs-4"
                                           id="userCode" readonly>
                                    <button class="btn btn-outline-secondary" onclick="copyCode()">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Verification URL:</label>
                                <div class="d-grid">
                                    <a id="verificationUrl" href="#" target="_blank" class="btn btn-success">
                                        <i class="fas fa-external-link-alt me-2"></i>
                                        Open GitHub
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <div class="spinner-border spinner-border-sm me-2" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span id="deviceFlowStatus">Waiting for authorization...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Token Auth Modal -->
    <div class="modal fade" id="tokenAuthModal" tabindex="-1" aria-labelledby="tokenAuthModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tokenAuthModalLabel">
                        <i class="fas fa-key me-2"></i>
                        Personal Access Token Authentication
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="tokenForm">
                        <div class="mb-3">
                            <label for="personalToken" class="form-label">GitHub Personal Access Token</label>
                            <input type="password" class="form-control" id="personalToken"
                                   placeholder="ghp_xxxxxxxxxxxxxxxxxxxx" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Token needs 'repo' and 'user:email' scopes
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Authenticate with Token
                            </button>
                        </div>
                    </form>

                    <div class="mt-3">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>How to create a token:</h6>
                            <ol class="mb-0 small">
                                <li>Go to GitHub Settings → Developer settings → Personal access tokens</li>
                                <li>Click "Generate new token (classic)"</li>
                                <li>Select scopes: <code>repo</code> and <code>user:email</code></li>
                                <li>Copy the generated token and paste it above</li>
                            </ol>
                            <a href="https://github.com/settings/tokens" target="_blank" class="btn btn-sm btn-outline-primary mt-2">
                                <i class="fas fa-external-link-alt me-1"></i>
                                Create Token
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let deviceCode = null;
        let pollingInterval = null;

        // Show Device Flow Modal
        function showDeviceFlow() {
            const modal = new bootstrap.Modal(document.getElementById('deviceFlowModal'));
            modal.show();
        }

        // Show Token Auth Modal
        function showTokenAuth() {
            const modal = new bootstrap.Modal(document.getElementById('tokenAuthModal'));
            modal.show();
        }

        // Start Device Flow
        async function startDeviceFlow() {
            try {
                const response = await fetch('/auth/github/device', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    deviceCode = result.device_code;
                    document.getElementById('userCode').value = result.user_code;
                    document.getElementById('verificationUrl').href = result.verification_uri;

                    // Show step 2
                    document.getElementById('deviceFlowStep1').classList.add('d-none');
                    document.getElementById('deviceFlowStep2').classList.remove('d-none');

                    // Start polling
                    startPolling(result.interval * 1000);
                } else {
                    alert('Failed to start device flow: ' + result.error);
                }
            } catch (error) {
                alert('Error starting device flow: ' + error.message);
            }
        }

        // Start polling for token
        function startPolling(interval) {
            pollingInterval = setInterval(async () => {
                try {
                    const response = await fetch('/auth/github/device/poll', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ device_code: deviceCode })
                    });

                    const result = await response.json();

                    if (result.success) {
                        clearInterval(pollingInterval);
                        document.getElementById('deviceFlowStatus').textContent = 'Authentication successful!';
                        setTimeout(() => {
                            window.location.href = '/analyze?type=private';
                        }, 1000);
                    } else if (!result.pending) {
                        clearInterval(pollingInterval);
                        document.getElementById('deviceFlowStatus').textContent = 'Authentication failed: ' + result.error;
                    }
                } catch (error) {
                    clearInterval(pollingInterval);
                    document.getElementById('deviceFlowStatus').textContent = 'Polling error: ' + error.message;
                }
            }, interval);
        }

        // Copy verification code
        function copyCode() {
            const codeInput = document.getElementById('userCode');
            codeInput.select();
            document.execCommand('copy');

            // Show feedback
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
                button.innerHTML = originalHTML;
            }, 1000);
        }

        // Handle token form submission
        document.getElementById('tokenForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const token = document.getElementById('personalToken').value;

            try {
                const response = await fetch('/auth/github/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ token })
                });

                const result = await response.json();

                if (result.success) {
                    window.location.href = '/analyze?type=private';
                } else {
                    alert('Authentication failed: ' + result.error);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        });

        // Clean up polling on page unload
        window.addEventListener('beforeunload', () => {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
        });
    </script>
</body>
</html>
