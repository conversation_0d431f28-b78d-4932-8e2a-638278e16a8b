<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .login-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
        .access-method {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        .access-method:hover {
            border-color: #007bff;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <section class="login-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="card login-card">
                        <div class="card-body p-5">
                            <!-- Header -->
                            <div class="text-center mb-5">
                                <h1 class="display-5 fw-bold text-dark mb-3">
                                    <i class="fas fa-code-branch me-3"></i>
                                    PR Review Tool v2.0
                                </h1>
                                <p class="lead text-muted">
                                    Choose your access method to start analyzing pull requests
                                </p>
                            </div>

                            <!-- Error Message -->
                            <% if (error) { %>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <% if (error === 'github_not_configured') { %>
                                    GitHub OAuth is not configured. Please contact the administrator.
                                <% } else if (error === 'invalid_oauth_state') { %>
                                    Invalid OAuth state. Please try logging in again.
                                <% } else if (error === 'oauth_failed') { %>
                                    GitHub authentication failed. Please try again.
                                <% } else { %>
                                    <%= error %>
                                <% } %>
                            </div>
                            <% } %>

                            <!-- Access Methods -->
                            <div class="row g-4">
                                <!-- Public Repository Access -->
                                <div class="col-lg-4">
                                    <div class="card access-method h-100" onclick="window.location.href='/analyze?type=public'">
                                        <div class="card-body text-center p-4">
                                            <div class="feature-icon bg-success text-white">
                                                <i class="fas fa-globe fa-2x"></i>
                                            </div>
                                            <h4 class="card-title">Public Repositories</h4>
                                            <p class="card-text text-muted mb-4">
                                                Analyze any public GitHub repository without authentication
                                            </p>
                                            <ul class="list-unstyled text-start mb-4">
                                                <li><i class="fas fa-check text-success me-2"></i>No login required</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Instant access</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Full analysis features</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Line-by-line review</li>
                                            </ul>
                                            <button class="btn btn-success btn-lg w-100">
                                                <i class="fas fa-play me-2"></i>
                                                Start Public Analysis
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Private Repository Access -->
                                <div class="col-lg-4">
                                    <div class="card access-method h-100">
                                        <div class="card-body text-center p-4">
                                            <div class="feature-icon bg-primary text-white">
                                                <i class="fab fa-github fa-2x"></i>
                                            </div>
                                            <h4 class="card-title">Private Repositories</h4>
                                            <p class="card-text text-muted mb-4">
                                                Access your private repositories with secure GitHub OAuth
                                            </p>
                                            <ul class="list-unstyled text-start mb-4">
                                                <li><i class="fas fa-check text-primary me-2"></i>Secure OAuth login</li>
                                                <li><i class="fas fa-check text-primary me-2"></i>Private repo access</li>
                                                <li><i class="fas fa-check text-primary me-2"></i>Analysis history</li>
                                                <li><i class="fas fa-check text-primary me-2"></i>Custom checklists</li>
                                            </ul>
                                            <% if (githubConfigured) { %>
                                                <a href="/auth/github" class="btn btn-primary btn-lg w-100">
                                                    <i class="fab fa-github me-2"></i>
                                                    Login with GitHub
                                                </a>
                                            <% } else { %>
                                                <button class="btn btn-outline-secondary btn-lg w-100" disabled>
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    OAuth Not Configured
                                                </button>
                                            <% } %>
                                        </div>
                                    </div>
                                </div>

                                <!-- Manual Input -->
                                <div class="col-lg-4">
                                    <div class="card access-method h-100" onclick="window.location.href='/analyze?type=manual'">
                                        <div class="card-body text-center p-4">
                                            <div class="feature-icon bg-warning text-white">
                                                <i class="fas fa-keyboard fa-2x"></i>
                                            </div>
                                            <h4 class="card-title">Manual Input</h4>
                                            <p class="card-text text-muted mb-4">
                                                Direct URL and PR number entry for maximum flexibility
                                            </p>
                                            <ul class="list-unstyled text-start mb-4">
                                                <li><i class="fas fa-check text-warning me-2"></i>Direct URL input</li>
                                                <li><i class="fas fa-check text-warning me-2"></i>Any repository</li>
                                                <li><i class="fas fa-check text-warning me-2"></i>Quick setup</li>
                                                <li><i class="fas fa-check text-warning me-2"></i>Flexible access</li>
                                            </ul>
                                            <button class="btn btn-warning btn-lg w-100">
                                                <i class="fas fa-edit me-2"></i>
                                                Manual Entry
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Features Section -->
                            <div class="mt-5 pt-5 border-top">
                                <h3 class="text-center mb-4">What You Get</h3>
                                <div class="row g-4">
                                    <div class="col-md-3 text-center">
                                        <i class="fas fa-code fa-3x text-primary mb-3"></i>
                                        <h6>Line-by-Line Analysis</h6>
                                        <p class="small text-muted">Detailed review of every changed line with specific feedback</p>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                                        <h6>Security Scanning</h6>
                                        <p class="small text-muted">Automatic detection of security vulnerabilities</p>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <i class="fas fa-list-check fa-3x text-warning mb-3"></i>
                                        <h6>Custom Checklists</h6>
                                        <p class="small text-muted">Configurable analysis rules for your team</p>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                                        <h6>Quality Metrics</h6>
                                        <p class="small text-muted">Comprehensive code quality assessment</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Start -->
                            <div class="mt-5 p-4 bg-light rounded">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="mb-2">
                                            <i class="fas fa-rocket me-2"></i>
                                            Quick Start
                                        </h5>
                                        <p class="mb-0 text-muted">
                                            New to PR reviews? Start with a public repository to see how our comprehensive analysis works.
                                        </p>
                                    </div>
                                    <div class="col-md-4 text-md-end">
                                        <a href="/analyze?type=public" class="btn btn-primary">
                                            <i class="fas fa-play me-2"></i>
                                            Try Public Analysis
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Footer -->
                            <div class="text-center mt-4">
                                <p class="small text-muted mb-0">
                                    Enhanced PR Review Tool v2.0 • 
                                    <a href="/" class="text-decoration-none">Dashboard</a> • 
                                    <a href="/checklists" class="text-decoration-none">Manage Checklists</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
