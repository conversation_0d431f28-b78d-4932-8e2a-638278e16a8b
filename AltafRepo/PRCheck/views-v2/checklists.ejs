<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .checklist-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .checklist-card {
            transition: all 0.3s ease;
            border-left: 4px solid #007bff;
        }
        .checklist-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .rule-item {
            background-color: #f8f9fa;
            border-left: 3px solid #28a745;
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 0 0.375rem 0.375rem 0;
        }
        .modal-lg {
            max-width: 800px;
        }
        .severity-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-code-branch me-2"></i>
                PR Review Tool v2.0
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>
                    Dashboard
                </a>
                <a class="nav-link" href="/analyze">
                    <i class="fas fa-search me-1"></i>
                    Analyze PR
                </a>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <section class="checklist-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-6 mb-3">
                        <i class="fas fa-list-check me-2"></i>
                        Manage Checklists
                    </h1>
                    <p class="lead mb-0">
                        Create and customize analysis checklists for your PR reviews
                    </p>
                </div>
                <div class="col-lg-4 text-end">
                    <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#createChecklistModal">
                        <i class="fas fa-plus me-2"></i>
                        Create New Checklist
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Checklists Grid -->
        <div class="row g-4" id="checklistsGrid">
            <% Object.values(checklists).forEach(checklist => { %>
            <div class="col-lg-6">
                <div class="card checklist-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            <%= checklist.name %>
                        </h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                    type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="#" onclick="editChecklist('<%= checklist.id %>')">
                                        <i class="fas fa-edit me-2"></i>Edit
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="duplicateChecklist('<%= checklist.id %>')">
                                        <i class="fas fa-copy me-2"></i>Duplicate
                                    </a>
                                </li>
                                <% if (checklist.id !== 'default') { %>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="#" onclick="deleteChecklist('<%= checklist.id %>')">
                                        <i class="fas fa-trash me-2"></i>Delete
                                    </a>
                                </li>
                                <% } %>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3"><%= checklist.description %></p>
                        
                        <!-- Categories -->
                        <% if (checklist.checks && checklist.checks.length > 0) { %>
                            <h6 class="mb-3">Categories (<%= checklist.checks.length %>)</h6>
                            <% checklist.checks.forEach(category => { %>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <strong class="text-primary">
                                        <i class="fas fa-folder me-2"></i>
                                        <%= category.name %>
                                    </strong>
                                    <span class="badge bg-secondary">
                                        <%= category.rules ? category.rules.length : 0 %> rules
                                    </span>
                                </div>
                                <p class="small text-muted mb-2"><%= category.description %></p>
                                
                                <!-- Sample Rules -->
                                <% if (category.rules && category.rules.length > 0) { %>
                                    <% category.rules.slice(0, 2).forEach(rule => { %>
                                    <div class="rule-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <strong class="small"><%= rule.name %></strong>
                                                <p class="small text-muted mb-0"><%= rule.description %></p>
                                            </div>
                                            <span class="badge severity-badge bg-<%= getSeverityColor(rule.severity) %>">
                                                <%= rule.severity %>
                                            </span>
                                        </div>
                                    </div>
                                    <% }); %>
                                    <% if (category.rules.length > 2) { %>
                                    <div class="small text-muted">
                                        ... and <%= category.rules.length - 2 %> more rules
                                    </div>
                                    <% } %>
                                <% } %>
                            </div>
                            <% }); %>
                        <% } else { %>
                            <div class="text-center py-3">
                                <i class="fas fa-info-circle text-muted fa-2x mb-2"></i>
                                <p class="text-muted mb-0">No categories defined</p>
                            </div>
                        <% } %>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <% if (checklist.createdAt) { %>
                                    Created <%= new Date(checklist.createdAt).toLocaleDateString() %>
                                <% } else { %>
                                    Default checklist
                                <% } %>
                            </small>
                            <a href="/analyze?checklist=<%= checklist.id %>" class="btn btn-primary btn-sm">
                                <i class="fas fa-play me-1"></i>
                                Use for Analysis
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <% }); %>
        </div>

        <!-- Empty State -->
        <% if (Object.keys(checklists).length === 0) { %>
        <div class="text-center py-5">
            <i class="fas fa-list-check fa-4x text-muted mb-4"></i>
            <h4>No Custom Checklists</h4>
            <p class="text-muted mb-4">Create your first custom checklist to get started</p>
            <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#createChecklistModal">
                <i class="fas fa-plus me-2"></i>
                Create Your First Checklist
            </button>
        </div>
        <% } %>
    </div>

    <!-- Create/Edit Checklist Modal -->
    <div class="modal fade" id="createChecklistModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        <span id="modalTitle">Create New Checklist</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="checklistForm">
                        <input type="hidden" id="checklistId" name="id">
                        
                        <!-- Basic Info -->
                        <div class="mb-3">
                            <label for="checklistName" class="form-label">Checklist Name</label>
                            <input type="text" class="form-control" id="checklistName" name="name" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="checklistDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="checklistDescription" name="description" rows="2"></textarea>
                        </div>

                        <!-- Categories -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Categories</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addCategory()">
                                    <i class="fas fa-plus me-1"></i>Add Category
                                </button>
                            </div>
                            <div id="categoriesContainer">
                                <!-- Categories will be added here dynamically -->
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveChecklist()">
                        <i class="fas fa-save me-2"></i>
                        Save Checklist
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let categoryCounter = 0;
        let ruleCounter = 0;

        // Helper function for severity colors
        function getSeverityColor(severity) {
            switch (severity) {
                case 'critical': return 'danger';
                case 'high': return 'warning';
                case 'medium': return 'info';
                case 'low': return 'secondary';
                default: return 'secondary';
            }
        }

        // Add category to form
        function addCategory() {
            const container = document.getElementById('categoriesContainer');
            const categoryId = `category_${categoryCounter++}`;
            
            const categoryHTML = `
                <div class="card mb-3" id="${categoryId}">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Category ${categoryCounter}</h6>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCategory('${categoryId}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Category Name</label>
                                <input type="text" class="form-control category-name" placeholder="e.g., Security Checks">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Description</label>
                                <input type="text" class="form-control category-description" placeholder="Brief description">
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">Rules</label>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="addRule('${categoryId}')">
                                    <i class="fas fa-plus me-1"></i>Add Rule
                                </button>
                            </div>
                            <div class="rules-container"></div>
                        </div>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', categoryHTML);
        }

        // Add rule to category
        function addRule(categoryId) {
            const category = document.getElementById(categoryId);
            const rulesContainer = category.querySelector('.rules-container');
            const ruleId = `rule_${ruleCounter++}`;
            
            const ruleHTML = `
                <div class="border rounded p-3 mb-2" id="${ruleId}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">Rule ${ruleCounter}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeRule('${ruleId}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label small">Rule Name</label>
                            <input type="text" class="form-control form-control-sm rule-name" placeholder="e.g., No Hardcoded Passwords">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label small">Severity</label>
                            <select class="form-select form-select-sm rule-severity">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="critical">Critical</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-2">
                        <label class="form-label small">Description</label>
                        <textarea class="form-control form-control-sm rule-description" rows="2" placeholder="Describe what this rule checks for"></textarea>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <label class="form-label small">Pass Message</label>
                            <input type="text" class="form-control form-control-sm rule-pass-message" placeholder="Message when rule passes">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label small">Fail Message</label>
                            <input type="text" class="form-control form-control-sm rule-fail-message" placeholder="Message when rule fails">
                        </div>
                    </div>
                </div>
            `;
            
            rulesContainer.insertAdjacentHTML('beforeend', ruleHTML);
        }

        // Remove category
        function removeCategory(categoryId) {
            document.getElementById(categoryId).remove();
        }

        // Remove rule
        function removeRule(ruleId) {
            document.getElementById(ruleId).remove();
        }

        // Save checklist
        async function saveChecklist() {
            const form = document.getElementById('checklistForm');
            const formData = new FormData(form);
            
            const checklist = {
                name: formData.get('name'),
                description: formData.get('description'),
                checks: []
            };
            
            // Collect categories
            document.querySelectorAll('#categoriesContainer .card').forEach(categoryCard => {
                const category = {
                    name: categoryCard.querySelector('.category-name').value,
                    description: categoryCard.querySelector('.category-description').value,
                    rules: []
                };
                
                // Collect rules
                categoryCard.querySelectorAll('.rules-container > div').forEach(ruleDiv => {
                    const rule = {
                        name: ruleDiv.querySelector('.rule-name').value,
                        description: ruleDiv.querySelector('.rule-description').value,
                        severity: ruleDiv.querySelector('.rule-severity').value,
                        passMessage: ruleDiv.querySelector('.rule-pass-message').value,
                        failMessage: ruleDiv.querySelector('.rule-fail-message').value
                    };
                    
                    if (rule.name) {
                        category.rules.push(rule);
                    }
                });
                
                if (category.name) {
                    checklist.checks.push(category);
                }
            });
            
            try {
                const checklistId = document.getElementById('checklistId').value;
                const url = checklistId ? `/api/checklists/${checklistId}` : '/api/checklists';
                const method = checklistId ? 'PUT' : 'POST';
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(checklist)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    location.reload();
                } else {
                    alert('Failed to save checklist: ' + result.error);
                }
            } catch (error) {
                alert('Failed to save checklist: ' + error.message);
            }
        }

        // Edit checklist
        function editChecklist(checklistId) {
            // Implementation for editing existing checklist
            console.log('Edit checklist:', checklistId);
        }

        // Duplicate checklist
        function duplicateChecklist(checklistId) {
            // Implementation for duplicating checklist
            console.log('Duplicate checklist:', checklistId);
        }

        // Delete checklist
        async function deleteChecklist(checklistId) {
            if (confirm('Are you sure you want to delete this checklist?')) {
                try {
                    const response = await fetch(`/api/checklists/${checklistId}`, {
                        method: 'DELETE'
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        location.reload();
                    } else {
                        alert('Failed to delete checklist: ' + result.error);
                    }
                } catch (error) {
                    alert('Failed to delete checklist: ' + error.message);
                }
            }
        }

        // Initialize with one category
        document.addEventListener('DOMContentLoaded', function() {
            addCategory();
            addRule('category_0');
        });
    </script>
</body>
</html>

<%
function getSeverityColor(severity) {
    switch (severity) {
        case 'critical': return 'danger';
        case 'high': return 'warning';
        case 'medium': return 'info';
        case 'low': return 'secondary';
        default: return 'secondary';
    }
}
%>
