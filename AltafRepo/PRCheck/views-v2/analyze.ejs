<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .analysis-form {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        .form-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .checklist-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .checklist-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .checklist-card.selected {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            z-index: 9999;
        }
        .loading-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
        }
        .progress-step {
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }
        .progress-step.active {
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-check me-2 text-primary"></i>
                <span class="fw-bold">CodeReview Pro</span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </nav>

    <!-- Analysis Form Section -->
    <section class="analysis-form">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card form-card">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                <h2 class="text-dark mb-3">
                                    <i class="fas fa-search me-2"></i>
                                    Analyze Pull Request
                                </h2>
                                <p class="text-muted">
                                    <% if (type === 'public') { %>
                                        Analyzing public repository - no authentication required
                                    <% } else if (type === 'private') { %>
                                        Analyzing private repository - authenticated access
                                    <% } else { %>
                                        Manual repository input - flexible analysis
                                    <% } %>
                                </p>
                            </div>

                            <form id="analysisForm">
                                <!-- Repository Selection -->
                                <div class="mb-4">
                                    <label class="form-label text-dark fw-bold">
                                        <i class="fab fa-github me-2"></i>
                                        Repository Selection
                                    </label>

                                    <!-- Repository Selection Tabs -->
                                    <ul class="nav nav-tabs mb-3" id="repoTabs" role="tablist">
                                        <% if (user && type === 'private') { %>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="dropdown-tab" data-bs-toggle="tab"
                                                    data-bs-target="#dropdown-pane" type="button" role="tab">
                                                <i class="fas fa-list me-1"></i>
                                                My Repositories
                                            </button>
                                        </li>
                                        <% } %>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link <%= !user || type !== 'private' ? 'active' : '' %>"
                                                    id="manual-tab" data-bs-toggle="tab"
                                                    data-bs-target="#manual-pane" type="button" role="tab">
                                                <i class="fas fa-keyboard me-1"></i>
                                                Manual Entry
                                            </button>
                                        </li>
                                    </ul>

                                    <!-- Tab Content -->
                                    <div class="tab-content" id="repoTabContent">
                                        <!-- Repository Dropdown Tab -->
                                        <% if (user && type === 'private') { %>
                                        <div class="tab-pane fade show active" id="dropdown-pane" role="tabpanel">
                                            <div class="mb-3">
                                                <input type="text" class="form-control" id="repoSearch"
                                                       placeholder="Search repositories...">
                                            </div>
                                            <div class="mb-3">
                                                <select class="form-select" id="repoTypeFilter">
                                                    <option value="all">All Repositories</option>
                                                    <option value="owned">Owned</option>
                                                    <option value="forked">Forked</option>
                                                    <option value="organization">Organization</option>
                                                    <option value="collaborator">Collaborator</option>
                                                    <option value="private">Private Only</option>
                                                    <option value="public">Public Only</option>
                                                </select>
                                            </div>
                                            <div id="repoStats" class="mb-3">
                                                <!-- Repository statistics will be displayed here -->
                                            </div>
                                            <div class="border rounded" style="max-height: 300px; overflow-y: auto;">
                                                <div id="repositoryList" class="list-group list-group-flush">
                                                    <div class="list-group-item text-center py-4">
                                                        <div class="spinner-border spinner-border-sm me-2" role="status">
                                                            <span class="visually-hidden">Loading...</span>
                                                        </div>
                                                        Loading repositories...
                                                    </div>
                                                </div>
                                            </div>
                                            <input type="hidden" id="selectedRepoUrl" name="repoUrl">
                                        </div>
                                        <% } %>

                                        <!-- Manual Entry Tab -->
                                        <div class="tab-pane fade <%= !user || type !== 'private' ? 'show active' : '' %>"
                                             id="manual-pane" role="tabpanel">
                                            <input type="url"
                                                   class="form-control form-control-lg"
                                                   id="manualRepoUrl"
                                                   placeholder="https://github.com/owner/repository"
                                                   <%= !user || type !== 'private' ? 'required' : '' %>>
                                            <div class="form-text">
                                                Enter the full GitHub repository URL
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Organization and PR Selection -->
                                <div class="mb-4">
                                    <label for="organization-select" class="form-label text-dark fw-bold">
                                        <i class="fas fa-sitemap me-2"></i>
                                        Select Organization
                                    </label>
                                    <select id="organization-select" class="form-select">
                                        <option value="">-- Select an Organization --</option>
                                    </select>
                                </div>

                                <div id="pr-list-container" style="display: none;" class="mb-4">
                                    <h5 class="text-dark">Available Pull Requests</h5>
                                    <div class="border rounded" style="max-height: 400px; overflow-y: auto;">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Repo</th>
                                                    <th>PR #</th>
                                                    <th>Title</th>
                                                    <th>Author</th>
                                                    <th>Select</th>
                                                </tr>
                                            </thead>
                                            <tbody id="pr-list">
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- PR Number -->
                                <div class="mb-4">
                                    <label for="prNumber" class="form-label text-dark fw-bold">
                                        <i class="fas fa-code-branch me-2"></i>
                                        Pull Request Number
                                    </label>
                                    <input type="number" 
                                           class="form-control form-control-lg" 
                                           id="prNumber" 
                                           name="prNumber"
                                           placeholder="123"
                                           min="1"
                                           required>
                                    <div class="form-text">
                                        Enter the PR number (e.g., 123 for PR #123)
                                    </div>
                                </div>

                                <!-- Checklist Selection -->
                                <div class="mb-4">
                                    <label class="form-label text-dark fw-bold">
                                        <i class="fas fa-list-check me-2"></i>
                                        Analysis Checklist
                                    </label>
                                    <div class="row g-3" id="checklistSelection">
                                        <% Object.values(checklists).forEach(checklist => { %>
                                        <div class="col-md-6">
                                            <div class="card checklist-card h-100" data-checklist="<%= checklist.id %>">
                                                <div class="card-body">
                                                    <h6 class="card-title">
                                                        <i class="fas fa-check-circle me-2"></i>
                                                        <%= checklist.name %>
                                                    </h6>
                                                    <p class="card-text small text-muted">
                                                        <%= checklist.description %>
                                                    </p>
                                                    <div class="small">
                                                        <i class="fas fa-tasks me-1"></i>
                                                        <%= checklist.checks?.length || 0 %> categories
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <% }); %>
                                    </div>
                                    <input type="hidden" id="selectedChecklist" name="checklistId" value="default">
                                </div>

                                <!-- Analysis Type -->
                                <input type="hidden" name="analysisType" value="<%= type %>">

                                <!-- Submit Button -->
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-play me-2"></i>
                                        Start Comprehensive Analysis
                                    </button>
                                </div>
                            </form>

                            <!-- Quick Examples -->
                            <div class="mt-4 p-3 bg-light rounded">
                                <h6 class="text-dark mb-3">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    Quick Examples
                                </h6>
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <button class="btn btn-outline-secondary btn-sm w-100" 
                                                onclick="fillExample('https://github.com/microsoft/vscode', '123')">
                                            <i class="fab fa-microsoft me-1"></i>
                                            VS Code Example
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button class="btn btn-outline-secondary btn-sm w-100" 
                                                onclick="fillExample('https://github.com/facebook/react', '456')">
                                            <i class="fab fa-react me-1"></i>
                                            React Example
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <div class="row text-center">
                <div class="col-12 mb-5">
                    <h3>What You'll Get</h3>
                    <p class="text-muted">Comprehensive analysis with detailed feedback</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-md-3 text-center">
                    <div class="mb-3">
                        <i class="fas fa-code fa-3x text-primary"></i>
                    </div>
                    <h5>Line-by-Line Review</h5>
                    <p class="text-muted small">
                        Detailed analysis of every changed line with specific feedback and suggestions
                    </p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="mb-3">
                        <i class="fas fa-shield-alt fa-3x text-success"></i>
                    </div>
                    <h5>Security Scanning</h5>
                    <p class="text-muted small">
                        Automatic detection of security vulnerabilities and hardcoded secrets
                    </p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="mb-3">
                        <i class="fas fa-chart-line fa-3x text-warning"></i>
                    </div>
                    <h5>Quality Metrics</h5>
                    <p class="text-muted small">
                        Code quality assessment with maintainability and complexity analysis
                    </p>
                </div>
                <div class="col-md-3 text-center">
                    <div class="mb-3">
                        <i class="fas fa-list-check fa-3x text-info"></i>
                    </div>
                    <h5>Custom Checklists</h5>
                    <p class="text-muted small">
                        Configurable analysis rules tailored to your team's standards
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner-border spinner-border-lg mb-4" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h4 class="mb-4">Analyzing Pull Request</h4>
            <div class="progress-steps">
                <div class="progress-step" id="step1">
                    <i class="fas fa-download me-2"></i>
                    Fetching PR data...
                </div>
                <div class="progress-step mt-2" id="step2">
                    <i class="fas fa-file-code me-2"></i>
                    Analyzing files...
                </div>
                <div class="progress-step mt-2" id="step3">
                    <i class="fas fa-shield-alt me-2"></i>
                    Security scanning...
                </div>
                <div class="progress-step mt-2" id="step4">
                    <i class="fas fa-check-circle me-2"></i>
                    Generating report...
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Checklist selection
        document.querySelectorAll('.checklist-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                document.querySelectorAll('.checklist-card').forEach(c => c.classList.remove('selected'));
                
                // Add selected class to clicked card
                this.classList.add('selected');
                
                // Update hidden input
                document.getElementById('selectedChecklist').value = this.dataset.checklist;
            });
        });

        // Set default selection
        document.querySelector('.checklist-card[data-checklist="default"]')?.classList.add('selected');



        // Loading functions
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'block';
            
            // Simulate progress steps
            setTimeout(() => document.getElementById('step1').classList.add('active'), 500);
            setTimeout(() => document.getElementById('step2').classList.add('active'), 1500);
            setTimeout(() => document.getElementById('step3').classList.add('active'), 3000);
            setTimeout(() => document.getElementById('step4').classList.add('active'), 4500);
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
            document.querySelectorAll('.progress-step').forEach(step => step.classList.remove('active'));
        }

        // Example functions
        function fillExample(url, pr) {
            const activeTab = document.querySelector('.tab-pane.active input[name="repoUrl"]');
            if (activeTab) {
                activeTab.value = url;
            }
            document.getElementById('prNumber').value = pr;
        }

        // Repository management
        let repositories = [];
        let filteredRepositories = [];

        // Helper functions for repository display
        function getRepoTypeBadge(repo) {
            switch (repo.repo_type) {
                case 'owned':
                    return '<span class="badge bg-primary me-2">Owned</span>';
                case 'forked':
                    return '<span class="badge bg-warning me-2">Forked</span>';
                case 'organization':
                    return '<span class="badge bg-info me-2">Organization</span>';
                case 'collaborator':
                    return '<span class="badge bg-success me-2">Collaborator</span>';
                default:
                    return '';
            }
        }

        function getOwnerInfo(repo) {
            if (repo.owner && repo.owner.type === 'Organization') {
                return `<i class="fas fa-users me-1"></i>${repo.owner.login}`;
            } else if (repo.repo_type === 'collaborator') {
                return `<i class="fas fa-user me-1"></i>${repo.owner.login}`;
            }
            return '';
        }

        function displayRepositoryStats(breakdown) {
            const statsContainer = document.getElementById('repoStats');
            if (!statsContainer) return;

            statsContainer.innerHTML = `
                <div class="row g-2 mb-3">
                    <div class="col-auto">
                        <span class="badge bg-secondary">Total: ${breakdown.owned + breakdown.forked + breakdown.organization + breakdown.collaborator}</span>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-primary">Owned: ${breakdown.owned}</span>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-warning">Forked: ${breakdown.forked}</span>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-info">Organization: ${breakdown.organization}</span>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-success">Collaborator: ${breakdown.collaborator}</span>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-dark">Private: ${breakdown.private}</span>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-light text-dark">Public: ${breakdown.public}</span>
                    </div>
                </div>
            `;
        }

        // Load repositories for authenticated users
        <% if (user && type === 'private') { %>
        document.addEventListener('DOMContentLoaded', function() {
            loadRepositories();
        });

        async function loadRepositories() {
            try {
                const response = await fetch('/api/repositories');
                const result = await response.json();

                if (result.success) {
                    repositories = result.repositories;
                    filteredRepositories = repositories;
                    displayRepositories();
                    displayRepositoryStats(result.breakdown);
                } else {
                    showRepositoryError('Failed to load repositories: ' + result.error);
                }
            } catch (error) {
                showRepositoryError('Error loading repositories: ' + error.message);
            }
        }

        function displayRepositories() {
            const container = document.getElementById('repositoryList');

            if (filteredRepositories.length === 0) {
                container.innerHTML = `
                    <div class="list-group-item text-center py-4 text-muted">
                        <i class="fas fa-search fa-2x mb-2"></i>
                        <div>No repositories found</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredRepositories.map(repo => `
                <div class="list-group-item list-group-item-action repo-item"
                     data-repo-url="${repo.html_url}"
                     data-repo-name="${repo.full_name}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">
                                <i class="fas fa-${repo.private ? 'lock' : 'globe'} me-2 text-${repo.private ? 'warning' : 'success'}"></i>
                                ${repo.full_name}
                                ${repo.fork ? '<i class="fas fa-code-branch ms-2 text-info" title="Fork"></i>' : ''}
                            </h6>
                            <p class="mb-1 text-muted small">${repo.description || 'No description'}</p>
                            <div class="small text-muted">
                                ${getRepoTypeBadge(repo)}
                                ${repo.language ? `<span class="badge bg-secondary me-2">${repo.language}</span>` : ''}
                                <i class="fas fa-star me-1"></i>${repo.stargazers_count}
                                <i class="fas fa-exclamation-circle ms-2 me-1"></i>${repo.open_issues_count}
                                <span class="ms-2">Updated: ${new Date(repo.updated_at).toLocaleDateString()}</span>
                            </div>
                        </div>
                        <div class="text-end">
                            <div class="small text-muted mb-1">${getOwnerInfo(repo)}</div>
                            <button type="button" class="btn btn-sm btn-outline-primary select-repo-btn">
                                <i class="fas fa-check me-1"></i>
                                Select
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            // Add click handlers
            document.querySelectorAll('.repo-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    if (!e.target.closest('.select-repo-btn')) {
                        selectRepository(this.dataset.repoUrl, this.dataset.repoName);
                    }
                });

                item.querySelector('.select-repo-btn').addEventListener('click', function(e) {
                    e.stopPropagation();
                    selectRepository(item.dataset.repoUrl, item.dataset.repoName);
                });
            });
        }

        function selectRepository(repoUrl, repoName) {
            document.getElementById('selectedRepoUrl').value = repoUrl;
            document.getElementById('manualRepoUrl').value = repoUrl;

            // Update UI to show selection
            document.querySelectorAll('.repo-item').forEach(item => {
                item.classList.remove('active');
            });

            const selectedItem = document.querySelector(`[data-repo-url="${repoUrl}"]`);
            if (selectedItem) {
                selectedItem.classList.add('active');
                selectedItem.querySelector('.select-repo-btn').innerHTML = `
                    <i class="fas fa-check-circle me-1"></i>
                    Selected
                `;
            }

            // Show success message
            showRepositorySuccess(`Selected: ${repoName}`);
        }

        function showRepositoryError(message) {
            const container = document.getElementById('repositoryList');
            container.innerHTML = `
                <div class="list-group-item text-center py-4">
                    <i class="fas fa-exclamation-triangle text-warning fa-2x mb-2"></i>
                    <div class="text-danger">${message}</div>
                    <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="loadRepositories()">
                        <i class="fas fa-redo me-1"></i>
                        Retry
                    </button>
                </div>
            `;
        }

        function showRepositorySuccess(message) {
            // You could add a toast notification here
            console.log(message);
        }

        // Repository filtering functionality
        function applyFilters() {
            const searchTerm = document.getElementById('repoSearch')?.value.toLowerCase() || '';
            const typeFilter = document.getElementById('repoTypeFilter')?.value || 'all';

            let filtered = repositories;

            // Apply type filter
            if (typeFilter !== 'all') {
                switch (typeFilter) {
                    case 'private':
                        filtered = filtered.filter(repo => repo.private);
                        break;
                    case 'public':
                        filtered = filtered.filter(repo => !repo.private);
                        break;
                    default:
                        filtered = filtered.filter(repo => repo.repo_type === typeFilter);
                        break;
                }
            }

            // Apply search filter
            if (searchTerm !== '') {
                filtered = filtered.filter(repo =>
                    repo.full_name.toLowerCase().includes(searchTerm) ||
                    (repo.description && repo.description.toLowerCase().includes(searchTerm)) ||
                    (repo.language && repo.language.toLowerCase().includes(searchTerm))
                );
            }

            filteredRepositories = filtered;
            displayRepositories();
        }

        // Repository search functionality
        document.getElementById('repoSearch')?.addEventListener('input', applyFilters);

        // Repository type filter functionality
        document.getElementById('repoTypeFilter')?.addEventListener('change', applyFilters);

        // Organization and PR selection
        const orgSelect = document.getElementById('organization-select');
        const prListContainer = document.getElementById('pr-list-container');
        const prList = document.getElementById('pr-list');
        const manualRepoUrlInput = document.getElementById('manualRepoUrl');
        const prNumberInput = document.getElementById('prNumber');

        async function loadOrganizations() {
            try {
                const response = await fetch('/api/user/orgs');
                const data = await response.json();

                if (data.success) {
                    data.organizations.forEach(org => {
                        const option = document.createElement('option');
                        option.value = org.login;
                        option.textContent = org.login;
                        orgSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error fetching organizations:', error);
            }
        }

        orgSelect.addEventListener('change', async (event) => {
            const selectedOrg = event.target.value;

            if (selectedOrg) {
                prListContainer.style.display = 'block';
                prList.innerHTML = '<tr><td colspan="5">Loading...</td></tr>';

                try {
                    const response = await fetch(`/api/orgs/${selectedOrg}/pulls`);
                    const data = await response.json();

                    if (data.success) {
                        prList.innerHTML = '';
                        data.pullRequests.forEach(pr => {
                            const row = document.createElement('tr');
                            const repoFullName = pr.repository_url.split('/').slice(-2).join('/');
                            row.innerHTML = `
                                <td>${repoFullName}</td>
                                <td>${pr.number}</td>
                                <td>${pr.title}</td>
                                <td>${pr.user.login}</td>
                                <td><button class="btn btn-sm btn-primary select-pr-btn" data-repo-url="https://github.com/${repoFullName}" data-pr-number="${pr.number}">Select</button></td>
                            `;
                            prList.appendChild(row);
                        });
                    } else {
                        prList.innerHTML = `<tr><td colspan="5">${data.error}</td></tr>`;
                    }
                } catch (error) {
                    console.error('Error fetching pull requests:', error);
                    prList.innerHTML = '<tr><td colspan="5">Failed to fetch pull requests.</td></tr>';
                }
            } else {
                prListContainer.style.display = 'none';
            }
        });

        prList.addEventListener('click', (event) => {
            if (event.target.classList.contains('select-pr-btn')) {
                const repoUrl = event.target.dataset.repoUrl;
                const prNumber = event.target.dataset.prNumber;

                document.getElementById('selectedRepoUrl').value = repoUrl;
                document.getElementById('manualRepoUrl').value = repoUrl;
                prNumberInput.value = prNumber;

                // Switch to manual entry tab
                const manualTab = new bootstrap.Tab(document.getElementById('manual-tab'));
                manualTab.show();
            }
        });

        if (orgSelect) {
            loadOrganizations();
        }
        <% } %>

        // Handle form submission with repository selection
        document.getElementById('analysisForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const repoUrl = document.getElementById('manualRepoUrl').value || document.getElementById('selectedRepoUrl').value;

            if (!repoUrl) {
                alert('Please select a repository or enter a URL');
                return;
            }

            const data = {
                repoUrl: repoUrl,
                prNumber: formData.get('prNumber'),
                analysisType: formData.get('analysisType'),
                checklistId: formData.get('checklistId')
            };

            // Show loading overlay
            showLoading();

            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    // Redirect to results page
                    window.location.href = `/results/${result.reviewId}`;
                } else {
                    hideLoading();
                    alert('Analysis failed: ' + result.error);
                }
            } catch (error) {
                hideLoading();
                alert('Analysis failed: ' + error.message);
            }
        });
    </script>
</body>
</html>
