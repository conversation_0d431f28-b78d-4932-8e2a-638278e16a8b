<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <style>
        .results-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            margin: 0 auto;
        }
        .file-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1.5rem;
        }
        .line-comment {
            border-left: 3px solid #28a745;
            background-color: #f8f9fa;
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 0 0.375rem 0.375rem 0;
        }
        .security-issue {
            border-left: 3px solid #dc3545;
            background-color: #f8d7da;
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 0 0.375rem 0.375rem 0;
        }
        .quality-issue {
            border-left: 3px solid #ffc107;
            background-color: #fff3cd;
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 0 0.375rem 0.375rem 0;
        }
        .suggestion {
            border-left: 3px solid #17a2b8;
            background-color: #d1ecf1;
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 0 0.375rem 0.375rem 0;
        }
        .code-snippet {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            margin: 0.5rem 0;
        }
        .line-number {
            color: #6c757d;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .category-header {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        .metric-card {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .loading-placeholder {
            text-align: center;
            padding: 3rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-code-branch me-2"></i>
                PR Review Tool v2.0
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>
                    Dashboard
                </a>
                <a class="nav-link" href="/analyze">
                    <i class="fas fa-plus me-1"></i>
                    New Analysis
                </a>
            </div>
        </div>
    </nav>

    <!-- Results Header -->
    <section class="results-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-6 mb-3">
                        <i class="fas fa-chart-line me-2"></i>
                        PR Analysis Results
                    </h1>
                    <p class="lead mb-0" id="prTitle">
                        Loading analysis results...
                    </p>
                </div>
                <div class="col-lg-4 text-center">
                    <div class="score-circle bg-light text-dark" id="scoreCircle">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <p class="mt-2 mb-0" id="scoreText">Calculating score...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Results Content -->
    <div class="container my-5">
        <div id="resultsContent">
            <!-- Loading Placeholder -->
            <div class="loading-placeholder">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h4>Loading Comprehensive Analysis...</h4>
                <p class="text-muted">Please wait while we load your detailed PR review</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <script>
        // Load results on page load
        document.addEventListener('DOMContentLoaded', function() {
            const reviewId = '<%= reviewId %>';
            
            if (reviewId) {
                loadReviewResults(reviewId);
            } else {
                // Try to load from localStorage for direct access
                const storedAnalysis = localStorage.getItem('lastAnalysis');
                if (storedAnalysis) {
                    try {
                        const analysis = JSON.parse(storedAnalysis);
                        displayResults({ analysis });
                    } catch (error) {
                        showError('Failed to load stored analysis');
                    }
                } else {
                    showError('No analysis data found');
                }
            }
        });

        // Load review results from API
        async function loadReviewResults(reviewId) {
            try {
                const response = await fetch(`/api/reviews/${reviewId}`);
                const result = await response.json();
                
                if (result.success) {
                    displayResults(result.review);
                } else {
                    showError(result.error || 'Failed to load review');
                }
            } catch (error) {
                showError('Failed to load review: ' + error.message);
            }
        }

        // Display comprehensive results
        function displayResults(review) {
            const analysis = review.analysis;
            
            // Update header
            updateHeader(analysis);
            
            // Generate results HTML
            const resultsHTML = generateResultsHTML(analysis);
            
            // Update content
            document.getElementById('resultsContent').innerHTML = resultsHTML;
            
            // Highlight code
            Prism.highlightAll();
        }

        // Update header with score and PR info
        function updateHeader(analysis) {
            const percentage = analysis.summary.percentage;
            const scoreCircle = document.getElementById('scoreCircle');
            const scoreText = document.getElementById('scoreText');
            const prTitle = document.getElementById('prTitle');
            
            // Update score circle
            let scoreColor = 'bg-danger';
            if (percentage >= 90) scoreColor = 'bg-success';
            else if (percentage >= 75) scoreColor = 'bg-primary';
            else if (percentage >= 60) scoreColor = 'bg-warning';
            
            scoreCircle.className = `score-circle ${scoreColor} text-white`;
            scoreCircle.innerHTML = `${percentage}%`;
            
            // Update score text
            scoreText.textContent = `${analysis.summary.score}/${analysis.summary.totalChecks} checks passed`;
            
            // Update PR title
            if (analysis.metadata) {
                prTitle.innerHTML = `
                    <i class="fab fa-github me-2"></i>
                    ${analysis.metadata.repository} - PR #${analysis.metadata.prNumber}
                `;
            }
        }

        // Generate comprehensive results HTML
        function generateResultsHTML(analysis) {
            let html = '';
            
            // Summary metrics
            html += generateSummaryMetrics(analysis);
            
            // Category results
            html += generateCategoryResults(analysis);
            
            // File analysis
            html += generateFileAnalysis(analysis);
            
            // Line-by-line review
            html += generateLineByLineReview(analysis);
            
            // Recommendations
            html += generateRecommendations(analysis);
            
            return html;
        }

        // Generate summary metrics
        function generateSummaryMetrics(analysis) {
            const fileAnalysis = analysis.fileAnalysis || {};
            const lineReview = analysis.lineByLineReview || {};
            const issues = analysis.issues || {};
            
            return `
                <div class="row g-4 mb-5">
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <i class="fas fa-file-code fa-2x mb-2"></i>
                                <h4>${fileAnalysis.totalFiles || 0}</h4>
                                <small>Files Analyzed</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <i class="fas fa-comments fa-2x mb-2"></i>
                                <h4>${lineReview.totalLines || 0}</h4>
                                <small>Lines Reviewed</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <h4>${(issues.critical?.length || 0) + (issues.major?.length || 0)}</h4>
                                <small>Issues Found</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <i class="fas fa-lightbulb fa-2x mb-2"></i>
                                <h4>${issues.suggestions?.length || 0}</h4>
                                <small>Suggestions</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Generate category results
        function generateCategoryResults(analysis) {
            if (!analysis.categories) return '';
            
            let html = '<div class="category-header"><h3><i class="fas fa-list-check me-2"></i>Checklist Results</h3></div>';
            
            Object.values(analysis.categories).forEach(category => {
                const percentage = category.totalChecks > 0 ? Math.round((category.passedChecks / category.totalChecks) * 100) : 0;
                const statusColor = percentage >= 80 ? 'success' : percentage >= 60 ? 'warning' : 'danger';
                
                html += `
                    <div class="card mb-4">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-check-circle me-2"></i>
                                    ${category.name}
                                </h5>
                                <span class="badge bg-${statusColor} fs-6">${percentage}%</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">${category.description}</p>
                            <div class="row">
                `;
                
                category.checks.forEach(check => {
                    const checkIcon = check.passed ? 'check-circle text-success' : 'times-circle text-danger';
                    html += `
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-${checkIcon} me-3 mt-1"></i>
                                <div>
                                    <h6 class="mb-1">${check.name}</h6>
                                    <p class="text-muted small mb-0">${check.message}</p>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                html += `
                            </div>
                        </div>
                    </div>
                `;
            });
            
            return html;
        }

        // Generate file analysis
        function generateFileAnalysis(analysis) {
            if (!analysis.fileAnalysis?.files?.length) return '';
            
            let html = `
                <div class="category-header">
                    <h3><i class="fas fa-file-code me-2"></i>File Analysis</h3>
                </div>
            `;
            
            analysis.fileAnalysis.files.forEach(file => {
                html += `
                    <div class="card file-card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-file me-2"></i>
                                    ${file.filename}
                                    <span class="badge bg-${getStatusColor(file.status)} ms-2">${file.status}</span>
                                </h6>
                                <small class="text-muted">+${file.additions} -${file.deletions}</small>
                            </div>
                        </div>
                        <div class="card-body">
                `;
                
                // File recommendations
                if (file.recommendations?.length) {
                    html += `
                        <div class="mb-3">
                            <h6><i class="fas fa-lightbulb me-2"></i>Recommendations</h6>
                            <ul class="list-unstyled">
                    `;
                    file.recommendations.forEach(rec => {
                        html += `<li><i class="fas fa-arrow-right text-primary me-2"></i>${rec}</li>`;
                    });
                    html += '</ul></div>';
                }
                
                html += '</div></div>';
            });
            
            return html;
        }

        // Generate line-by-line review
        function generateLineByLineReview(analysis) {
            if (!analysis.lineByLineReview?.comments?.length) return '';
            
            let html = `
                <div class="category-header">
                    <h3><i class="fas fa-code me-2"></i>Line-by-Line Review</h3>
                </div>
            `;
            
            // Group comments by file
            const commentsByFile = {};
            analysis.lineByLineReview.comments.forEach(comment => {
                if (!commentsByFile[comment.file]) {
                    commentsByFile[comment.file] = [];
                }
                commentsByFile[comment.file].push(comment);
            });
            
            // Group issues by file
            const issuesByFile = {};
            ['critical', 'major', 'minor', 'suggestions'].forEach(severity => {
                if (analysis.issues[severity]) {
                    analysis.issues[severity].forEach(issue => {
                        if (!issuesByFile[issue.file]) {
                            issuesByFile[issue.file] = [];
                        }
                        issuesByFile[issue.file].push({...issue, severity});
                    });
                }
            });
            
            // Display by file
            Object.keys({...commentsByFile, ...issuesByFile}).forEach(filename => {
                html += `
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-file-code me-2"></i>
                                ${filename}
                            </h6>
                        </div>
                        <div class="card-body">
                `;
                
                // Show issues first
                if (issuesByFile[filename]) {
                    issuesByFile[filename].forEach(issue => {
                        const cssClass = getIssueCssClass(issue.severity);
                        const icon = getIssueIcon(issue.severity);
                        
                        html += `
                            <div class="${cssClass}">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-${icon} me-2 mt-1"></i>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <strong>Line ${issue.line}: ${issue.message}</strong>
                                            <span class="badge bg-${getSeverityColor(issue.severity)}">${issue.severity}</span>
                                        </div>
                                        <div class="code-snippet mt-2">
                                            <span class="line-number">${issue.line}:</span>
                                            <code>${escapeHtml(issue.code)}</code>
                                        </div>
                                        ${issue.suggestion ? `<div class="mt-2"><strong>Suggestion:</strong> ${issue.suggestion}</div>` : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                }
                
                // Show comments
                if (commentsByFile[filename]) {
                    commentsByFile[filename].forEach(comment => {
                        html += `
                            <div class="line-comment">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-comment me-2 mt-1"></i>
                                    <div class="flex-grow-1">
                                        <strong>Line ${comment.line}: ${comment.message}</strong>
                                        <div class="code-snippet mt-2">
                                            <span class="line-number">${comment.line}:</span>
                                            <code>${escapeHtml(comment.code)}</code>
                                        </div>
                                        ${comment.suggestion ? `<div class="mt-2"><em>${comment.suggestion}</em></div>` : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                }
                
                html += '</div></div>';
            });
            
            return html;
        }

        // Generate recommendations
        function generateRecommendations(analysis) {
            if (!analysis.recommendations?.length) return '';
            
            let html = `
                <div class="category-header">
                    <h3><i class="fas fa-lightbulb me-2"></i>Overall Recommendations</h3>
                </div>
                <div class="card">
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
            `;
            
            analysis.recommendations.forEach((rec, index) => {
                html += `
                    <li class="list-group-item">
                        <i class="fas fa-arrow-right text-primary me-2"></i>
                        ${rec}
                    </li>
                `;
            });
            
            html += '</ul></div></div>';
            return html;
        }

        // Helper functions
        function getStatusColor(status) {
            switch (status) {
                case 'added': return 'success';
                case 'modified': return 'primary';
                case 'removed': return 'danger';
                default: return 'secondary';
            }
        }

        function getIssueCssClass(severity) {
            switch (severity) {
                case 'critical': return 'security-issue';
                case 'major': return 'quality-issue';
                case 'minor': return 'quality-issue';
                default: return 'suggestion';
            }
        }

        function getIssueIcon(severity) {
            switch (severity) {
                case 'critical': return 'exclamation-triangle';
                case 'major': return 'exclamation-circle';
                case 'minor': return 'info-circle';
                default: return 'lightbulb';
            }
        }

        function getSeverityColor(severity) {
            switch (severity) {
                case 'critical': return 'danger';
                case 'major': return 'warning';
                case 'minor': return 'info';
                default: return 'secondary';
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Show error message
        function showError(message) {
            document.getElementById('resultsContent').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle text-warning fa-4x mb-3"></i>
                    <h4>Unable to Load Results</h4>
                    <p class="text-muted">${message}</p>
                    <div class="mt-4">
                        <a href="/analyze" class="btn btn-primary me-2">
                            <i class="fas fa-plus me-2"></i>
                            Start New Analysis
                        </a>
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
