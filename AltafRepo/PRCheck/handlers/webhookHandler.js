const express = require('express');
const router = express.Router();
const githubService = require('../services/githubService');
const checklistService = require('../services/checklistService');
const aiService = require('../services/aiService');
const logger = require('../utils/logger');

// GitHub webhook handler
router.post('/github', express.raw({ type: 'application/json' }), async (req, res) => {
    try {
        const signature = req.get('X-Hub-Signature-256');
        const event = req.get('X-GitHub-Event');
        const payload = JSON.parse(req.body.toString());
        
        // Verify webhook signature
        if (!githubService.validateWebhookSignature(req.body, signature)) {
            logger.warn('Invalid webhook signature');
            return res.status(401).json({ error: 'Invalid signature' });
        }
        
        logger.info(`Received GitHub webhook: ${event}`, {
            event,
            action: payload.action,
            repository: payload.repository?.full_name,
            pr_number: payload.pull_request?.number
        });
        
        // Handle different webhook events
        switch (event) {
            case 'pull_request':
                await handlePullRequestEvent(payload);
                break;
            case 'pull_request_review':
                await handlePullRequestReviewEvent(payload);
                break;
            case 'push':
                await handlePushEvent(payload);
                break;
            default:
                logger.info(`Unhandled webhook event: ${event}`);
        }
        
        res.status(200).json({ message: 'Webhook processed successfully' });
        
    } catch (error) {
        logger.error('Error processing webhook:', error);
        res.status(500).json({ error: 'Failed to process webhook' });
    }
});

async function handlePullRequestEvent(payload) {
    const { action, pull_request: pr, repository } = payload;
    
    // Only process certain actions
    const relevantActions = ['opened', 'synchronize', 'reopened', 'ready_for_review'];
    if (!relevantActions.includes(action)) {
        return;
    }
    
    try {
        // Get full PR data
        const fullPR = await githubService.getPR(
            repository.owner.login,
            repository.name,
            pr.number
        );
        
        // Run checklist analysis
        const checklistResults = await checklistService.analyzePR(fullPR);
        
        // Generate AI suggestions if score is low
        if (checklistResults.weightedScore < 70) {
            const suggestions = await aiService.generatePRSuggestions(checklistResults, fullPR);
            
            // Post comment with suggestions
            const commentBody = `## 🤖 PR Review Assistant Analysis
            
**Quality Score: ${checklistResults.weightedScore}%** (${checklistResults.score}/${checklistResults.totalChecks} checks passed)

### Issues Found:
${checklistResults.checks
    .filter(check => !check.passed)
    .map(check => `- **${check.name}**: ${check.reason}`)
    .join('\n')}

### AI Suggestions:
${suggestions}

---
*This analysis was generated automatically. You can view the detailed report [here](/pr/${repository.owner.login}/${repository.name}/${pr.number}).*`;
            
            await githubService.createPRComment(
                repository.owner.login,
                repository.name,
                pr.number,
                commentBody
            );
            
            logger.info(`Posted analysis comment for PR ${repository.full_name}#${pr.number}`);
        }
        
    } catch (error) {
        logger.error(`Error analyzing PR ${repository.full_name}#${pr.number}:`, error);
    }
}

async function handlePullRequestReviewEvent(payload) {
    const { action, review, pull_request: pr, repository } = payload;
    
    if (action !== 'submitted') {
        return;
    }
    
    try {
        logger.info(`Review submitted for PR ${repository.full_name}#${pr.number}`, {
            reviewer: review.user.login,
            state: review.state
        });
        
        // Could implement additional logic here, such as:
        // - Updating PR status based on reviews
        // - Triggering additional analysis
        // - Notifying team members
        
    } catch (error) {
        logger.error(`Error processing review for PR ${repository.full_name}#${pr.number}:`, error);
    }
}

async function handlePushEvent(payload) {
    const { repository, ref, commits } = payload;
    
    // Only process pushes to main/master branches
    if (!ref.includes('main') && !ref.includes('master')) {
        return;
    }
    
    try {
        logger.info(`Push to ${repository.full_name}:${ref}`, {
            commits_count: commits.length,
            pusher: payload.pusher.name
        });
        
        // Could implement additional logic here, such as:
        // - Analyzing commit message quality
        // - Triggering automated checks
        // - Updating metrics
        
    } catch (error) {
        logger.error(`Error processing push to ${repository.full_name}:`, error);
    }
}

// Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        webhook_handler: 'active',
        supported_events: ['pull_request', 'pull_request_review', 'push'],
        timestamp: new Date().toISOString()
    });
});

module.exports = router;
