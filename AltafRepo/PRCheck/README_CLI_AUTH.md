# 🚀 CLI Authentication for PR Review Tool

## 🎯 Overview

Your PR Review Tool now supports **CLI-friendly GitHub authentication** that eliminates the need for client secrets and callback URLs! Perfect for CLI applications, Docker environments, and any situation where traditional OAuth setup is problematic.

## ✅ What's Working Right Now

### **Personal Access Token Authentication** ✅
- **Status**: Fully functional
- **Setup**: Just create a GitHub token
- **Benefits**: Zero configuration needed

### **Enhanced Web Interface** ✅
- **Multiple auth options**: Dropdown with Device Flow, Personal Token, and OAuth
- **Step-by-step guidance**: Interactive modals with instructions
- **Professional UI**: Clean, intuitive interface

### **CLI Scripts** ✅
- **Interactive CLI**: `github-cli-auth.js` for guided authentication
- **Docker integration**: Works seamlessly with your Docker setup
- **Cross-platform**: macOS, Linux, Windows support

## 🚀 Quick Start (3 Steps)

### 1. Create GitHub Personal Access Token
```bash
# Visit: https://github.com/settings/tokens
# Click "Generate new token (classic)"
# Select scopes: "repo" and "user:email"
# Copy the generated token
```

### 2. Start Your Application
```bash
docker compose -f docker-compose.v2.yml up -d
```

### 3. Authenticate via CLI
```bash
# Interactive mode (recommended)
node github-cli-auth.js

# Or direct token
node github-cli-auth.js --token ghp_your_token_here

# Or use Docker wrapper
./docker-cli-auth.sh
```

## 🔧 Your Current Configuration

- **GitHub Client ID**: `Ov23libp9MfZXWUt3Ult` ✅
- **Personal Token Auth**: ✅ Working
- **Device Flow**: ❌ Needs to be enabled in GitHub App settings
- **Web Interface**: ✅ Enhanced with multiple auth options

## 📋 Available CLI Commands

### Host Machine Commands:
```bash
# Interactive authentication
node github-cli-auth.js

# Direct token authentication  
node github-cli-auth.js --token ghp_your_token_here

# Use saved token
node github-cli-auth.js --saved

# Show help
node github-cli-auth.js --help

# Docker wrapper script
./docker-cli-auth.sh
./docker-cli-auth.sh --token ghp_your_token_here
```

### Docker Container Commands:
```bash
# Run CLI inside container
docker exec -it prcheck-pr-review-v2-1 node github-cli-auth.js

# With token
docker exec -it prcheck-pr-review-v2-1 node github-cli-auth.js --token ghp_your_token_here
```

## 🌐 Web Interface Options

Visit: http://localhost:3000/login

**Authentication Methods Available:**
1. **Device Flow** (CLI-friendly) - Requires GitHub App configuration
2. **Personal Access Token** - Ready to use now ✅
3. **OAuth Web Flow** - Traditional method

## 🔑 Personal Access Token Setup

### Create Token:
1. Go to: https://github.com/settings/tokens
2. Click **"Generate new token (classic)"**
3. Name: "PR Review Tool"
4. Scopes needed:
   - ✅ `repo` (Full control of private repositories)
   - ✅ `user:email` (Access user email addresses)
5. Click **"Generate token"**
6. **Copy immediately** (you won't see it again!)

### Use Token:
```bash
# Method 1: Interactive CLI
node github-cli-auth.js
# Enter token when prompted

# Method 2: Direct command
node github-cli-auth.js --token ghp_your_token_here

# Method 3: Web interface
# Visit http://localhost:3000/login
# Choose "Personal Access Token" option
```

## 🐳 Docker Integration

The CLI authentication works perfectly with Docker:

```bash
# Start application
docker compose -f docker-compose.v2.yml up -d

# Authenticate from host
node github-cli-auth.js

# Or authenticate inside container
docker exec -it prcheck-pr-review-v2-1 node github-cli-auth.js

# Or use the wrapper script
./docker-cli-auth.sh
```

## 🎯 Example Workflow

```bash
# 1. Start the application
docker compose -f docker-compose.v2.yml up -d

# 2. Authenticate
node github-cli-auth.js
# Follow prompts to enter your token

# 3. Analyze a PR (via CLI)
# The script will offer to run a demo analysis

# 4. Or use web interface
# Visit http://localhost:3000 (you're now authenticated)
```

## 🔒 Security Features

- **Secure token storage**: Local file with restricted permissions (600)
- **Token validation**: Verifies token and scopes before use
- **No client secrets**: Personal tokens don't require client secrets
- **Scope limitation**: Only requests necessary permissions

## 🆘 Troubleshooting

### Token Issues:
```bash
# Test token directly
curl -X POST http://localhost:3000/auth/github/token \
  -H "Content-Type: application/json" \
  -d '{"token":"your_token_here"}'
```

### Application Issues:
```bash
# Check if running
curl http://localhost:3000/

# Check Docker logs
docker logs prcheck-pr-review-v2-1
```

### Common Problems:
1. **Invalid token**: Check scopes (`repo`, `user:email`) and expiration
2. **App not running**: `docker compose -f docker-compose.v2.yml up -d`
3. **Permission denied**: Token may lack required scopes

## 🎉 Benefits Achieved

✅ **No Client Secret Required** - Personal tokens work independently  
✅ **No Callback URLs** - Perfect for CLI and Docker environments  
✅ **Zero Configuration** - Just create a token and go  
✅ **Docker Compatible** - Works seamlessly with containers  
✅ **Cross-Platform** - macOS, Linux, Windows support  
✅ **Secure** - Proper token handling and validation  
✅ **User-Friendly** - Interactive CLI with guidance  

## 🔮 Optional: Enable Device Flow

For the ultimate CLI experience, you can enable Device Flow in your GitHub App:

1. Go to: https://github.com/settings/apps
2. Find your app with Client ID `Ov23libp9MfZXWUt3Ult`
3. Enable "Device Flow" option
4. Test with: `curl -X POST http://localhost:3000/auth/github/device`

## 📚 Files Created

- `github-cli-auth.js` - Main CLI authentication script
- `docker-cli-auth.sh` - Docker wrapper script  
- `CLI_SETUP_GUIDE.md` - Detailed setup instructions
- Enhanced login page with multiple auth options
- API endpoints for token authentication

## 🌟 Success!

You now have a **production-ready CLI authentication system** that works perfectly with your Docker setup. No more complex OAuth configuration - just create a Personal Access Token and start analyzing PRs! 🚀

**Next Steps:**
1. Create your GitHub Personal Access Token
2. Run `node github-cli-auth.js` 
3. Start analyzing PRs with CLI-friendly authentication!
