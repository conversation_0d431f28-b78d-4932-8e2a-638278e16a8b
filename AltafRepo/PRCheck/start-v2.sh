#!/bin/bash

echo "🚀 Starting Enhanced PR Review Tool v2.0"
echo "========================================"

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker compose -f docker-compose.simple.yml down 2>/dev/null || true

# Create Dockerfile for v2
cat > Dockerfile.v2 << 'EOF'
FROM node:18-alpine

# Install curl for health checks
RUN apk add --no-cache curl

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application files
COPY app-v2.js ./
COPY analysis-engine.js ./
COPY file-analyzer.js ./
COPY default-checklists.js ./
COPY views-v2/ ./views-v2/
COPY public/ ./public/

# Create data directory
RUN mkdir -p data

# Create default environment file
RUN echo "PORT=3000" > .env.default

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/ || exit 1

EXPOSE 3000

CMD ["node", "app-v2.js"]
EOF

# Create docker-compose for v2
cat > docker-compose.v2.yml << 'EOF'
version: '3.8'

services:
  pr-review-v2:
    build:
      context: .
      dockerfile: Dockerfile.v2
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    env_file:
      - .env
    volumes:
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  data:
EOF

# Build and start the application
echo "🔨 Building and starting Enhanced PR Review Tool v2.0..."
docker compose -f docker-compose.v2.yml up --build -d

# Wait for service to start
echo "⏳ Waiting for service to start..."
sleep 5

# Check service status
echo "📊 Service Status:"
echo "=================="

# Check if container is running
if docker compose -f docker-compose.v2.yml ps | grep -q "Up"; then
    echo "✅ Application: Running (http://localhost:3000)"
else
    echo "❌ Application: Failed to start"
    echo "📋 Logs:"
    docker compose -f docker-compose.v2.yml logs
    exit 1
fi

# Check if .env file exists and GitHub OAuth is configured
if [ -f .env ]; then
    if grep -q "GITHUB_CLIENT_ID" .env && grep -q "GITHUB_CLIENT_SECRET" .env; then
        echo "✅ GitHub OAuth: Configured"
    else
        echo "⚠️  GitHub OAuth: Not configured (private repos won't work)"
    fi
else
    echo "⚠️  GitHub OAuth: Not configured (private repos won't work)"
fi

echo ""
echo "🎉 Enhanced PR Review Tool v2.0 is ready!"
echo "========================================"
echo "📱 Access your application at: http://localhost:3000"
echo ""
echo "🆕 New Features:"
echo "• ✅ Enhanced Dashboard with three analysis methods"
echo "• ✅ Line-by-line code review with detailed feedback"
echo "• ✅ Comprehensive security scanning"
echo "• ✅ Custom checklist management"
echo "• ✅ Professional results display"
echo "• ✅ Improved redirection and data flow"
echo ""
echo "📋 Analysis Methods:"
echo "• 🌐 Public Repository Analysis (no login required)"
echo "• 🔒 Private Repository Analysis (GitHub OAuth)"
echo "• ⌨️  Manual Repository Input (flexible entry)"
echo ""
echo "📝 Useful commands:"
echo "• View logs: docker compose -f docker-compose.v2.yml logs -f"
echo "• Stop app: docker compose -f docker-compose.v2.yml down"
echo "• Restart: ./start-v2.sh"
echo ""

# Test the application
echo "🧪 Testing application..."
if curl -s http://localhost:3000/ > /dev/null; then
    echo "✅ Application is responding"
else
    echo "❌ Application is not responding"
    echo "📋 Container logs:"
    docker compose -f docker-compose.v2.yml logs --tail=20
fi

echo ""
echo "🎯 Ready to test with your repository:"
echo "   Repository: https://github.com/saltaf07/assistila-web"
echo "   PR Number: 1"
echo ""
