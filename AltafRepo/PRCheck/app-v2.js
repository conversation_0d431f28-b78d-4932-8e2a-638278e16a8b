// Load environment variables first
require('dotenv').config();

const express = require('express');
const session = require('express-session');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// Import analysis modules
const { performPRAnalysis } = require('./analysis-engine');
const { getDefaultChecklists } = require('./default-checklists');

const app = express();
const PORT = process.env.PORT || 3000;

const GITHUB_CLIENT_ID = process.env.GITHUB_CLIENT_ID;
const GITHUB_CLIENT_SECRET = process.env.GITHUB_CLIENT_SECRET;
const SESSION_SECRET = process.env.SESSION_SECRET || 'pr-review-secret-key';

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));

// Session configuration
app.use(session({
    secret: SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 } // 24 hours
}));

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views-v2'));

// Data storage paths
const DATA_DIR = path.join(__dirname, 'data');
const REVIEWS_FILE = path.join(DATA_DIR, 'reviews.json');
const CHECKLISTS_FILE = path.join(DATA_DIR, 'checklists.json');
const USERS_FILE = path.join(DATA_DIR, 'users.json');
const TOKENS_FILE = path.join(DATA_DIR, 'tokens.json');

// Ensure data directory exists
async function ensureDataDirectory() {
    try {
        await fs.access(DATA_DIR);
    } catch {
        await fs.mkdir(DATA_DIR, { recursive: true });
    }
}

// Initialize data files
async function initializeDataFiles() {
    await ensureDataDirectory();

    // Initialize reviews file
    try {
        await fs.access(REVIEWS_FILE);
    } catch {
        await fs.writeFile(REVIEWS_FILE, JSON.stringify([]));
    }

    // Initialize checklists file with default checklists
    try {
        await fs.access(CHECKLISTS_FILE);
    } catch {
        const defaultChecklists = await getDefaultChecklists();
        await fs.writeFile(CHECKLISTS_FILE, JSON.stringify(defaultChecklists, null, 2));
    }

    // Initialize users file
    try {
        await fs.access(USERS_FILE);
    } catch {
        await fs.writeFile(USERS_FILE, JSON.stringify([]));
    }

    // Initialize tokens file
    try {
        await fs.access(TOKENS_FILE);
    } catch {
        await fs.writeFile(TOKENS_FILE, JSON.stringify({}));
    }
}

// Helper functions for data management
async function loadReviews() {
    try {
        const data = await fs.readFile(REVIEWS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return [];
    }
}

async function saveReview(review) {
    const reviews = await loadReviews();
    reviews.unshift(review);
    await fs.writeFile(REVIEWS_FILE, JSON.stringify(reviews, null, 2));
}

async function loadChecklists() {
    try {
        const data = await fs.readFile(CHECKLISTS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return await getDefaultChecklists();
    }
}

async function saveChecklists(checklists) {
    await fs.writeFile(CHECKLISTS_FILE, JSON.stringify(checklists, null, 2));
}

async function loadUsers() {
    try {
        const data = await fs.readFile(USERS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return [];
    }
}

async function saveUser(user) {
    const users = await loadUsers();
    const existingIndex = users.findIndex(u => u.id === user.id);
    if (existingIndex >= 0) {
        users[existingIndex] = user;
    } else {
        users.push(user);
    }
    await fs.writeFile(USERS_FILE, JSON.stringify(users, null, 2));
}

// Token storage functions
async function loadTokens() {
    try {
        const data = await fs.readFile(TOKENS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        return {};
    }
}

async function saveToken(userId, tokenData) {
    try {
        const tokens = await loadTokens();

        // Simple encryption using base64 (in production, use proper encryption)
        const encryptedToken = Buffer.from(tokenData.token).toString('base64');

        tokens[userId] = {
            token: encryptedToken,
            createdAt: new Date().toISOString(),
            lastUsed: new Date().toISOString(),
            remember: tokenData.remember || false
        };

        await fs.writeFile(TOKENS_FILE, JSON.stringify(tokens, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving token:', error);
        return false;
    }
}

async function getStoredToken(userId) {
    try {
        const tokens = await loadTokens();
        const tokenData = tokens[userId];

        if (!tokenData) {
            return null;
        }

        // Decrypt token
        const decryptedToken = Buffer.from(tokenData.token, 'base64').toString('utf8');

        // Update last used
        tokenData.lastUsed = new Date().toISOString();
        await fs.writeFile(TOKENS_FILE, JSON.stringify(tokens, null, 2));

        return {
            token: decryptedToken,
            createdAt: tokenData.createdAt,
            lastUsed: tokenData.lastUsed,
            remember: tokenData.remember
        };
    } catch (error) {
        console.error('Error retrieving token:', error);
        return null;
    }
}

async function removeStoredToken(userId) {
    try {
        const tokens = await loadTokens();
        delete tokens[userId];
        await fs.writeFile(TOKENS_FILE, JSON.stringify(tokens, null, 2));
        return true;
    } catch (error) {
        console.error('Error removing token:', error);
        return false;
    }
}

// Routes

// Home page - Enhanced Dashboard
app.get('/', async (req, res) => {
    try {
        let user = req.session.user || null;

        // Auto-login with stored token if no active session
        if (!user && req.query.auto_login !== 'false') {
            const userId = req.query.user_id;
            if (userId) {
                const tokenData = await getStoredToken(userId);
                if (tokenData && tokenData.remember) {
                    try {
                        // Validate stored token
                        const userResponse = await axios.get('https://api.github.com/user', {
                            headers: {
                                'Authorization': `token ${tokenData.token}`,
                                'User-Agent': 'PR-Review-Tool'
                            }
                        });

                        // Create user session
                        user = {
                            id: userResponse.data.id,
                            login: userResponse.data.login,
                            name: userResponse.data.name,
                            email: userResponse.data.email,
                            avatar_url: userResponse.data.avatar_url,
                            accessToken: tokenData.token,
                            loginTime: new Date().toISOString(),
                            authMethod: 'stored_token'
                        };

                        req.session.user = user;
                        await saveUser(user);
                    } catch (error) {
                        console.error('Auto-login failed:', error);
                        // Remove invalid token
                        await removeStoredToken(userId);
                    }
                }
            }
        }

        const reviews = user ? (await loadReviews()).filter(r => r.userId === user.id) : [];
        const checklists = await loadChecklists();

        res.render('dashboard', {
            title: 'CodeReview Pro Dashboard',
            user,
            reviews: reviews.slice(0, 5), // Show last 5 reviews
            totalReviews: reviews.length,
            checklists: Object.keys(checklists).length,
            githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET)
        });
    } catch (error) {
        console.error('Dashboard error:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load dashboard',
            error: error.message
        });
    }
});

// Login page
app.get('/login', (req, res) => {
    res.render('login', {
        title: 'Login - PR Review Tool',
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
        error: req.query.error
    });
});

// Analysis page
app.get('/analyze', async (req, res) => {
    try {
        const user = req.session.user || null;
        const checklists = await loadChecklists();
        
        res.render('analyze', {
            title: 'Analyze PR',
            user,
            checklists,
            type: req.query.type || 'public' // public, private, manual
        });
    } catch (error) {
        console.error('Analysis page error:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load analysis page',
            error: error.message
        });
    }
});

// Results page
app.get('/results/:reviewId?', async (req, res) => {
    try {
        const user = req.session.user || null;
        const reviewId = req.params.reviewId;
        let review = null;
        
        if (reviewId) {
            const reviews = await loadReviews();
            review = reviews.find(r => r.id === reviewId);
            
            // Check access permissions
            if (review && user && review.userId !== user.id) {
                return res.status(403).render('error', {
                    title: 'Access Denied',
                    message: 'You do not have permission to view this review'
                });
            }
        }
        
        res.render('results', {
            title: review ? `Results: PR #${review.prNumber}` : 'PR Analysis Results',
            user,
            review,
            reviewId
        });
    } catch (error) {
        console.error('Results page error:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load results',
            error: error.message
        });
    }
});

// Checklist management page
app.get('/checklists', async (req, res) => {
    try {
        const user = req.session.user || null;
        const checklists = await loadChecklists();

        res.render('checklists', {
            title: 'Manage Checklists',
            user,
            checklists
        });
    } catch (error) {
        console.error('Checklists page error:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load checklists',
            error: error.message
        });
    }
});

// Settings page
app.get('/settings', async (req, res) => {
    try {
        if (!req.session.user) {
            return res.redirect('/login');
        }

        const tokenData = await getStoredToken(req.session.user.id);

        res.render('settings', {
            title: 'Settings - PR Review Tool',
            user: req.session.user,
            tokenInfo: tokenData ? {
                hasToken: true,
                createdAt: tokenData.createdAt,
                lastUsed: tokenData.lastUsed,
                remember: tokenData.remember
            } : { hasToken: false }
        });
    } catch (error) {
        console.error('Settings page error:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load settings',
            error: error.message
        });
    }
});

// Initialize application
async function startApplication() {
    await initializeDataFiles();
    
    app.listen(PORT, () => {
        console.log(`🚀 Enhanced PR Review Tool v2.0 running on http://localhost:${PORT}`);
        console.log(`📊 Features: Enhanced Dashboard, Line-by-Line Analysis, Custom Checklists`);
        console.log(`🔐 GitHub OAuth: ${GITHUB_CLIENT_ID ? 'Configured' : 'Not configured'}`);
    });
}

// API Routes

// GitHub OAuth routes
app.get('/auth/github', (req, res) => {
    if (!GITHUB_CLIENT_ID) {
        return res.redirect('/login?error=github_not_configured');
    }

    const state = crypto.randomBytes(16).toString('hex');
    req.session.oauthState = state;

    const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${GITHUB_CLIENT_ID}&scope=repo&state=${state}`;
    res.redirect(githubAuthUrl);
});

app.get('/auth/github/callback', async (req, res) => {
    const { code, state } = req.query;

    if (!code || state !== req.session.oauthState) {
        return res.redirect('/login?error=invalid_oauth_state');
    }

    try {
        // Exchange code for access token
        const tokenResponse = await axios.post('https://github.com/login/oauth/access_token', {
            client_id: GITHUB_CLIENT_ID,
            client_secret: GITHUB_CLIENT_SECRET,
            code
        }, {
            headers: { Accept: 'application/json' }
        });

        const accessToken = tokenResponse.data.access_token;

        // Get user info
        const userResponse = await axios.get('https://api.github.com/user', {
            headers: { Authorization: `token ${accessToken}` }
        });

        const user = {
            id: userResponse.data.id,
            login: userResponse.data.login,
            name: userResponse.data.name,
            email: userResponse.data.email,
            avatar_url: userResponse.data.avatar_url,
            accessToken,
            loginTime: new Date().toISOString()
        };

        req.session.user = user;
        await saveUser(user);

        res.redirect('/?login=success');
    } catch (error) {
        console.error('OAuth callback error:', error);
        res.redirect('/login?error=oauth_failed');
    }
});

// Logout
app.post('/auth/logout', (req, res) => {
    req.session.destroy();
    res.redirect('/');
});

// Token management endpoints
app.get('/api/user/token', async (req, res) => {
    try {
        if (!req.session.user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }

        const tokenData = await getStoredToken(req.session.user.id);

        res.json({
            success: true,
            hasStoredToken: !!tokenData,
            tokenInfo: tokenData ? {
                createdAt: tokenData.createdAt,
                lastUsed: tokenData.lastUsed,
                remember: tokenData.remember
            } : null
        });
    } catch (error) {
        console.error('Error getting token info:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get token information'
        });
    }
});

app.post('/api/user/token/update', async (req, res) => {
    try {
        if (!req.session.user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }

        const { token, remember } = req.body;

        if (!token) {
            return res.status(400).json({
                success: false,
                error: 'Token is required'
            });
        }

        // Validate new token
        const userResponse = await axios.get('https://api.github.com/user', {
            headers: {
                'Authorization': `token ${token}`,
                'User-Agent': 'PR-Review-Tool'
            }
        });

        // Ensure token belongs to the same user
        if (userResponse.data.id !== req.session.user.id) {
            return res.status(400).json({
                success: false,
                error: 'Token must belong to the authenticated user'
            });
        }

        // Save the new token
        const saved = await saveToken(req.session.user.id, { token, remember });

        if (saved) {
            // Update session with new token
            req.session.user.accessToken = token;

            res.json({
                success: true,
                message: 'Token updated successfully'
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'Failed to save token'
            });
        }
    } catch (error) {
        console.error('Error updating token:', error);
        res.status(401).json({
            success: false,
            error: 'Invalid token or insufficient permissions'
        });
    }
});

app.delete('/api/user/token', async (req, res) => {
    try {
        if (!req.session.user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }

        const removed = await removeStoredToken(req.session.user.id);

        res.json({
            success: true,
            message: removed ? 'Token removed successfully' : 'No token was stored'
        });
    } catch (error) {
        console.error('Error removing token:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to remove token'
        });
    }
});

// CLI Authentication Methods

// GitHub Device Flow Authentication (No client secret needed)
app.post('/auth/github/device', async (req, res) => {
    try {
        if (!GITHUB_CLIENT_ID) {
            return res.status(400).json({
                success: false,
                error: 'GitHub Client ID not configured'
            });
        }

        // Start device flow
        const deviceResponse = await axios.post('https://github.com/login/device/code', {
            client_id: GITHUB_CLIENT_ID,
            scope: 'repo,user:email'
        }, {
            headers: {
                'Accept': 'application/json'
            }
        });

        const { device_code, user_code, verification_uri, expires_in, interval } = deviceResponse.data;

        res.json({
            success: true,
            user_code,
            verification_uri,
            device_code,
            expires_in,
            interval
        });

    } catch (error) {
        console.error('Device flow initiation error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to initiate device flow'
        });
    }
});

app.post('/auth/github/device/poll', async (req, res) => {
    const { device_code } = req.body;

    if (!device_code) {
        return res.status(400).json({
            success: false,
            error: 'Device code is required'
        });
    }

    try {
        // Poll for access token
        const tokenResponse = await axios.post('https://github.com/login/oauth/access_token', {
            client_id: GITHUB_CLIENT_ID,
            device_code: device_code,
            grant_type: 'urn:ietf:params:oauth:grant-type:device_code'
        }, {
            headers: {
                'Accept': 'application/json'
            }
        });

        if (tokenResponse.data.error) {
            return res.json({
                success: false,
                error: tokenResponse.data.error,
                pending: tokenResponse.data.error === 'authorization_pending'
            });
        }

        const accessToken = tokenResponse.data.access_token;

        // Get user info
        const userResponse = await axios.get('https://api.github.com/user', {
            headers: {
                'Authorization': `token ${accessToken}`,
                'User-Agent': 'PR-Review-Tool'
            }
        });

        const user = {
            id: userResponse.data.id,
            login: userResponse.data.login,
            name: userResponse.data.name,
            email: userResponse.data.email,
            avatar_url: userResponse.data.avatar_url,
            accessToken,
            loginTime: new Date().toISOString(),
            authMethod: 'device_flow'
        };

        req.session.user = user;
        await saveUser(user);

        res.json({
            success: true,
            user: {
                id: user.id,
                login: user.login,
                name: user.name,
                email: user.email,
                avatar_url: user.avatar_url
            }
        });

    } catch (error) {
        console.error('Device flow polling error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to poll for token'
        });
    }
});

// Personal Access Token Authentication
app.post('/auth/github/token', async (req, res) => {
    const { token, remember } = req.body;

    if (!token) {
        return res.status(400).json({
            success: false,
            error: 'Token is required'
        });
    }

    try {
        // Validate token and get user info
        const userResponse = await axios.get('https://api.github.com/user', {
            headers: {
                'Authorization': `token ${token}`,
                'User-Agent': 'PR-Review-Tool'
            }
        });

        const user = {
            id: userResponse.data.id,
            login: userResponse.data.login,
            name: userResponse.data.name,
            email: userResponse.data.email,
            avatar_url: userResponse.data.avatar_url,
            accessToken: token,
            loginTime: new Date().toISOString(),
            authMethod: 'personal_token'
        };

        req.session.user = user;
        await saveUser(user);

        // Save token persistently if requested
        if (remember) {
            await saveToken(user.id, { token, remember: true });
        }

        res.json({
            success: true,
            user: {
                id: user.id,
                login: user.login,
                name: user.name,
                email: user.email,
                avatar_url: user.avatar_url
            },
            tokenStored: remember || false
        });

    } catch (error) {
        console.error('Token validation error:', error);
        res.status(401).json({
            success: false,
            error: 'Invalid token or insufficient permissions'
        });
    }
});

// PR Analysis API
app.post('/api/analyze', async (req, res) => {
    try {
        const { repoUrl, prNumber, analysisType, checklistId } = req.body;
        const user = req.session.user;

        // Validate input
        if (!repoUrl || !prNumber) {
            return res.status(400).json({
                success: false,
                error: 'Repository URL and PR number are required'
            });
        }

        // Parse repository info
        const repoMatch = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
        if (!repoMatch) {
            return res.status(400).json({
                success: false,
                error: 'Invalid GitHub repository URL'
            });
        }

        const [, owner, repo] = repoMatch;

        // Perform analysis
        const analysis = await performPRAnalysis(owner, repo, prNumber, analysisType, checklistId, user);

        // Save review
        const review = {
            id: crypto.randomBytes(16).toString('hex'),
            userId: user?.id || null,
            repoUrl,
            prNumber: parseInt(prNumber),
            analysisType,
            checklistId,
            analysis,
            createdAt: new Date().toISOString()
        };

        await saveReview(review);

        res.json({
            success: true,
            reviewId: review.id,
            analysis
        });

    } catch (error) {
        console.error('Analysis error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Repository management API
app.get('/api/repositories', async (req, res) => {
    try {
        if (!req.session.user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }

        const user = req.session.user;
        const headers = {
            'Authorization': `token ${user.accessToken}`,
            'User-Agent': 'PR-Review-Tool'
        };

        let allRepositories = [];

        // Fetch user's own repositories (owned) - including forks
        const ownedRepos = await axios.get('https://api.github.com/user/repos', {
            headers,
            params: {
                sort: 'updated',
                per_page: 100,
                type: 'owner'
            }
        });

        // Note: GitHub API type=forks doesn't work reliably, so we'll identify forks by the fork property

        // Fetch repositories user is a member/collaborator of (including org repos)
        const memberRepos = await axios.get('https://api.github.com/user/repos', {
            headers,
            params: {
                sort: 'updated',
                per_page: 100,
                type: 'member'
            }
        });

        // Fetch all repositories with affiliation (cannot use type with affiliation)
        const allAccessRepos = await axios.get('https://api.github.com/user/repos', {
            headers,
            params: {
                sort: 'updated',
                per_page: 100,
                affiliation: 'owner,collaborator,organization_member'
            }
        });

        // Try to fetch specific known repositories that might not appear in list endpoints
        const knownRepositories = [];
        const knownRepoNames = ['altaf-re-makefiles-common']; // Add known missing repos here

        for (const repoName of knownRepoNames) {
            try {
                const specificRepo = await axios.get(`https://api.github.com/repos/${user.login}/${repoName}`, {
                    headers
                });
                knownRepositories.push(specificRepo.data);
                console.log(`Successfully fetched known repository: ${repoName}`);
            } catch (error) {
                console.log(`Known repository ${repoName} not accessible:`, error.response?.status);
            }
        }

        // Combine and deduplicate repositories
        const repoMap = new Map();

        [...ownedRepos.data, ...memberRepos.data, ...allAccessRepos.data, ...knownRepositories].forEach(repo => {
            if (!repoMap.has(repo.id)) {
                const repoData = {
                    id: repo.id,
                    name: repo.name,
                    full_name: repo.full_name,
                    description: repo.description,
                    private: repo.private,
                    html_url: repo.html_url,
                    updated_at: repo.updated_at,
                    language: repo.language,
                    stargazers_count: repo.stargazers_count,
                    open_issues_count: repo.open_issues_count,
                    fork: repo.fork,
                    owner: {
                        login: repo.owner.login,
                        type: repo.owner.type,
                        avatar_url: repo.owner.avatar_url
                    },
                    permissions: repo.permissions || {},
                    // Determine repository type for UI display
                    repo_type: repo.fork ? 'forked' :  // Any fork is classified as forked regardless of owner
                              repo.owner.login === user.login ? 'owned' :
                              repo.owner.type === 'Organization' ? 'organization' : 'collaborator'
                };
                repoMap.set(repo.id, repoData);
            }
        });

        allRepositories = Array.from(repoMap.values());

        // Sort by updated date (most recent first)
        allRepositories.sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));

        res.json({
            success: true,
            repositories: allRepositories,
            total: allRepositories.length,
            breakdown: {
                owned: allRepositories.filter(r => r.repo_type === 'owned').length,
                forked: allRepositories.filter(r => r.repo_type === 'forked').length,
                organization: allRepositories.filter(r => r.repo_type === 'organization').length,
                collaborator: allRepositories.filter(r => r.repo_type === 'collaborator').length,
                private: allRepositories.filter(r => r.private).length,
                public: allRepositories.filter(r => !r.private).length,
                total_forks: allRepositories.filter(r => r.fork).length
            }
        });

    } catch (error) {
        console.error('Error fetching repositories:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch repositories'
        });
    }
});

// Checklist management API
app.get('/api/checklists', async (req, res) => {
    try {
        const checklists = await loadChecklists();
        res.json({ success: true, checklists });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Get individual checklist
app.get('/api/checklists/:id', async (req, res) => {
    try {
        const checklists = await loadChecklists();
        const checklist = checklists[req.params.id];

        if (!checklist) {
            return res.status(404).json({
                success: false,
                error: 'Checklist not found'
            });
        }

        res.json({
            success: true,
            checklist
        });
    } catch (error) {
        console.error('Error fetching checklist:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch checklist'
        });
    }
});

app.post('/api/checklists', async (req, res) => {
    try {
        const { name, description, rules, checks } = req.body;
        const checklists = await loadChecklists();

        if (!name || (!rules && !checks)) {
            return res.status(400).json({
                success: false,
                error: 'Name and rules are required'
            });
        }

        const checklistId = crypto.randomBytes(8).toString('hex');
        checklists[checklistId] = {
            id: checklistId,
            name,
            description: description || '',
            rules: rules || undefined, // New unified structure
            checks: checks || undefined, // Legacy structure for backward compatibility
            createdAt: new Date().toISOString(),
            createdBy: req.session.user?.id || 'anonymous'
        };

        await saveChecklists(checklists);
        res.json({ success: true, checklistId });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.put('/api/checklists/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description, rules, checks } = req.body;
        const checklists = await loadChecklists();

        if (!checklists[id]) {
            return res.status(404).json({ success: false, error: 'Checklist not found' });
        }

        if (!name || (!rules && !checks)) {
            return res.status(400).json({
                success: false,
                error: 'Name and rules are required'
            });
        }

        checklists[id] = {
            ...checklists[id],
            name,
            description: description || '',
            rules: rules || undefined, // New unified structure
            checks: checks || undefined, // Legacy structure for backward compatibility
            updatedAt: new Date().toISOString()
        };

        await saveChecklists(checklists);
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.delete('/api/checklists/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const checklists = await loadChecklists();

        if (!checklists[id]) {
            return res.status(404).json({ success: false, error: 'Checklist not found' });
        }

        delete checklists[id];
        await saveChecklists(checklists);
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Get review data API
app.get('/api/reviews/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const reviews = await loadReviews();
        const review = reviews.find(r => r.id === id);

        if (!review) {
            return res.status(404).json({ success: false, error: 'Review not found' });
        }

        // Check access permissions
        const user = req.session.user;
        if (review.userId && (!user || review.userId !== user.id)) {
            return res.status(403).json({ success: false, error: 'Access denied' });
        }

        res.json({ success: true, review });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Start the application
startApplication().catch(console.error);

module.exports = app;
