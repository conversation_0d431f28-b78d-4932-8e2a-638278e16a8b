const express = require('express');
const session = require('express-session');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// Import analysis modules
const { performPRAnalysis } = require('./analysis-engine');
const { getDefaultChecklists } = require('./default-checklists');

const app = express();
const PORT = process.env.PORT || 3000;

// Load environment variables
require('dotenv').config();

const GITHUB_CLIENT_ID = process.env.GITHUB_CLIENT_ID;
const GITHUB_CLIENT_SECRET = process.env.GITHUB_CLIENT_SECRET;
const SESSION_SECRET = process.env.SESSION_SECRET || 'pr-review-secret-key';

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));

// Session configuration
app.use(session({
    secret: SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false, maxAge: 24 * 60 * 60 * 1000 } // 24 hours
}));

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views-v2'));

// Data storage paths
const DATA_DIR = path.join(__dirname, 'data');
const REVIEWS_FILE = path.join(DATA_DIR, 'reviews.json');
const CHECKLISTS_FILE = path.join(DATA_DIR, 'checklists.json');
const USERS_FILE = path.join(DATA_DIR, 'users.json');

// Ensure data directory exists
async function ensureDataDirectory() {
    try {
        await fs.access(DATA_DIR);
    } catch {
        await fs.mkdir(DATA_DIR, { recursive: true });
    }
}

// Initialize data files
async function initializeDataFiles() {
    await ensureDataDirectory();
    
    // Initialize reviews file
    try {
        await fs.access(REVIEWS_FILE);
    } catch {
        await fs.writeFile(REVIEWS_FILE, JSON.stringify([]));
    }
    
    // Initialize checklists file with default checklists
    try {
        await fs.access(CHECKLISTS_FILE);
    } catch {
        const defaultChecklists = await getDefaultChecklists();
        await fs.writeFile(CHECKLISTS_FILE, JSON.stringify(defaultChecklists, null, 2));
    }
    
    // Initialize users file
    try {
        await fs.access(USERS_FILE);
    } catch {
        await fs.writeFile(USERS_FILE, JSON.stringify([]));
    }
}

// Helper functions for data management
async function loadReviews() {
    try {
        const data = await fs.readFile(REVIEWS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return [];
    }
}

async function saveReview(review) {
    const reviews = await loadReviews();
    reviews.unshift(review);
    await fs.writeFile(REVIEWS_FILE, JSON.stringify(reviews, null, 2));
}

async function loadChecklists() {
    try {
        const data = await fs.readFile(CHECKLISTS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return await getDefaultChecklists();
    }
}

async function saveChecklists(checklists) {
    await fs.writeFile(CHECKLISTS_FILE, JSON.stringify(checklists, null, 2));
}

async function loadUsers() {
    try {
        const data = await fs.readFile(USERS_FILE, 'utf8');
        return JSON.parse(data);
    } catch {
        return [];
    }
}

async function saveUser(user) {
    const users = await loadUsers();
    const existingIndex = users.findIndex(u => u.id === user.id);
    if (existingIndex >= 0) {
        users[existingIndex] = user;
    } else {
        users.push(user);
    }
    await fs.writeFile(USERS_FILE, JSON.stringify(users, null, 2));
}

// Routes

// Home page - Enhanced Dashboard
app.get('/', async (req, res) => {
    try {
        const user = req.session.user || null;
        const reviews = user ? (await loadReviews()).filter(r => r.userId === user.id) : [];
        const checklists = await loadChecklists();
        
        res.render('dashboard', {
            title: 'PR Review Dashboard',
            user,
            reviews: reviews.slice(0, 5), // Show last 5 reviews
            totalReviews: reviews.length,
            checklists: Object.keys(checklists).length,
            githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET)
        });
    } catch (error) {
        console.error('Dashboard error:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load dashboard',
            error: error.message
        });
    }
});

// Login page
app.get('/login', (req, res) => {
    res.render('login', {
        title: 'Login - PR Review Tool',
        githubConfigured: !!(GITHUB_CLIENT_ID && GITHUB_CLIENT_SECRET),
        error: req.query.error
    });
});

// Analysis page
app.get('/analyze', async (req, res) => {
    try {
        const user = req.session.user || null;
        const checklists = await loadChecklists();
        
        res.render('analyze', {
            title: 'Analyze PR',
            user,
            checklists,
            type: req.query.type || 'public' // public, private, manual
        });
    } catch (error) {
        console.error('Analysis page error:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load analysis page',
            error: error.message
        });
    }
});

// Results page
app.get('/results/:reviewId?', async (req, res) => {
    try {
        const user = req.session.user || null;
        const reviewId = req.params.reviewId;
        let review = null;
        
        if (reviewId) {
            const reviews = await loadReviews();
            review = reviews.find(r => r.id === reviewId);
            
            // Check access permissions
            if (review && user && review.userId !== user.id) {
                return res.status(403).render('error', {
                    title: 'Access Denied',
                    message: 'You do not have permission to view this review'
                });
            }
        }
        
        res.render('results', {
            title: review ? `Results: PR #${review.prNumber}` : 'PR Analysis Results',
            user,
            review,
            reviewId
        });
    } catch (error) {
        console.error('Results page error:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load results',
            error: error.message
        });
    }
});

// Checklist management page
app.get('/checklists', async (req, res) => {
    try {
        const user = req.session.user || null;
        const checklists = await loadChecklists();
        
        res.render('checklists', {
            title: 'Manage Checklists',
            user,
            checklists
        });
    } catch (error) {
        console.error('Checklists page error:', error);
        res.status(500).render('error', {
            title: 'Error',
            message: 'Failed to load checklists',
            error: error.message
        });
    }
});

// Initialize application
async function startApplication() {
    await initializeDataFiles();
    
    app.listen(PORT, () => {
        console.log(`🚀 Enhanced PR Review Tool v2.0 running on http://localhost:${PORT}`);
        console.log(`📊 Features: Enhanced Dashboard, Line-by-Line Analysis, Custom Checklists`);
        console.log(`🔐 GitHub OAuth: ${GITHUB_CLIENT_ID ? 'Configured' : 'Not configured'}`);
    });
}

// API Routes

// GitHub OAuth routes
app.get('/auth/github', (req, res) => {
    if (!GITHUB_CLIENT_ID) {
        return res.redirect('/login?error=github_not_configured');
    }

    const state = crypto.randomBytes(16).toString('hex');
    req.session.oauthState = state;

    const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${GITHUB_CLIENT_ID}&scope=repo&state=${state}`;
    res.redirect(githubAuthUrl);
});

app.get('/auth/github/callback', async (req, res) => {
    const { code, state } = req.query;

    if (!code || state !== req.session.oauthState) {
        return res.redirect('/login?error=invalid_oauth_state');
    }

    try {
        // Exchange code for access token
        const tokenResponse = await axios.post('https://github.com/login/oauth/access_token', {
            client_id: GITHUB_CLIENT_ID,
            client_secret: GITHUB_CLIENT_SECRET,
            code
        }, {
            headers: { Accept: 'application/json' }
        });

        const accessToken = tokenResponse.data.access_token;

        // Get user info
        const userResponse = await axios.get('https://api.github.com/user', {
            headers: { Authorization: `token ${accessToken}` }
        });

        const user = {
            id: userResponse.data.id,
            login: userResponse.data.login,
            name: userResponse.data.name,
            email: userResponse.data.email,
            avatar_url: userResponse.data.avatar_url,
            accessToken,
            loginTime: new Date().toISOString()
        };

        req.session.user = user;
        await saveUser(user);

        res.redirect('/?login=success');
    } catch (error) {
        console.error('OAuth callback error:', error);
        res.redirect('/login?error=oauth_failed');
    }
});

// Logout
app.post('/auth/logout', (req, res) => {
    req.session.destroy();
    res.redirect('/');
});

// PR Analysis API
app.post('/api/analyze', async (req, res) => {
    try {
        const { repoUrl, prNumber, analysisType, checklistId } = req.body;
        const user = req.session.user;

        // Validate input
        if (!repoUrl || !prNumber) {
            return res.status(400).json({
                success: false,
                error: 'Repository URL and PR number are required'
            });
        }

        // Parse repository info
        const repoMatch = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
        if (!repoMatch) {
            return res.status(400).json({
                success: false,
                error: 'Invalid GitHub repository URL'
            });
        }

        const [, owner, repo] = repoMatch;

        // Perform analysis
        const analysis = await performPRAnalysis(owner, repo, prNumber, analysisType, checklistId, user);

        // Save review
        const review = {
            id: crypto.randomBytes(16).toString('hex'),
            userId: user?.id || null,
            repoUrl,
            prNumber: parseInt(prNumber),
            analysisType,
            checklistId,
            analysis,
            createdAt: new Date().toISOString()
        };

        await saveReview(review);

        res.json({
            success: true,
            reviewId: review.id,
            analysis
        });

    } catch (error) {
        console.error('Analysis error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Repository management API
app.get('/api/repositories', async (req, res) => {
    try {
        if (!req.session.user) {
            return res.status(401).json({
                success: false,
                error: 'Authentication required'
            });
        }

        const user = req.session.user;
        const headers = {
            'Authorization': `token ${user.accessToken}`,
            'User-Agent': 'PR-Review-Tool'
        };

        // Fetch user's repositories
        const response = await axios.get('https://api.github.com/user/repos', {
            headers,
            params: {
                sort: 'updated',
                per_page: 100,
                type: 'all' // all, owner, public, private, member
            }
        });

        const repositories = response.data.map(repo => ({
            id: repo.id,
            name: repo.name,
            full_name: repo.full_name,
            description: repo.description,
            private: repo.private,
            html_url: repo.html_url,
            updated_at: repo.updated_at,
            language: repo.language,
            stargazers_count: repo.stargazers_count,
            open_issues_count: repo.open_issues_count
        }));

        res.json({
            success: true,
            repositories,
            total: repositories.length
        });

    } catch (error) {
        console.error('Error fetching repositories:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch repositories'
        });
    }
});

// Checklist management API
app.get('/api/checklists', async (req, res) => {
    try {
        const checklists = await loadChecklists();
        res.json({ success: true, checklists });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Get individual checklist
app.get('/api/checklists/:id', async (req, res) => {
    try {
        const checklists = await loadChecklists();
        const checklist = checklists[req.params.id];

        if (!checklist) {
            return res.status(404).json({
                success: false,
                error: 'Checklist not found'
            });
        }

        res.json({
            success: true,
            checklist
        });
    } catch (error) {
        console.error('Error fetching checklist:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch checklist'
        });
    }
});

app.post('/api/checklists', async (req, res) => {
    try {
        const { name, description, rules, checks } = req.body;
        const checklists = await loadChecklists();

        if (!name || (!rules && !checks)) {
            return res.status(400).json({
                success: false,
                error: 'Name and rules are required'
            });
        }

        const checklistId = crypto.randomBytes(8).toString('hex');
        checklists[checklistId] = {
            id: checklistId,
            name,
            description: description || '',
            rules: rules || undefined, // New unified structure
            checks: checks || undefined, // Legacy structure for backward compatibility
            createdAt: new Date().toISOString(),
            createdBy: req.session.user?.id || 'anonymous'
        };

        await saveChecklists(checklists);
        res.json({ success: true, checklistId });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.put('/api/checklists/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description, rules, checks } = req.body;
        const checklists = await loadChecklists();

        if (!checklists[id]) {
            return res.status(404).json({ success: false, error: 'Checklist not found' });
        }

        if (!name || (!rules && !checks)) {
            return res.status(400).json({
                success: false,
                error: 'Name and rules are required'
            });
        }

        checklists[id] = {
            ...checklists[id],
            name,
            description: description || '',
            rules: rules || undefined, // New unified structure
            checks: checks || undefined, // Legacy structure for backward compatibility
            updatedAt: new Date().toISOString()
        };

        await saveChecklists(checklists);
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.delete('/api/checklists/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const checklists = await loadChecklists();

        if (!checklists[id]) {
            return res.status(404).json({ success: false, error: 'Checklist not found' });
        }

        delete checklists[id];
        await saveChecklists(checklists);
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Get review data API
app.get('/api/reviews/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const reviews = await loadReviews();
        const review = reviews.find(r => r.id === id);

        if (!review) {
            return res.status(404).json({ success: false, error: 'Review not found' });
        }

        // Check access permissions
        const user = req.session.user;
        if (review.userId && (!user || review.userId !== user.id)) {
            return res.status(403).json({ success: false, error: 'Access denied' });
        }

        res.json({ success: true, review });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Start the application
startApplication().catch(console.error);

module.exports = app;
