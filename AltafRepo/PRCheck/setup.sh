#!/bin/bash

# PR Review Assistant Setup Script
# This script helps you set up the Docker-based PR review application

set -e

echo "🚀 PR Review Assistant Setup"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    print_success "Docker is installed"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    print_success "Docker Compose is installed"
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_success "Docker is running"
}

# Check Ollama setup
check_ollama() {
    print_info "Checking Ollama setup..."
    
    # Check if Ollama container is running
    if docker ps | grep -q ollama; then
        print_success "Ollama container is running"
        
        # Check if required model is available
        if docker exec ollama ollama list | grep -q "GandalfBaum/llama3.2-claude3.7"; then
            print_success "Required AI model is available"
        else
            print_warning "Required AI model not found"
            read -p "Would you like to install GandalfBaum/llama3.2-claude3.7:latest? (y/N): " install_model
            if [[ $install_model =~ ^[Yy]$ ]]; then
                print_info "Installing AI model (this may take a while)..."
                docker exec ollama ollama pull GandalfBaum/llama3.2-claude3.7:latest
                print_success "AI model installed"
            fi
        fi
    else
        print_warning "Ollama container not found"
        read -p "Would you like to start Ollama container? (y/N): " start_ollama
        if [[ $start_ollama =~ ^[Yy]$ ]]; then
            print_info "Starting Ollama container..."
            docker run -d --name ollama -p 11434:11434 ollama/ollama
            sleep 5
            print_info "Installing AI model..."
            docker exec ollama ollama pull GandalfBaum/llama3.2-claude3.7:latest
            print_success "Ollama setup complete"
        fi
    fi
}

# Setup environment file
setup_environment() {
    print_info "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Created .env file from template"
    else
        print_warning ".env file already exists"
    fi
    
    # Check if GitHub token is set
    if grep -q "your_github_personal_access_token_here" .env; then
        print_warning "GitHub token not configured"
        echo ""
        echo "Please follow these steps to configure your GitHub token:"
        echo "1. Go to https://github.com/settings/tokens"
        echo "2. Click 'Generate new token (classic)'"
        echo "3. Select scopes: 'repo', 'read:user'"
        echo "4. Copy the generated token"
        echo ""
        read -p "Enter your GitHub Personal Access Token: " github_token
        if [ ! -z "$github_token" ]; then
            sed -i.bak "s/your_github_personal_access_token_here/$github_token/" .env
            print_success "GitHub token configured"
        fi
    else
        print_success "GitHub token appears to be configured"
    fi
    
    # Generate session secret if needed
    if grep -q "your_super_secret_session_key_here" .env; then
        session_secret=$(openssl rand -base64 32 2>/dev/null || date +%s | sha256sum | base64 | head -c 32)
        sed -i.bak "s/your_super_secret_session_key_here/$session_secret/" .env
        print_success "Generated session secret"
    fi
}

# Build and start services
start_services() {
    print_info "Building and starting services..."
    
    # Create necessary directories
    mkdir -p data logs
    
    # Build and start services
    docker-compose up -d --build
    
    print_success "Services started"
    
    # Wait for services to be ready
    print_info "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are healthy
    if curl -f http://localhost:3000/health &> /dev/null; then
        print_success "Application is healthy"
    else
        print_warning "Application may still be starting up"
    fi
}

# Display final information
show_completion_info() {
    echo ""
    echo "🎉 Setup Complete!"
    echo "=================="
    echo ""
    echo "Your PR Review Assistant is now running:"
    echo ""
    echo "📱 Web Interface:    http://localhost:3000"
    echo "📊 Dashboard:        http://localhost:3000/dashboard"
    echo "🔧 API Health:       http://localhost:3000/health"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Open http://localhost:3000 in your browser"
    echo "2. Test the GitHub integration on the dashboard"
    echo "3. Analyze a PR to verify everything is working"
    echo ""
    echo "🛠️  Useful Commands:"
    echo "• View logs:         docker-compose logs -f"
    echo "• Stop services:     docker-compose down"
    echo "• Restart services:  docker-compose restart"
    echo "• Update services:   docker-compose pull && docker-compose up -d"
    echo ""
    echo "📚 Documentation: See README.md for detailed usage instructions"
    echo ""
}

# Main setup flow
main() {
    echo ""
    check_prerequisites
    echo ""
    check_ollama
    echo ""
    setup_environment
    echo ""
    start_services
    echo ""
    show_completion_info
}

# Handle script interruption
trap 'echo -e "\n${RED}Setup interrupted${NC}"; exit 1' INT

# Run main function
main

# Cleanup backup files
rm -f .env.bak 2>/dev/null || true
