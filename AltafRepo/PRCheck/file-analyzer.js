// Comprehensive File Analysis with Line-by-Line Review
async function analyzeFile(file, checklist) {
    const analysis = {
        filename: file.filename,
        status: file.status, // added, modified, removed
        additions: file.additions,
        deletions: file.deletions,
        changes: file.changes,
        totalLines: 0,
        linesWithIssues: 0,
        lineComments: [],
        issues: {
            critical: [],
            major: [],
            minor: [],
            suggestions: []
        },
        codeQuality: {
            complexity: 'low',
            maintainability: 'good',
            security: 'safe'
        },
        recommendations: []
    };
    
    // Skip analysis for deleted files
    if (file.status === 'removed') {
        return analysis;
    }
    
    // Analyze patch if available
    if (file.patch) {
        await analyzePatch(file.patch, file.filename, analysis);
    }
    
    // File-specific analysis based on extension
    const fileExtension = getFileExtension(file.filename);
    await analyzeByFileType(fileExtension, file, analysis);
    
    // Generate file-specific recommendations
    analysis.recommendations = generateFileRecommendations(analysis, fileExtension);
    
    return analysis;
}

// Analyze patch for line-by-line review
async function analyzePatch(patch, filename, analysis) {
    const lines = patch.split('\n');
    let currentLine = 0;
    let inHunk = false;
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        // Parse hunk headers to track line numbers
        if (line.startsWith('@@')) {
            const match = line.match(/@@ -\d+,?\d* \+(\d+),?\d* @@/);
            if (match) {
                currentLine = parseInt(match[1]) - 1;
                inHunk = true;
            }
            continue;
        }
        
        if (!inHunk) continue;
        
        // Analyze added lines (starting with +)
        if (line.startsWith('+') && !line.startsWith('+++')) {
            currentLine++;
            analysis.totalLines++;
            
            const codeContent = line.substring(1);
            const lineAnalysis = await analyzeCodeLine(codeContent, currentLine, filename);
            
            if (lineAnalysis.hasIssues) {
                analysis.linesWithIssues++;
                
                // Add line comments
                analysis.lineComments.push(...lineAnalysis.comments.map(comment => ({
                    ...comment,
                    line: currentLine,
                    file: filename,
                    code: codeContent.trim()
                })));
                
                // Categorize issues
                lineAnalysis.issues.forEach(issue => {
                    const issueWithContext = {
                        ...issue,
                        line: currentLine,
                        file: filename,
                        code: codeContent.trim()
                    };
                    
                    switch (issue.severity) {
                        case 'critical':
                            analysis.issues.critical.push(issueWithContext);
                            break;
                        case 'major':
                            analysis.issues.major.push(issueWithContext);
                            break;
                        case 'minor':
                            analysis.issues.minor.push(issueWithContext);
                            break;
                        default:
                            analysis.issues.suggestions.push(issueWithContext);
                    }
                });
            }
        } else if (line.startsWith(' ')) {
            currentLine++;
        }
    }
}

// Analyze individual code line
async function analyzeCodeLine(code, lineNumber, filename) {
    const result = {
        hasIssues: false,
        comments: [],
        issues: []
    };
    
    const trimmedCode = code.trim();
    
    // Skip empty lines and comments
    if (!trimmedCode || isComment(trimmedCode, filename)) {
        return result;
    }
    
    // Security analysis
    const securityIssues = analyzeLineSecurity(trimmedCode);
    if (securityIssues.length > 0) {
        result.hasIssues = true;
        result.issues.push(...securityIssues);
    }
    
    // Code quality analysis
    const qualityIssues = analyzeLineQuality(trimmedCode, filename);
    if (qualityIssues.length > 0) {
        result.hasIssues = true;
        result.issues.push(...qualityIssues);
    }
    
    // Best practices analysis
    const practiceIssues = analyzeBestPractices(trimmedCode, filename);
    if (practiceIssues.length > 0) {
        result.hasIssues = true;
        result.issues.push(...practiceIssues);
    }
    
    // Generate review comments
    const reviewComments = generateLineReviewComments(trimmedCode, filename);
    if (reviewComments.length > 0) {
        result.hasIssues = true;
        result.comments.push(...reviewComments);
    }
    
    return result;
}

// Security analysis for individual line
function analyzeLineSecurity(code) {
    const issues = [];
    
    const securityPatterns = [
        {
            pattern: /(?:password|pwd|pass)\s*[=:]\s*["'][^"']+["']/i,
            message: "Hardcoded password detected",
            severity: "critical",
            suggestion: "Use environment variables or secure configuration for passwords"
        },
        {
            pattern: /(?:api[_-]?key|apikey|access[_-]?key)\s*[=:]\s*["'][^"']+["']/i,
            message: "Hardcoded API key detected",
            severity: "critical",
            suggestion: "Store API keys in environment variables or secure vault"
        },
        {
            pattern: /(?:secret|token)\s*[=:]\s*["'][^"']+["']/i,
            message: "Hardcoded secret detected",
            severity: "critical",
            suggestion: "Use secure configuration management for secrets"
        },
        {
            pattern: /eval\s*\(/i,
            message: "Use of eval() detected",
            severity: "major",
            suggestion: "Avoid eval() as it can execute arbitrary code and poses security risks"
        },
        {
            pattern: /innerHTML\s*=/i,
            message: "Direct innerHTML assignment detected",
            severity: "major",
            suggestion: "Use textContent or sanitize HTML to prevent XSS attacks"
        },
        {
            pattern: /document\.write\s*\(/i,
            message: "Use of document.write detected",
            severity: "minor",
            suggestion: "Use modern DOM manipulation methods instead of document.write"
        }
    ];
    
    securityPatterns.forEach(pattern => {
        if (pattern.pattern.test(code)) {
            issues.push({
                type: 'security',
                severity: pattern.severity,
                message: pattern.message,
                suggestion: pattern.suggestion
            });
        }
    });
    
    return issues;
}

// Code quality analysis for individual line
function analyzeLineQuality(code, filename) {
    const issues = [];
    
    const qualityPatterns = [
        {
            pattern: /console\.log\s*\(/i,
            message: "Console.log statement found",
            severity: "minor",
            suggestion: "Remove console.log statements before production deployment"
        },
        {
            pattern: /debugger\s*;?/i,
            message: "Debugger statement found",
            severity: "major",
            suggestion: "Remove debugger statements before committing code"
        },
        {
            pattern: /TODO|FIXME|HACK|XXX/i,
            message: "TODO/FIXME comment found",
            severity: "minor",
            suggestion: "Address TODO items or create proper issue tracking"
        },
        {
            pattern: /var\s+\w+/i,
            message: "Use of 'var' detected",
            severity: "minor",
            suggestion: "Consider using 'let' or 'const' instead of 'var' for better scoping"
        },
        {
            pattern: /==\s*[^=]/i,
            message: "Loose equality operator detected",
            severity: "minor",
            suggestion: "Use strict equality (===) instead of loose equality (==)"
        }
    ];
    
    qualityPatterns.forEach(pattern => {
        if (pattern.pattern.test(code)) {
            issues.push({
                type: 'quality',
                severity: pattern.severity,
                message: pattern.message,
                suggestion: pattern.suggestion
            });
        }
    });
    
    // Line length check
    if (code.length > 120) {
        issues.push({
            type: 'formatting',
            severity: 'minor',
            message: `Line too long (${code.length} characters)`,
            suggestion: "Break long lines for better readability (recommended max: 120 characters)"
        });
    }
    
    // Complexity check
    const complexity = calculateLineComplexity(code);
    if (complexity > 10) {
        issues.push({
            type: 'complexity',
            severity: 'major',
            message: `High complexity detected (score: ${complexity})`,
            suggestion: "Consider breaking complex logic into smaller, more manageable functions"
        });
    }
    
    return issues;
}

// Best practices analysis
function analyzeBestPractices(code, filename) {
    const issues = [];
    const fileExt = getFileExtension(filename);
    
    // Language-specific best practices
    switch (fileExt) {
        case 'js':
        case 'ts':
            issues.push(...analyzeJavaScriptBestPractices(code));
            break;
        case 'py':
            issues.push(...analyzePythonBestPractices(code));
            break;
        case 'java':
            issues.push(...analyzeJavaBestPractices(code));
            break;
    }
    
    return issues;
}

// Generate review comments for line
function generateLineReviewComments(code, filename) {
    const comments = [];
    const fileExt = getFileExtension(filename);
    
    // Function/method detection
    if (isFunctionDeclaration(code, fileExt)) {
        comments.push({
            type: 'documentation',
            message: "Consider adding documentation for this function",
            suggestion: "Add JSDoc comments or docstrings to explain the function's purpose, parameters, and return value"
        });
    }
    
    // Class detection
    if (isClassDeclaration(code, fileExt)) {
        comments.push({
            type: 'design',
            message: "Ensure class follows single responsibility principle",
            suggestion: "Verify that this class has a single, well-defined purpose"
        });
    }
    
    // Async/await detection
    if (code.includes('async') || code.includes('await') || code.includes('Promise')) {
        comments.push({
            type: 'error_handling',
            message: "Ensure proper error handling for async operations",
            suggestion: "Add try-catch blocks or proper error handling for asynchronous code"
        });
    }
    
    return comments;
}

// Helper functions
function getFileExtension(filename) {
    return filename.split('.').pop().toLowerCase();
}

function isComment(code, filename) {
    const fileExt = getFileExtension(filename);
    const commentPatterns = {
        'js': /^\s*(\/\/|\/\*|\*)/,
        'ts': /^\s*(\/\/|\/\*|\*)/,
        'py': /^\s*#/,
        'java': /^\s*(\/\/|\/\*|\*)/,
        'css': /^\s*(\/\*|\*)/,
        'html': /^\s*<!--/
    };
    
    const pattern = commentPatterns[fileExt];
    return pattern ? pattern.test(code) : false;
}

function calculateLineComplexity(code) {
    let complexity = 1;
    const complexityPatterns = [
        /if\s*\(/g, /else/g, /for\s*\(/g, /while\s*\(/g, /switch\s*\(/g,
        /case\s+/g, /catch\s*\(/g, /&&/g, /\|\|/g, /\?/g
    ];
    
    complexityPatterns.forEach(pattern => {
        const matches = code.match(pattern);
        if (matches) {
            complexity += matches.length;
        }
    });
    
    return complexity;
}

function isFunctionDeclaration(code, fileExt) {
    const functionPatterns = {
        'js': /function\s+\w+|const\s+\w+\s*=\s*\(|=>\s*{/,
        'ts': /function\s+\w+|const\s+\w+\s*=\s*\(|=>\s*{/,
        'py': /def\s+\w+/,
        'java': /(public|private|protected)?\s*(static)?\s*\w+\s+\w+\s*\(/
    };
    
    const pattern = functionPatterns[fileExt];
    return pattern ? pattern.test(code) : false;
}

function isClassDeclaration(code, fileExt) {
    const classPatterns = {
        'js': /class\s+\w+/,
        'ts': /class\s+\w+/,
        'py': /class\s+\w+/,
        'java': /(public|private|protected)?\s*class\s+\w+/
    };
    
    const pattern = classPatterns[fileExt];
    return pattern ? pattern.test(code) : false;
}

// Language-specific best practice analyzers
function analyzeJavaScriptBestPractices(code) {
    const issues = [];
    
    if (code.includes('document.getElementById') && !code.includes('null')) {
        issues.push({
            type: 'best_practice',
            severity: 'minor',
            message: "Consider null checking for DOM elements",
            suggestion: "Check if element exists before using it to prevent runtime errors"
        });
    }
    
    return issues;
}

function analyzePythonBestPractices(code) {
    const issues = [];
    
    if (code.includes('import *')) {
        issues.push({
            type: 'best_practice',
            severity: 'minor',
            message: "Wildcard import detected",
            suggestion: "Use specific imports instead of wildcard imports for better code clarity"
        });
    }
    
    return issues;
}

function analyzeJavaBestPractices(code) {
    const issues = [];
    
    if (code.includes('System.out.print')) {
        issues.push({
            type: 'best_practice',
            severity: 'minor',
            message: "System.out.print detected",
            suggestion: "Use proper logging framework instead of System.out.print"
        });
    }
    
    return issues;
}

// File-specific analysis
async function analyzeByFileType(fileExtension, file, analysis) {
    // Update code quality based on file type and content
    if (['js', 'ts', 'jsx', 'tsx'].includes(fileExtension)) {
        analysis.codeQuality.complexity = analysis.totalLines > 100 ? 'high' : 'medium';
    }
    
    if (analysis.issues.critical.length > 0) {
        analysis.codeQuality.security = 'vulnerable';
    } else if (analysis.issues.major.length > 0) {
        analysis.codeQuality.security = 'needs_review';
    }
    
    if (analysis.issues.major.length > 5 || analysis.issues.minor.length > 10) {
        analysis.codeQuality.maintainability = 'poor';
    } else if (analysis.issues.minor.length > 5) {
        analysis.codeQuality.maintainability = 'fair';
    }
}

// Generate file-specific recommendations
function generateFileRecommendations(analysis, fileExtension) {
    const recommendations = [];
    
    if (analysis.issues.critical.length > 0) {
        recommendations.push("Address critical security issues immediately before merging");
    }
    
    if (analysis.issues.major.length > 0) {
        recommendations.push("Review and fix major issues to improve code quality");
    }
    
    if (analysis.totalLines > 500) {
        recommendations.push("Consider breaking large files into smaller, more manageable modules");
    }
    
    const fileTypeRecommendations = {
        'js': ['Add JSDoc comments', 'Consider using TypeScript for better type safety'],
        'py': ['Follow PEP 8 style guidelines', 'Add type hints for better documentation'],
        'java': ['Use proper exception handling', 'Follow Java naming conventions'],
        'css': ['Use CSS custom properties for theming', 'Consider using a CSS preprocessor']
    };
    
    if (fileTypeRecommendations[fileExtension]) {
        recommendations.push(...fileTypeRecommendations[fileExtension]);
    }
    
    return recommendations;
}

module.exports = analyzeFile;
