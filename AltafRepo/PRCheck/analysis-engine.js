const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

// Enhanced PR Analysis Engine with Line-by-Line Review
async function performPRAnalysis(owner, repo, prNumber, analysisType, checklistId, user) {
    try {
        console.log(`Starting analysis for ${owner}/${repo} PR #${prNumber}`);
        
        // Setup GitHub API headers
        const headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'PR-Review-Tool'
        };
        
        // Add authentication for private repos
        if (analysisType === 'private' && user?.accessToken) {
            headers['Authorization'] = `token ${user.accessToken}`;
        }
        
        // Fetch PR data
        const prData = await fetchPRData(owner, repo, prNumber, headers);
        
        // Fetch PR files and diffs
        const prFiles = await fetchPRFiles(owner, repo, prNumber, headers);
        
        // Fetch commits
        const prCommits = await fetchPRCommits(owner, repo, prNumber, headers);
        
        // Load checklist
        const checklist = await loadChecklist(checklistId);
        
        // Perform comprehensive analysis
        const analysis = await analyzeWithChecklist(prData, prFiles, prCommits, checklist);
        
        // Add metadata
        analysis.metadata = {
            repository: `${owner}/${repo}`,
            prNumber,
            analysisType,
            checklistId,
            analyzedAt: new Date().toISOString(),
            analyzedBy: user?.login || 'anonymous'
        };
        
        console.log(`Analysis completed: ${analysis.summary.score}/${analysis.summary.totalChecks} checks passed`);
        
        return analysis;
        
    } catch (error) {
        console.error('Analysis error:', error);

        // Return a basic error analysis instead of throwing
        return {
            summary: {
                score: 0,
                totalChecks: 0,
                percentage: 0,
                status: 'error'
            },
            categories: {},
            fileAnalysis: {
                totalFiles: 0,
                filesAnalyzed: 0,
                files: []
            },
            lineByLineReview: {
                totalLines: 0,
                linesWithIssues: 0,
                comments: []
            },
            issues: {
                critical: [],
                major: [],
                minor: [],
                suggestions: []
            },
            recommendations: [`Analysis failed: ${error.message}`],
            metadata: {
                repository: `${owner}/${repo}`,
                prNumber,
                analysisType,
                checklistId,
                analyzedAt: new Date().toISOString(),
                analyzedBy: user?.login || 'anonymous',
                error: error.message
            }
        };
    }
}

// Fetch PR data from GitHub API
async function fetchPRData(owner, repo, prNumber, headers) {
    try {
        const response = await axios.get(
            `https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}`,
            { headers }
        );

        const prData = response.data;

        // Ensure required properties exist with defaults
        return {
            id: prData.id || 0,
            number: prData.number || prNumber,
            title: prData.title || 'No title',
            body: prData.body || '',
            state: prData.state || 'unknown',
            head: prData.head || { ref: 'unknown', sha: '' },
            base: prData.base || { ref: 'main', sha: '' },
            user: prData.user || { login: 'unknown' },
            created_at: prData.created_at || new Date().toISOString(),
            updated_at: prData.updated_at || new Date().toISOString(),
            additions: prData.additions || 0,
            deletions: prData.deletions || 0,
            changed_files: prData.changed_files || 0,
            commits: prData.commits || 0,
            html_url: prData.html_url || '',
            diff_url: prData.diff_url || '',
            patch_url: prData.patch_url || ''
        };
    } catch (error) {
        console.error(`Error fetching PR data for ${owner}/${repo}#${prNumber}:`, error.message);
        throw new Error(`Failed to fetch PR data: ${error.response?.status === 404 ? 'PR not found' : error.message}`);
    }
}

// Fetch PR files and diffs
async function fetchPRFiles(owner, repo, prNumber, headers) {
    try {
        const response = await axios.get(
            `https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}/files`,
            { headers }
        );
        return response.data || [];
    } catch (error) {
        console.error(`Error fetching PR files for ${owner}/${repo}#${prNumber}:`, error.message);
        return []; // Return empty array if files can't be fetched
    }
}

// Fetch PR commits
async function fetchPRCommits(owner, repo, prNumber, headers) {
    try {
        const response = await axios.get(
            `https://api.github.com/repos/${owner}/${repo}/pulls/${prNumber}/commits`,
            { headers }
        );
        return response.data || [];
    } catch (error) {
        console.error(`Error fetching PR commits for ${owner}/${repo}#${prNumber}:`, error.message);
        return []; // Return empty array if commits can't be fetched
    }
}

// Load checklist configuration
async function loadChecklist(checklistId) {
    try {
        const checklistsPath = path.join(__dirname, 'data', 'checklists.json');
        const data = await fs.readFile(checklistsPath, 'utf8');
        const checklists = JSON.parse(data);
        
        if (checklistId && checklists[checklistId]) {
            return checklists[checklistId];
        }
        
        // Return default checklist
        return checklists['default'] || await getDefaultChecklist();
    } catch (error) {
        console.error('Error loading checklist:', error);
        return await getDefaultChecklist();
    }
}

// Comprehensive analysis with checklist
async function analyzeWithChecklist(prData, prFiles, prCommits, checklist) {
    const analysis = {
        summary: {
            score: 0,
            totalChecks: 0,
            percentage: 0,
            status: 'pending'
        },
        categories: {},
        fileAnalysis: {
            totalFiles: prFiles.length,
            filesAnalyzed: 0,
            files: []
        },
        lineByLineReview: {
            totalLines: 0,
            linesWithIssues: 0,
            comments: []
        },
        issues: {
            critical: [],
            major: [],
            minor: [],
            suggestions: []
        },
        recommendations: []
    };
    
    // Analyze against checklist
    for (const category of checklist.checks || []) {
        const categoryResult = await analyzeCategory(category, prData, prFiles, prCommits);
        analysis.categories[category.id] = categoryResult;
        
        analysis.summary.totalChecks += categoryResult.totalChecks;
        analysis.summary.score += categoryResult.passedChecks;
    }
    
    // Perform file-by-file analysis
    const analyzeFile = require('./file-analyzer');
    for (const file of prFiles) {
        const fileAnalysis = await analyzeFile(file, checklist);
        analysis.fileAnalysis.files.push(fileAnalysis);
        analysis.fileAnalysis.filesAnalyzed++;

        // Aggregate line-by-line comments
        analysis.lineByLineReview.comments.push(...fileAnalysis.lineComments);
        analysis.lineByLineReview.totalLines += fileAnalysis.totalLines;
        analysis.lineByLineReview.linesWithIssues += fileAnalysis.linesWithIssues;

        // Aggregate issues
        analysis.issues.critical.push(...fileAnalysis.issues.critical);
        analysis.issues.major.push(...fileAnalysis.issues.major);
        analysis.issues.minor.push(...fileAnalysis.issues.minor);
        analysis.issues.suggestions.push(...fileAnalysis.issues.suggestions);
    }
    
    // Calculate final percentage
    analysis.summary.percentage = analysis.summary.totalChecks > 0 
        ? Math.round((analysis.summary.score / analysis.summary.totalChecks) * 100)
        : 0;
    
    // Determine status
    if (analysis.summary.percentage >= 90) {
        analysis.summary.status = 'excellent';
    } else if (analysis.summary.percentage >= 75) {
        analysis.summary.status = 'good';
    } else if (analysis.summary.percentage >= 60) {
        analysis.summary.status = 'needs_improvement';
    } else {
        analysis.summary.status = 'poor';
    }
    
    // Generate recommendations
    analysis.recommendations = generateRecommendations(analysis);
    
    return analysis;
}

// Analyze category against PR data
async function analyzeCategory(category, prData, prFiles, prCommits) {
    const result = {
        id: category.id,
        name: category.name,
        description: category.description,
        totalChecks: category.rules?.length || 0,
        passedChecks: 0,
        checks: []
    };
    
    for (const rule of category.rules || []) {
        const checkResult = await evaluateRule(rule, prData, prFiles, prCommits);
        result.checks.push(checkResult);
        
        if (checkResult.passed) {
            result.passedChecks++;
        }
    }
    
    return result;
}

// Evaluate individual rule
async function evaluateRule(rule, prData, prFiles, prCommits) {
    const result = {
        id: rule.id,
        name: rule.name,
        description: rule.description,
        passed: false,
        message: '',
        severity: rule.severity || 'medium',
        details: {}
    };
    
    try {
        // Validate prData exists
        if (!prData) {
            throw new Error('PR data is not available');
        }

        switch (rule.type) {
            case 'title_check':
                result.passed = evalTitleRule(rule, prData.title || '');
                break;
            case 'description_check':
                result.passed = evalDescriptionRule(rule, prData.body || '');
                break;
            case 'branch_check':
                result.passed = evalBranchRule(rule, prData.head || {}, prData.base || {});
                break;
            case 'size_check':
                result.passed = evalSizeRule(rule, prData, prFiles || []);
                break;
            case 'commit_check':
                result.passed = evalCommitRule(rule, prCommits || []);
                break;
            default:
                result.passed = false;
                result.message = `Unknown rule type: ${rule.type}`;
        }

        result.message = result.passed ? (rule.passMessage || 'Check passed') : (rule.failMessage || 'Check failed');

    } catch (error) {
        result.passed = false;
        result.message = `Error evaluating rule: ${error.message}`;
    }
    
    return result;
}

// Import rule evaluation functions
const {
    evaluateTitleRule: evalTitleRule,
    evaluateDescriptionRule: evalDescriptionRule,
    evaluateBranchRule: evalBranchRule,
    evaluateSizeRule: evalSizeRule,
    evaluateCommitRule: evalCommitRule
} = require('./default-checklists');

// Generate recommendations based on analysis
function generateRecommendations(analysis) {
    const recommendations = [];

    // Critical issues
    if (analysis.issues.critical.length > 0) {
        recommendations.push("🚨 Address critical security issues immediately before merging");
    }

    // Major issues
    if (analysis.issues.major.length > 0) {
        recommendations.push("⚠️ Review and fix major code quality issues");
    }

    // File count
    if (analysis.fileAnalysis.totalFiles > 10) {
        recommendations.push("📁 Consider breaking large changes into smaller, focused PRs");
    }

    // Line count
    if (analysis.lineByLineReview.totalLines > 500) {
        recommendations.push("📏 Large PR detected - ensure adequate testing and review time");
    }

    // Security
    if (analysis.issues.critical.some(issue => issue.type === 'security')) {
        recommendations.push("🔒 Security vulnerabilities found - conduct security review");
    }

    // Code quality
    if (analysis.issues.minor.length > 10) {
        recommendations.push("🧹 Multiple minor issues found - consider code cleanup");
    }

    // Success case
    if (analysis.summary.percentage >= 90) {
        recommendations.push("✅ Excellent PR quality - ready for merge after final review");
    }

    return recommendations;
}

module.exports = {
    performPRAnalysis,
    analyzeWithChecklist,
    analyzeFile: require('./file-analyzer'),
    getDefaultChecklist: require('./default-checklists').getDefaultChecklist,
    generateRecommendations
};
