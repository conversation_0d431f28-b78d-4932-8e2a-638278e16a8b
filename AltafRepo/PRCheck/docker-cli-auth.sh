#!/bin/bash

# Docker CLI Authentication Script for PR Review Tool
# This script allows you to run CLI authentication from within the Docker container

echo "🐳 Docker CLI Authentication for PR Review Tool"
echo "==============================================="

# Check if Docker container is running
if ! docker ps | grep -q "prcheck-pr-review-v2-1"; then
    echo "❌ PR Review Tool container is not running"
    echo "🚀 Starting the container..."
    docker compose -f docker-compose.v2.yml up -d
    echo "⏳ Waiting for container to be ready..."
    sleep 5
fi

echo "✅ Container is running"

# Check if token is provided as argument
if [ "$1" = "--token" ] && [ -n "$2" ]; then
    echo "🔑 Using provided token..."
    docker exec -it prcheck-pr-review-v2-1 node github-cli-auth.js --token "$2"
elif [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "📚 Showing help..."
    docker exec -it prcheck-pr-review-v2-1 node github-cli-auth.js --help
else
    echo "🔑 Starting interactive authentication..."
    echo "💡 You'll be prompted to enter your GitHub Personal Access Token"
    echo ""
    docker exec -it prcheck-pr-review-v2-1 node github-cli-auth.js
fi

echo ""
echo "🌐 Web interface available at: http://localhost:3000"
echo "📚 Documentation: http://localhost:3000/checklists"
