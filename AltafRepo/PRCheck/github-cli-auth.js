#!/usr/bin/env node

/**
 * GitHub CLI Authentication for PR Review Tool
 * 
 * This script provides CLI-friendly GitHub authentication using Personal Access Tokens.
 * No client secret or callback URLs required!
 * 
 * Usage:
 *   node github-cli-auth.js
 *   node github-cli-auth.js --token YOUR_TOKEN
 *   node github-cli-auth.js --help
 */

const axios = require('axios');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000';
const TOKEN_FILE = path.join(__dirname, '.github-token');

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Utility function to prompt user
function prompt(question) {
    return new Promise((resolve) => {
        rl.question(question, resolve);
    });
}

// Save token securely (for demo purposes - in production, use proper keychain)
function saveToken(token) {
    try {
        fs.writeFileSync(TOKEN_FILE, token, { mode: 0o600 });
        console.log('✅ Token saved securely');
    } catch (error) {
        console.warn('⚠️  Could not save token:', error.message);
    }
}

// Load saved token
function loadSavedToken() {
    try {
        if (fs.existsSync(TOKEN_FILE)) {
            return fs.readFileSync(TOKEN_FILE, 'utf8').trim();
        }
    } catch (error) {
        console.warn('⚠️  Could not load saved token:', error.message);
    }
    return null;
}

// Authenticate with GitHub Personal Access Token
async function authenticateWithToken(token) {
    try {
        console.log('🔍 Validating GitHub token...');

        const response = await axios.post(`${BASE_URL}/auth/github/token`, {
            token: token.trim()
        });

        if (response.data.success) {
            console.log('🎉 Authentication successful!');
            console.log(`👤 Welcome, ${response.data.user.login}!`);
            console.log(`📧 Email: ${response.data.user.email || 'Not provided'}`);
            console.log(`🆔 GitHub ID: ${response.data.user.id}`);
            
            // Save token for future use
            saveToken(token.trim());
            
            return response.data.user;
        } else {
            throw new Error(response.data.error);
        }

    } catch (error) {
        console.error('❌ Authentication failed:', error.response?.data?.error || error.message);
        
        if (error.response?.status === 401) {
            console.log('\n💡 Token troubleshooting:');
            console.log('1. Make sure your token has "repo" and "user:email" scopes');
            console.log('2. Check that the token is not expired');
            console.log('3. Verify the token is copied correctly (no extra spaces)');
            console.log('4. Create a new token at: https://github.com/settings/tokens');
        }
        
        return null;
    }
}

// Interactive token input
async function getTokenInteractively() {
    console.log('\n🔑 GitHub Personal Access Token Authentication');
    console.log('==============================================');
    
    console.log('\n📋 To create a Personal Access Token:');
    console.log('1. Go to: https://github.com/settings/tokens');
    console.log('2. Click "Generate new token (classic)"');
    console.log('3. Select scopes: "repo" and "user:email"');
    console.log('4. Copy the generated token\n');
    
    const token = await prompt('Enter your GitHub Personal Access Token: ');
    
    if (!token.trim()) {
        console.log('❌ Token is required');
        return null;
    }
    
    return token.trim();
}

// Demo PR Analysis
async function demoAnalysis() {
    console.log('\n🔬 Demo: Analyzing a Pull Request');
    console.log('==================================');
    
    const useDemo = await prompt('Use demo repository? (y/n): ');
    
    let repoUrl, prNumber;
    
    if (useDemo.toLowerCase() === 'y' || useDemo.toLowerCase() === 'yes') {
        repoUrl = 'https://github.com/saltaf07/assistila-web';
        prNumber = '1';
        console.log(`📁 Using demo: ${repoUrl} PR #${prNumber}`);
    } else {
        repoUrl = await prompt('Enter repository URL: ');
        prNumber = await prompt('Enter PR number: ');
    }
    
    try {
        console.log(`\n🔍 Analyzing ${repoUrl} PR #${prNumber}...`);
        
        const analysisResponse = await axios.post(`${BASE_URL}/api/analyze`, {
            repoUrl: repoUrl.trim(),
            prNumber: prNumber.trim(),
            analysisType: 'private',
            checklistId: 'default'
        });
        
        if (analysisResponse.data.success) {
            console.log('✅ Analysis completed successfully!');
            console.log(`📊 Review ID: ${analysisResponse.data.reviewId}`);
            console.log(`🌐 View results: ${BASE_URL}/results/${analysisResponse.data.reviewId}`);
            
            // Open results in browser
            const openBrowser = await prompt('\nOpen results in browser? (y/n): ');
            if (openBrowser.toLowerCase() === 'y' || openBrowser.toLowerCase() === 'yes') {
                const { exec } = require('child_process');
                const url = `${BASE_URL}/results/${analysisResponse.data.reviewId}`;
                
                // Cross-platform browser opening
                const command = process.platform === 'darwin' ? 'open' : 
                               process.platform === 'win32' ? 'start' : 'xdg-open';
                
                exec(`${command} "${url}"`, (error) => {
                    if (error) {
                        console.log(`🌐 Please open: ${url}`);
                    } else {
                        console.log('🌐 Opening results in browser...');
                    }
                });
            }
        } else {
            throw new Error(analysisResponse.data.error);
        }
        
    } catch (error) {
        console.error('❌ Analysis failed:', error.response?.data?.error || error.message);
    }
}

// Show help
function showHelp() {
    console.log(`
🚀 GitHub CLI Authentication for PR Review Tool

Usage:
  node github-cli-auth.js                    # Interactive mode
  node github-cli-auth.js --token TOKEN      # Direct token authentication
  node github-cli-auth.js --help             # Show this help

Features:
  ✅ No client secret required
  ✅ No callback URLs needed
  ✅ Works with Docker
  ✅ Secure token storage
  ✅ Interactive PR analysis

Examples:
  # Interactive authentication
  node github-cli-auth.js

  # Direct token authentication
  node github-cli-auth.js --token ghp_your_token_here

  # Using saved token
  node github-cli-auth.js --saved

Requirements:
  - GitHub Personal Access Token with 'repo' and 'user:email' scopes
  - PR Review Tool running on ${BASE_URL}

Create token at: https://github.com/settings/tokens
`);
}

// Main CLI function
async function main() {
    const args = process.argv.slice(2);
    
    // Handle command line arguments
    if (args.includes('--help') || args.includes('-h')) {
        showHelp();
        rl.close();
        return;
    }
    
    console.log('🚀 GitHub CLI Authentication for PR Review Tool');
    console.log('===============================================\n');
    
    let token = null;
    let user = null;
    
    // Check for direct token argument
    const tokenIndex = args.indexOf('--token');
    if (tokenIndex !== -1 && args[tokenIndex + 1]) {
        token = args[tokenIndex + 1];
        console.log('🔑 Using provided token...');
        user = await authenticateWithToken(token);
    }
    // Check for saved token option
    else if (args.includes('--saved')) {
        token = loadSavedToken();
        if (token) {
            console.log('🔑 Using saved token...');
            user = await authenticateWithToken(token);
        } else {
            console.log('❌ No saved token found');
        }
    }
    // Interactive mode
    else {
        // Check for saved token first
        const savedToken = loadSavedToken();
        if (savedToken) {
            const useSaved = await prompt('🔑 Found saved token. Use it? (y/n): ');
            if (useSaved.toLowerCase() === 'y' || useSaved.toLowerCase() === 'yes') {
                token = savedToken;
                user = await authenticateWithToken(token);
            }
        }
        
        // If no saved token or user declined, get new token
        if (!user) {
            token = await getTokenInteractively();
            if (token) {
                user = await authenticateWithToken(token);
            }
        }
    }
    
    if (user) {
        console.log('\n🎯 What would you like to do?');
        console.log('1. Run demo PR analysis');
        console.log('2. Open web interface');
        console.log('3. Exit\n');
        
        const choice = await prompt('Enter your choice (1-3): ');
        
        switch (choice.trim()) {
            case '1':
                await demoAnalysis();
                break;
            case '2':
                console.log(`🌐 Opening web interface: ${BASE_URL}`);
                const { exec } = require('child_process');
                const command = process.platform === 'darwin' ? 'open' : 
                               process.platform === 'win32' ? 'start' : 'xdg-open';
                exec(`${command} "${BASE_URL}"`, (error) => {
                    if (error) {
                        console.log(`🌐 Please open: ${BASE_URL}`);
                    }
                });
                break;
            case '3':
                console.log('👋 Goodbye!');
                break;
            default:
                console.log('❌ Invalid choice');
        }
        
        console.log('\n🎉 CLI authentication completed!');
        console.log(`🌐 Web interface: ${BASE_URL}`);
        console.log(`📚 Documentation: ${BASE_URL}/checklists`);
    } else {
        console.log('\n❌ Authentication failed. Please try again.');
    }
    
    rl.close();
}

// Handle errors and cleanup
process.on('SIGINT', () => {
    console.log('\n\n👋 Goodbye!');
    rl.close();
    process.exit(0);
});

// Run the CLI
if (require.main === module) {
    main().catch(error => {
        console.error('💥 Unexpected error:', error.message);
        rl.close();
        process.exit(1);
    });
}

module.exports = { authenticateWithToken, main };
