# Perplexity AI MCP Server Setup for Cline VS Code Extension

## 🚨 SECURITY ALERT
**Your API key was shared publicly and is compromised. Please regenerate it immediately:**
1. Go to [Perplexity AI Settings](https://www.perplexity.ai/settings/api)
2. Revoke the old key: `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`
3. Generate a new API key

## 📋 What's Been Configured

I've set up a dedicated Perplexity AI MCP server alongside your existing configuration:

### MCP Servers Configured:
1. **task-master-ai** - Multi-provider AI server (includes Perplexity support)
2. **context7** - Context management server  
3. **perplexity-ai** - Dedicated Perplexity AI server ✨ *NEW*

### Files Created/Modified:
- `.roo/mcp.json` - Updated with Perplexity AI server configuration
- `.env` - Environment variables file for API keys
- `setup-perplexity-mcp.sh` - Automated setup script
- `PERPLEXITY_MCP_SETUP.md` - This documentation

## 🚀 Quick Setup

### Option 1: Automated Setup
```bash
./setup-perplexity-mcp.sh
```

### Option 2: Manual Setup

1. **Install the Perplexity MCP server:**
   ```bash
   npm install -g @perplexity-ai/mcp-server
   ```

2. **Update your `.env` file with your NEW API key:**
   ```bash
   PERPLEXITY_API_KEY=your_new_perplexity_api_key_here
   ```

3. **Restart VS Code** to load the new MCP server

## 📁 Configuration Details

### MCP Configuration (`.roo/mcp.json`)
```json
{
  "mcpServers": {
    "perplexity-ai": {
      "command": "npx",
      "args": [
        "-y",
        "@perplexity-ai/mcp-server"
      ],
      "env": {
        "PERPLEXITY_API_KEY": "YOUR_NEW_PERPLEXITY_API_KEY_HERE"
      }
    }
  }
}
```

### Environment Variables (`.env`)
```bash
PERPLEXITY_API_KEY=your_new_perplexity_api_key_here
```

## 🧪 Testing the Setup

1. **Restart VS Code** completely
2. **Open Cline extension**
3. **Check MCP servers** are loaded in the Cline interface
4. **Test Perplexity features** by asking research questions

## 🔧 Troubleshooting

### MCP Server Not Loading
- Ensure Node.js and npm are installed
- Check that the package is installed globally: `npm list -g @perplexity-ai/mcp-server`
- Verify your API key is valid and not expired

### API Key Issues
- Make sure you've regenerated the compromised key
- Verify the key is correctly set in `.env`
- Check there are no extra spaces or quotes around the key

### VS Code/Cline Issues
- Completely restart VS Code (not just reload window)
- Check VS Code developer console for MCP errors
- Ensure Cline extension is up to date

## 📚 Usage Examples

Once configured, you can use Perplexity AI through Cline for:
- **Research queries**: "Search for the latest information about..."
- **Real-time data**: "What's the current status of..."
- **Technical documentation**: "Find recent updates about..."
- **Market analysis**: "Get current information on..."

## 🔒 Security Best Practices

1. **Never commit `.env` to version control** (already in `.gitignore`)
2. **Regenerate API keys if compromised**
3. **Use environment variables for all sensitive data**
4. **Regularly rotate API keys**
5. **Monitor API usage for unusual activity**

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Check VS Code developer console for errors
4. Ensure API keys are valid and properly formatted

## 🎯 Next Steps

1. **Regenerate your Perplexity API key** (most important!)
2. **Run the setup script** or follow manual steps
3. **Test the integration** with Cline
4. **Explore Perplexity AI features** in your development workflow

---

**Remember: Your old API key is compromised and must be regenerated immediately!**
