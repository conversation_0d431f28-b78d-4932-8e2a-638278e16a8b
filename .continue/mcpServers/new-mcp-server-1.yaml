name: taskmaster-ai-mcp
version: "1.0.0"
mcpServers:
  - name: taskmaster-ai
    command: npx
    args:
      - -y
      - --package=task-master-ai
      - task-master-ai
    env:
      ANTHROPIC_API_KEY: "YOUR_ANTHROPIC_API_KEY_HERE"
      PERPLEXITY_API_KEY: "YOUR_PERPLEXITY_API_KEY_HERE"
      OPENAI_API_KEY: "YOUR_OPENAI_KEY_HERE"
      GOOGLE_API_KEY: "AIzaSyBeySlH3SMxKFrDaDiZMq1qRjXFGJRS5TY"
      MISTRAL_API_KEY: "YOUR_MISTRAL_KEY_HERE"
      OPENROUTER_API_KEY: "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"
      XAI_API_KEY: "YOUR_XAI_KEY_HERE"
      AZURE_OPENAI_API_KEY: "YOUR_AZURE_KEY_HERE"
      OLLAMA_API_KEY: "YOUR_OLLAMA_API_KEY_HERE"
