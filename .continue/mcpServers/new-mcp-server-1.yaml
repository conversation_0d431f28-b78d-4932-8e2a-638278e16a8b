name: New MCP server
version: 0.0.1
schema: v1
mcpServers:
  - name: New MCP server
    command: npx
    args:
      - -y
      - <your-mcp-server>
    env: {}
  - name: taskmaster-ai
    command: npx
    args:
      - -y
      - --package=task-master-ai
      - task-master-ai
    env:
      Gemini_API_KEY: "AIzaSyBeySlH3SMxKFrDaDiZMq1qRjXFGJRS5TY"
      PERPLEXITY_API_KEY: "YOUR_PERPLEXITY_API_KEY_HERE"
      MODEL: "claude-3-7-sonnet-20250219"
      PERPLEXITY_MODEL: "sonar-pro"
      MAX_TOKENS: 64000
      TEMPERATURE: 0.2
      DEFAULT_SUBTASKS: 5
      DEFAULT_PRIORITY: "medium"